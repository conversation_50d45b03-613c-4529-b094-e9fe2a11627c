#!/usr/bin/env node

/**
 * Fleet Management System - Install Missing Dependencies
 * Installs commonly missing dependencies
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, description) {
  log(`🔧 ${description}...`, 'blue')
  try {
    execSync(command, { stdio: 'inherit' })
    log(`   ✅ ${description} completed`, 'green')
    return true
  } catch (error) {
    log(`   ❌ ${description} failed`, 'red')
    return false
  }
}

function checkPackageExists(packageName) {
  const packagePath = path.join(process.cwd(), 'node_modules', packageName)
  return fs.existsSync(packagePath)
}

async function installMissingDependencies() {
  log('📦 Fleet Management System - Installing Missing Dependencies', 'bold')
  log('=' .repeat(60), 'blue')

  // List of commonly missing packages
  const requiredPackages = [
    '@supabase/supabase-js',
    'dotenv',
    'next',
    'react',
    'typescript'
  ]

  const missingPackages = []

  // Check which packages are missing
  log('\n🔍 Checking for missing packages...', 'blue')
  for (const pkg of requiredPackages) {
    if (checkPackageExists(pkg)) {
      log(`   ✅ ${pkg} - installed`, 'green')
    } else {
      log(`   ❌ ${pkg} - missing`, 'red')
      missingPackages.push(pkg)
    }
  }

  if (missingPackages.length === 0) {
    log('\n🎉 All required packages are installed!', 'green')
    return true
  }

  // Install missing packages
  log(`\n📦 Installing ${missingPackages.length} missing packages...`, 'yellow')
  
  for (const pkg of missingPackages) {
    const success = runCommand(`npm install ${pkg}`, `Installing ${pkg}`)
    if (!success) {
      log(`\n⚠️  Failed to install ${pkg}. Trying alternative methods...`, 'yellow')
      
      // Try with --force
      runCommand(`npm install ${pkg} --force`, `Installing ${pkg} with --force`)
    }
  }

  // Verify installation
  log('\n🔍 Verifying installation...', 'blue')
  let allInstalled = true
  
  for (const pkg of missingPackages) {
    if (checkPackageExists(pkg)) {
      log(`   ✅ ${pkg} - now installed`, 'green')
    } else {
      log(`   ❌ ${pkg} - still missing`, 'red')
      allInstalled = false
    }
  }

  if (allInstalled) {
    log('\n🎉 All missing packages have been installed!', 'green')
  } else {
    log('\n⚠️  Some packages are still missing. Manual intervention may be required.', 'yellow')
  }

  return allInstalled
}

// Quick install for specific packages
async function quickInstall() {
  log('⚡ Quick Install - Essential Packages', 'bold')
  log('=' .repeat(40), 'blue')

  const essentialPackages = [
    '@supabase/supabase-js',
    'dotenv'
  ]

  for (const pkg of essentialPackages) {
    if (!checkPackageExists(pkg)) {
      runCommand(`npm install ${pkg}`, `Installing ${pkg}`)
    } else {
      log(`✅ ${pkg} already installed`, 'green')
    }
  }

  log('\n🎯 Essential packages installation complete!', 'green')
}

// Main execution
async function main() {
  const command = process.argv[2]

  switch (command) {
    case 'all':
      await installMissingDependencies()
      break
    
    case 'quick':
      await quickInstall()
      break
    
    case 'dotenv':
      runCommand('npm install dotenv', 'Installing dotenv')
      break
    
    case 'supabase':
      runCommand('npm install @supabase/supabase-js', 'Installing @supabase/supabase-js')
      break
    
    default:
      log('Fleet Management System - Missing Dependencies Installer', 'bold')
      log('')
      log('Usage: node install-missing.js <command>')
      log('')
      log('Commands:')
      log('  all       - Check and install all missing dependencies')
      log('  quick     - Install essential packages only')
      log('  dotenv    - Install dotenv package')
      log('  supabase  - Install @supabase/supabase-js package')
      log('')
      log('Examples:')
      log('  node scripts/install-missing.js quick')
      log('  node scripts/install-missing.js all')
      log('')
      
      // Auto-run quick install
      log('Auto-running quick install...', 'yellow')
      await quickInstall()
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`❌ Installation failed: ${error.message}`, 'red')
    process.exit(1)
  })
}

module.exports = { installMissingDependencies, quickInstall }
