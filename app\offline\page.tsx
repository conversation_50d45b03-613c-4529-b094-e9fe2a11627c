"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RefreshCw, Wifi, WifiOff, Signal, SignalHigh, SignalLow, AlertCircle } from "lucide-react"

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true)
  const [retryCount, setRetryCount] = useState(0)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)

  useEffect(() => {
    // Check online status
    const handleOnline = () => {
      setIsOnline(true)
      setLastSyncTime(new Date())
    }
    
    const handleOffline = () => {
      setIsOnline(false)
    }

    setIsOnline(navigator.onLine)
    
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    window.location.reload()
  }

  const goToDashboard = () => {
    window.location.href = '/dashboard'
  }

  const NetworkStatusIcon = () => {
    if (isOnline) {
      return <SignalHigh className="h-12 w-12 text-green-500" />
    } else {
      return <WifiOff className="h-12 w-12 text-red-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Main offline card */}
        <Card className="shadow-xl border-0">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <NetworkStatusIcon />
            </div>
            <CardTitle className="text-2xl font-bold text-gray-800">
              {isOnline ? 'Connection Restored' : 'You\'re Offline'}
            </CardTitle>
            <CardDescription className="text-gray-600">
              {isOnline 
                ? 'Your internet connection has been restored.' 
                : 'Please check your internet connection and try again.'
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent className="text-center space-y-4">
            <div className="flex justify-center space-x-2">
              <Badge variant={isOnline ? "default" : "destructive"}>
                {isOnline ? 'Online' : 'Offline'}
              </Badge>
              {retryCount > 0 && (
                <Badge variant="outline">
                  Retry #{retryCount}
                </Badge>
              )}
            </div>
            
            {lastSyncTime && (
              <p className="text-sm text-gray-500">
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </p>
            )}
            
            <div className="flex space-x-2">
              <Button 
                onClick={handleRetry}
                className="flex-1"
                disabled={isOnline}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              
              <Button 
                onClick={goToDashboard}
                variant="outline"
                className="flex-1"
              >
                Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Offline features card */}
        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-blue-500" />
              Offline Features
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Cached Data</span>
                <Badge variant="outline" className="text-xs">
                  Available
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">View Vehicles</span>
                <Badge variant="outline" className="text-xs">
                  Limited
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">View Drivers</span>
                <Badge variant="outline" className="text-xs">
                  Limited
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Add New Records</span>
                <Badge variant="destructive" className="text-xs">
                  Unavailable
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Reports</span>
                <Badge variant="destructive" className="text-xs">
                  Unavailable
                </Badge>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Note:</strong> Some features may be limited when offline. 
                Your changes will be synced when you reconnect.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Tips card */}
        <Card className="shadow-xl border-0">
          <CardHeader>
            <CardTitle className="text-lg">Tips for Better Offline Experience</CardTitle>
          </CardHeader>
          
          <CardContent>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2"></span>
                <span>Enable notifications to stay updated</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2"></span>
                <span>Use cached data to view existing records</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2"></span>
                <span>Changes will sync automatically when online</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-2"></span>
                <span>Check your network settings if issues persist</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}