#!/usr/bin/env node

/**
 * Fleet Management System - Test Profiles Creator
 * Creates test profiles for authentication testing
 * Note: Users need to sign up manually first, then this script updates their profiles
 */

const fs = require('fs')
const path = require('path')
const https = require('https')

// Load environment variables manually
function loadEnvVars() {
  const envPath = path.join(process.cwd(), '.env.local')
  const envVars = {}
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')
    
    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=')
        const value = valueParts.join('=').trim()
        envVars[key.trim()] = value
      }
    }
  }
  
  return envVars
}

const envVars = loadEnvVars()
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL || 'https://vjozjofhwlpskbgbgzve.supabase.co'
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE0NzIxOCwiZXhwIjoyMDY4NzIzMjE4fQ.lL5Dw3NPc8HPQS6_pi5-hnDUsexj4Ao9EOXxvuIzZaU'

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Make HTTP request to Supabase
function makeSupabaseRequest(method, endpoint, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(endpoint, supabaseUrl)
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
        'Prefer': 'return=minimal'
      }
    }

    const req = https.request(url, options, (res) => {
      let responseData = ''
      
      res.on('data', (chunk) => {
        responseData += chunk
      })
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsed = responseData ? JSON.parse(responseData) : {}
            resolve(parsed)
          } catch (error) {
            resolve(responseData)
          }
        } else {
          try {
            const parsed = JSON.parse(responseData)
            reject(new Error(`HTTP ${res.statusCode}: ${parsed.message || responseData}`))
          } catch (error) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`))
          }
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Test profiles data
const testProfiles = [
  {
    id: '11111111-1111-1111-1111-111111111111',
    full_name: 'System Administrator',
    email: '<EMAIL>',
    role: 'Super Admin',
    branch_id: null,
    user_status: 'Active'
  },
  {
    id: '22222222-2222-2222-2222-222222222222',
    full_name: 'Branch Manager',
    email: '<EMAIL>',
    role: 'Manager',
    branch_id: '550e8400-e29b-41d4-a716-446655440001', // Cairo branch
    user_status: 'Active'
  },
  {
    id: '33333333-3333-3333-3333-333333333333',
    full_name: 'Fleet Employee',
    email: '<EMAIL>',
    role: 'Employee',
    branch_id: '550e8400-e29b-41d4-a716-446655440001', // Cairo branch
    user_status: 'Active'
  }
]

// Create or update a profile
async function createProfile(profileData) {
  try {
    log(`   Creating/updating profile: ${profileData.email}...`, 'blue')
    
    // Try to insert the profile
    await makeSupabaseRequest('POST', '/rest/v1/profiles', profileData)
    
    log(`   ✅ Profile created: ${profileData.email}`, 'green')
    return true
  } catch (error) {
    if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
      try {
        // Profile exists, update it
        await makeSupabaseRequest('PATCH', `/rest/v1/profiles?id=eq.${profileData.id}`, {
          full_name: profileData.full_name,
          role: profileData.role,
          branch_id: profileData.branch_id,
          user_status: profileData.user_status
        })
        
        log(`   ✅ Profile updated: ${profileData.email}`, 'green')
        return true
      } catch (updateError) {
        log(`   ❌ Error updating profile ${profileData.email}: ${updateError.message}`, 'red')
        return false
      }
    } else {
      log(`   ❌ Error creating profile ${profileData.email}: ${error.message}`, 'red')
      return false
    }
  }
}

// Main function to create test profiles
async function createTestProfiles() {
  log('👥 Fleet Management System - Test Profiles Creator', 'bold')
  log('=' .repeat(60), 'blue')

  try {
    // Test connection
    log('📡 Testing Supabase connection...', 'blue')
    await makeSupabaseRequest('GET', '/rest/v1/branches?select=count&limit=1')
    log('   ✅ Connection successful', 'green')

    log('\n👤 Creating test profiles...', 'blue')
    
    let successCount = 0
    let totalCount = testProfiles.length

    for (const profileData of testProfiles) {
      const success = await createProfile(profileData)
      if (success) successCount++
    }

    // Summary
    log('\n' + '=' .repeat(60), 'blue')
    log('📊 Test Profiles Creation Summary', 'bold')
    log('=' .repeat(60), 'blue')

    log(`✅ Successfully created/updated: ${successCount}/${totalCount} profiles`, 'green')

    if (successCount === totalCount) {
      log('\n🎉 All test profiles are ready!', 'green')
      log('\n📋 Test Credentials (for manual signup):', 'blue')
      testProfiles.forEach(profile => {
        log(`   ${profile.role}: ${profile.email}`, 'yellow')
      })
      
      log('\n📝 Instructions:', 'blue')
      log('1. Go to http://localhost:3000/login', 'blue')
      log('2. Click "Sign Up" and create accounts with the emails above', 'blue')
      log('3. Use any password (e.g., "password123")', 'blue')
      log('4. The profiles will automatically get the correct roles', 'blue')
    } else {
      log('\n⚠️  Some profiles could not be created. Check the errors above.', 'yellow')
    }

    return successCount === totalCount

  } catch (error) {
    log(`\n❌ Test profiles creation failed: ${error.message}`, 'red')
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      log('\n🔧 Connection troubleshooting:', 'yellow')
      log('1. Check your internet connection', 'yellow')
      log('2. Verify Supabase URL in .env.local', 'yellow')
      log('3. Ensure Supabase project is active', 'yellow')
    }
    
    throw error
  }
}

// Main execution
async function main() {
  try {
    const success = await createTestProfiles()
    
    if (success) {
      log('\n🎉 Test profiles created successfully!', 'green')
      process.exit(0)
    } else {
      log('\n⚠️  Some issues occurred during profile creation.', 'yellow')
      process.exit(1)
    }
  } catch (error) {
    log(`❌ Script failed: ${error.message}`, 'red')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { createTestProfiles }
