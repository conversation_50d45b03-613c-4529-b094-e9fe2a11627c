"use client"

import { useState, useEffect } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { MaintenanceForm } from "@/components/forms/maintenance-form"
import { useAsyncOperation } from "@/hooks/use-async-operation"
import { supabaseApiClient, type MaintenanceRecord } from "@/lib/supabase-api-client"
import { Plus, Search, Filter, Edit, Trash2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock } from "lucide-react"

export default function MaintenancePage() {
  const [maintenance, setMaintenance] = useState<MaintenanceRecord[]>([])
  const [filteredMaintenance, setFilteredMaintenance] = useState<MaintenanceRecord[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingMaintenance, setEditingMaintenance] = useState<MaintenanceRecord | null>(null)
  const [deletingMaintenance, setDeletingMaintenance] = useState<MaintenanceRecord | null>(null)

  const { execute: loadMaintenance, isLoading: loadingMaintenance } = useAsyncOperation({
    onSuccess: (data) => setMaintenance(data || []),
  })

  const { execute: deleteMaintenance, isLoading: deletingMaintenanceLoading } = useAsyncOperation({
    successMessage: "Maintenance record deleted successfully",
    onSuccess: () => {
      setDeletingMaintenance(null)
      loadMaintenanceData()
    },
  })

  const loadMaintenanceData = () => {
    loadMaintenance(() => supabaseApiClient.getMaintenance()).catch(() => {
      /* error already toasted by useAsyncOperation */
    })
  }

  useEffect(() => {
    loadMaintenanceData()
  }, [])

  useEffect(() => {
    let filtered = maintenance

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (record) =>
          record.vehicle_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.maintenance_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.technician.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.maintenance_status.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((record) => record.maintenance_status === statusFilter)
    }

    setFilteredMaintenance(filtered)
  }, [maintenance, searchTerm, statusFilter])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200"
      case "in progress":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "scheduled":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const handleEdit = (record: MaintenanceRecord) => {
    setEditingMaintenance(record)
  }

  const handleDelete = (record: MaintenanceRecord) => {
    setDeletingMaintenance(record)
  }

  const confirmDelete = () => {
    if (deletingMaintenance) {
      deleteMaintenance(() => supabaseApiClient.deleteMaintenance(deletingMaintenance.maintenance_id))
    }
  }

  const handleFormSuccess = () => {
    setIsAddDialogOpen(false)
    setEditingMaintenance(null)
    loadMaintenanceData()
  }

  const completedMaintenance = maintenance.filter((m) => m.maintenance_status === "Completed").length
  const inProgressMaintenance = maintenance.filter((m) => m.maintenance_status === "In Progress").length
  const scheduledMaintenance = maintenance.filter((m) => m.maintenance_status === "Scheduled").length
  const overdueMaintenance = maintenance.filter((m) => {
    const scheduledDate = new Date(m.scheduled_date)
    const today = new Date()
    return scheduledDate < today && m.maintenance_status !== "Completed"
  }).length

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Maintenance Management</h1>
              <p className="text-gray-600 mt-2">Track and manage vehicle maintenance schedules</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Schedule Maintenance
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Maintenance</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Wrench className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{maintenance.length}</div>
                <p className="text-xs text-gray-600 mt-1">All records</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Completed</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{completedMaintenance}</div>
                <p className="text-xs text-gray-600 mt-1">Finished work</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">In Progress</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-secondary">
                  <Clock className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{inProgressMaintenance}</div>
                <p className="text-xs text-gray-600 mt-1">Currently active</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Overdue</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-danger">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{overdueMaintenance}</div>
                <p className="text-xs text-gray-600 mt-1">Needs attention</p>
              </CardContent>
            </Card>
          </div>

          {/* Maintenance Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">Maintenance Records</CardTitle>
              <CardDescription className="text-gray-600">Track all vehicle maintenance activities</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search maintenance records..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Status</option>
                    <option value="Scheduled">Scheduled</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                    <option value="Cancelled">Cancelled</option>
                  </select>
                  <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingMaintenance ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading maintenance records...</span>
                </div>
              ) : (
                /* Maintenance Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Vehicle</TableHead>
                        <TableHead className="text-gray-700">Type</TableHead>
                        <TableHead className="text-gray-700">Status</TableHead>
                        <TableHead className="text-gray-700">Scheduled Date</TableHead>
                        <TableHead className="text-gray-700">Cost</TableHead>
                        <TableHead className="text-gray-700">Technician</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMaintenance.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                            {searchTerm || statusFilter !== "all"
                              ? "No maintenance records found matching your criteria"
                              : "No maintenance records added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredMaintenance.map((record) => (
                          <TableRow key={record.maintenance_id} className="border-gray-100 hover:bg-blue-50/30">
                            <TableCell className="font-medium text-gray-800">{record.vehicle_id}</TableCell>
                            <TableCell className="text-gray-700">{record.maintenance_type}</TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(record.maintenance_status)}>{record.maintenance_status}</Badge>
                            </TableCell>
                            <TableCell className="text-gray-700">{record.scheduled_date}</TableCell>
                            <TableCell className="text-gray-700">${record.cost}</TableCell>
                            <TableCell className="text-gray-700">{record.technician}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(record)}
                                  className="hover:bg-blue-100 hover:text-blue-700"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(record)}
                                  className="hover:bg-red-100 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add Maintenance Dialog */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Schedule New Maintenance</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Create a new maintenance record for a vehicle.
                </DialogDescription>
              </DialogHeader>
              <MaintenanceForm onSuccess={handleFormSuccess} onCancel={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          {/* Edit Maintenance Dialog */}
          <Dialog open={!!editingMaintenance} onOpenChange={() => setEditingMaintenance(null)}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Edit Maintenance Record</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Update the maintenance information below.
                </DialogDescription>
              </DialogHeader>
              {editingMaintenance && (
                <MaintenanceForm
                  maintenance={editingMaintenance}
                  onSuccess={handleFormSuccess}
                  onCancel={() => setEditingMaintenance(null)}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <ConfirmationDialog
            open={!!deletingMaintenance}
            onOpenChange={() => setDeletingMaintenance(null)}
            title="Delete Maintenance Record"
            description={`Are you sure you want to delete this maintenance record? This action cannot be undone.`}
            confirmText="Delete Record"
            onConfirm={confirmDelete}
            variant="destructive"
            loading={deletingMaintenanceLoading}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
