// Service Worker for Fleet Management System
// Version 1.0.0

const CACHE_NAME = 'fleet-management-v1.0.0'
const API_CACHE_NAME = 'fleet-management-api-v1.0.0'

// Static assets to cache
const STATIC_CACHE_URLS = [
  '/',
  '/dashboard',
  '/vehicles',
  '/drivers',
  '/maintenance',
  '/fuel',
  '/reports',
  '/offline',
  '/manifest.json',
  // Add your static assets here
]

// API routes to cache
const API_CACHE_URLS = [
  '/api/sheets',
]

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Install event')
  
  event.waitUntil(
    Promise.all([
      caches.open(CACHE_NAME).then((cache) => {
        console.log('Service Worker: Caching static assets')
        return cache.addAll(STATIC_CACHE_URLS)
      }),
      caches.open(API_CACHE_NAME).then((cache) => {
        console.log('Service Worker: API cache opened')
        return cache
      })
    ])
  )
  
  // Force activation
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activate event')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== API_CACHE_NAME) {
            console.log('Service Worker: Deleting old cache', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  
  // Take control of all pages
  self.clients.claim()
})

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip external requests
  if (url.origin !== location.origin) {
    return
  }
  
  // API requests - network first with cache fallback
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request))
    return
  }
  
  // Static assets - cache first
  if (isStaticAsset(url.pathname)) {
    event.respondWith(handleStaticAsset(request))
    return
  }
  
  // Pages - network first with cache fallback
  event.respondWith(handlePageRequest(request))
})

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  const cache = await caches.open(API_CACHE_NAME)
  
  try {
    // Try network first
    const response = await fetch(request)
    
    if (response.ok) {
      // Cache successful responses
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('Service Worker: Network failed for API request, trying cache')
    
    // Fallback to cache
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      // Add offline indicator header
      const response = cachedResponse.clone()
      response.headers.set('X-Served-From-Cache', 'true')
      return response
    }
    
    // If no cache available, return offline response
    return new Response(
      JSON.stringify({
        success: false,
        error: 'No internet connection and no cached data available',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Handle static assets with cache-first strategy
async function handleStaticAsset(request) {
  const cache = await caches.open(CACHE_NAME)
  
  // Try cache first
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse) {
    return cachedResponse
  }
  
  try {
    // Fallback to network
    const response = await fetch(request)
    
    if (response.ok) {
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('Service Worker: Failed to fetch static asset', request.url)
    
    // Return a generic offline response for assets
    return new Response('Asset unavailable offline', {
      status: 503,
      headers: { 'Content-Type': 'text/plain' }
    })
  }
}

// Handle page requests with network-first strategy
async function handlePageRequest(request) {
  const cache = await caches.open(CACHE_NAME)
  
  try {
    // Try network first
    const response = await fetch(request)
    
    if (response.ok) {
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('Service Worker: Network failed for page request, trying cache')
    
    // Fallback to cache
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      return cachedResponse
    }
    
    // If no cache, return offline page
    const offlineResponse = await cache.match('/offline')
    
    if (offlineResponse) {
      return offlineResponse
    }
    
    // Final fallback
    return new Response(
      `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Offline - Fleet Management System</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: #f5f5f5;
          }
          .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
          }
          h1 {
            color: #333;
            margin-bottom: 1rem;
          }
          p {
            color: #666;
            margin-bottom: 1rem;
          }
          button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
          }
          button:hover {
            background: #1d4ed8;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="icon">📡</div>
          <h1>You're Offline</h1>
          <p>Please check your internet connection and try again.</p>
          <button onclick="window.location.reload()">Retry</button>
        </div>
      </body>
      </html>
      `,
      {
        status: 503,
        headers: { 'Content-Type': 'text/html' }
      }
    )
  }
}

// Check if URL is a static asset
function isStaticAsset(pathname) {
  return (
    pathname.startsWith('/_next/static/') ||
    pathname.startsWith('/static/') ||
    pathname.startsWith('/icons/') ||
    pathname.startsWith('/images/') ||
    pathname.endsWith('.css') ||
    pathname.endsWith('.js') ||
    pathname.endsWith('.png') ||
    pathname.endsWith('.jpg') ||
    pathname.endsWith('.jpeg') ||
    pathname.endsWith('.svg') ||
    pathname.endsWith('.ico') ||
    pathname.endsWith('.woff') ||
    pathname.endsWith('.woff2') ||
    pathname.endsWith('.ttf')
  )
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync event', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync())
  }
})

async function doBackgroundSync() {
  console.log('Service Worker: Performing background sync')
  
  try {
    // Get stored offline actions
    const offlineActions = await getOfflineActions()
    
    for (const action of offlineActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        })
        
        // Remove successful action
        await removeOfflineAction(action.id)
      } catch (error) {
        console.log('Service Worker: Failed to sync action', action.id, error)
      }
    }
  } catch (error) {
    console.log('Service Worker: Background sync failed', error)
  }
}

// Helper functions for offline actions
async function getOfflineActions() {
  const cache = await caches.open(API_CACHE_NAME)
  const response = await cache.match('/offline-actions')
  
  if (response) {
    return response.json()
  }
  
  return []
}

async function removeOfflineAction(actionId) {
  const actions = await getOfflineActions()
  const filteredActions = actions.filter(action => action.id !== actionId)
  
  const cache = await caches.open(API_CACHE_NAME)
  await cache.put('/offline-actions', new Response(JSON.stringify(filteredActions)))
}

// Push notifications
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push event', event)
  
  if (event.data) {
    const data = event.data.json()
    
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      vibrate: [100, 50, 100],
      data: data.data,
      actions: data.actions || []
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  }
})

// Notification click handler
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification click', event)
  
  event.notification.close()
  
  const clickedAction = event.action
  const notificationData = event.notification.data
  
  let url = '/'
  
  if (clickedAction) {
    url = notificationData.actionUrls[clickedAction] || '/'
  } else if (notificationData.url) {
    url = notificationData.url
  }
  
  event.waitUntil(
    clients.openWindow(url)
  )
})

// Error handling
self.addEventListener('error', (event) => {
  console.error('Service Worker: Error', event.error)
})

self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker: Unhandled rejection', event.reason)
})

// Periodic background sync (if supported)
self.addEventListener('periodicsync', (event) => {
  console.log('Service Worker: Periodic sync event', event.tag)
  
  if (event.tag === 'fleet-data-sync') {
    event.waitUntil(doPeriodicSync())
  }
})

async function doPeriodicSync() {
  console.log('Service Worker: Performing periodic sync')
  
  try {
    // Sync critical data
    const criticalEndpoints = [
      '/api/sheets?action=getVehicles',
      '/api/sheets?action=getDrivers',
    ]
    
    for (const endpoint of criticalEndpoints) {
      try {
        const response = await fetch(endpoint)
        if (response.ok) {
          const cache = await caches.open(API_CACHE_NAME)
          await cache.put(endpoint, response.clone())
        }
      } catch (error) {
        console.log('Service Worker: Failed to sync endpoint', endpoint, error)
      }
    }
  } catch (error) {
    console.log('Service Worker: Periodic sync failed', error)
  }
}

console.log('Service Worker: Loaded and ready')