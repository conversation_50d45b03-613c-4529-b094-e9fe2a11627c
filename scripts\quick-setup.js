#!/usr/bin/env node

/**
 * Fleet Management System - Quick Setup Script
 * Applies database schema and RLS policies quickly
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.log('Please check your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function executeSQL(sql, description) {
  try {
    console.log(`🔧 ${description}...`)
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    let successCount = 0
    let errorCount = 0

    for (const statement of statements) {
      if (statement.trim().length === 0) continue

      try {
        const { error } = await supabase.rpc('exec_sql', { 
          sql: statement + ';' 
        })

        if (error) {
          // Some errors are expected (like "already exists")
          if (error.message.includes('already exists') || 
              error.message.includes('duplicate key')) {
            console.log(`   ⚠️  ${error.message} (continuing...)`)
          } else {
            throw error
          }
        }
        successCount++
      } catch (error) {
        errorCount++
        console.error(`   ❌ Error: ${error.message}`)
      }
    }

    console.log(`   ✅ ${description} completed (${successCount} successful, ${errorCount} errors)`)
    return { success: successCount, errors: errorCount }
  } catch (error) {
    console.error(`❌ Failed to ${description.toLowerCase()}:`, error.message)
    throw error
  }
}

async function quickSetup() {
  console.log('🚀 Fleet Management System - Quick Setup')
  console.log('=' .repeat(50))
  console.log(`📡 Connecting to: ${supabaseUrl}`)
  console.log('')

  try {
    // Test connection
    console.log('🔍 Testing Supabase connection...')
    const { data, error } = await supabase.from('_supabase_migrations').select('*').limit(1)
    if (error && !error.message.includes('does not exist')) {
      throw new Error(`Connection failed: ${error.message}`)
    }
    console.log('   ✅ Connection successful')

    // Apply database schema
    const schemaPath = path.join(__dirname, '..', 'supabase', 'migrations', '001_initial_schema.sql')
    if (fs.existsSync(schemaPath)) {
      const schemaSQL = fs.readFileSync(schemaPath, 'utf8')
      await executeSQL(schemaSQL, 'Applying database schema')
    } else {
      console.log('⚠️  Schema file not found, skipping...')
    }

    // Apply RLS policies
    const rlsPath = path.join(__dirname, '..', 'supabase', 'migrations', '002_row_level_security.sql')
    if (fs.existsSync(rlsPath)) {
      const rlsSQL = fs.readFileSync(rlsPath, 'utf8')
      await executeSQL(rlsSQL, 'Applying RLS policies')
    } else {
      console.log('⚠️  RLS file not found, skipping...')
    }

    // Create test users
    console.log('👥 Creating test users...')
    await createTestUsers()

    // Apply seed data
    console.log('🌱 Applying sample data...')
    try {
      const { applySeedDataSimple } = require('./apply-seed-simple.js')
      await applySeedDataSimple()
    } catch (error) {
      console.log('⚠️  Seed data application failed, but setup can continue')
      console.log('   You can apply seed data later with: npm run seed')
    }

    console.log('')
    console.log('=' .repeat(50))
    console.log('✅ Quick setup completed successfully!')
    console.log('')
    console.log('🎯 Next steps:')
    console.log('1. Run: npm run dev')
    console.log('2. Open: http://localhost:3000')
    console.log('3. Login with test credentials:')
    console.log('   - <EMAIL> / FleetAdmin123!')
    console.log('   - <EMAIL> / FleetManager123!')
    console.log('   - <EMAIL> / FleetEmployee123!')
    console.log('')
    console.log('📊 Sample data includes:')
    console.log('   - 3 branches (Cairo, Alexandria, Gouna)')
    console.log('   - 5 drivers with valid licenses')
    console.log('   - 6 vehicles (LEVC, Toyota, Hyundai)')
    console.log('   - Maintenance and fuel records')
    console.log('')

  } catch (error) {
    console.error('❌ Quick setup failed:', error.message)
    process.exit(1)
  }
}

async function createTestUsers() {
  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'FleetAdmin123!',
      role: 'Super Admin',
      full_name: 'مدير النظام'
    },
    {
      email: '<EMAIL>', 
      password: 'FleetManager123!',
      role: 'Manager',
      full_name: 'مدير الفرع'
    },
    {
      email: '<EMAIL>',
      password: 'FleetEmployee123!', 
      role: 'Employee',
      full_name: 'موظف'
    }
  ]

  for (const user of testUsers) {
    try {
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: user.email,
        password: user.password,
        email_confirm: true
      })

      if (authError) {
        if (authError.message.includes('already registered')) {
          console.log(`   ⚠️  User ${user.email} already exists`)
          continue
        }
        throw authError
      }

      console.log(`   ✅ Created user: ${user.email}`)

    } catch (error) {
      console.error(`   ❌ Failed to create user ${user.email}:`, error.message)
    }
  }

  console.log('   📝 Note: User profiles need to be created manually with branch assignments')
}

// Alternative method using direct SQL execution
async function executeDirectSQL(sql) {
  try {
    // This is a workaround for environments where rpc might not work
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({ sql })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    throw new Error(`Direct SQL execution failed: ${error.message}`)
  }
}

// Check if we can run the script
if (require.main === module) {
  quickSetup().catch(error => {
    console.error('❌ Setup failed:', error.message)
    process.exit(1)
  })
}

module.exports = { quickSetup, executeSQL, createTestUsers }
