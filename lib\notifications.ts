import { toast } from 'sonner'

// Notification types
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  timestamp: Date
  read: boolean
  actionUrl?: string
  actionLabel?: string
  priority: 'low' | 'medium' | 'high'
  category: 'system' | 'maintenance' | 'fuel' | 'driver' | 'vehicle'
  userId?: string
  metadata?: Record<string, any>
}

// Notification service
class NotificationService {
  private notifications: Notification[] = []
  private subscribers: ((notifications: Notification[]) => void)[] = []
  private storageKey = 'fleet-notifications'

  constructor() {
    // Load notifications from localStorage on initialization
    this.loadFromStorage()
  }

  // Subscribe to notification updates
  subscribe(callback: (notifications: Notification[]) => void) {
    this.subscribers.push(callback)
    return () => {
      this.subscribers = this.subscribers.filter(sub => sub !== callback)
    }
  }

  // Notify all subscribers
  private notifySubscribers() {
    this.subscribers.forEach(callback => callback(this.notifications))
  }

  // Save to localStorage
  private saveToStorage() {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.storageKey, JSON.stringify(this.notifications))
    }
  }

  // Load from localStorage
  private loadFromStorage() {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        try {
          this.notifications = JSON.parse(stored).map((n: any) => ({
            ...n,
            timestamp: new Date(n.timestamp)
          }))
        } catch (error) {
          console.error('Failed to load notifications from storage:', error)
        }
      }
    }
  }

  // Add a new notification
  add(notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) {
    const newNotification: Notification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      read: false
    }

    this.notifications.unshift(newNotification)
    
    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100)
    }

    this.saveToStorage()
    this.notifySubscribers()

    // Show toast notification
    this.showToast(newNotification)

    return newNotification
  }

  // Show toast notification
  private showToast(notification: Notification) {
    const { type, title, message, actionUrl, actionLabel } = notification

    const toastOptions = {
      description: message,
      action: actionUrl && actionLabel ? {
        label: actionLabel,
        onClick: () => {
          if (typeof window !== 'undefined') {
            window.location.href = actionUrl
          }
        }
      } : undefined,
      duration: type === 'error' ? 10000 : 5000,
    }

    switch (type) {
      case 'success':
        toast.success(title, toastOptions)
        break
      case 'error':
        toast.error(title, toastOptions)
        break
      case 'warning':
        toast.warning(title, toastOptions)
        break
      case 'info':
      default:
        toast.info(title, toastOptions)
        break
    }
  }

  // Get all notifications
  getAll() {
    return this.notifications
  }

  // Get unread notifications
  getUnread() {
    return this.notifications.filter(n => !n.read)
  }

  // Get notifications by category
  getByCategory(category: Notification['category']) {
    return this.notifications.filter(n => n.category === category)
  }

  // Mark notification as read
  markAsRead(id: string) {
    const notification = this.notifications.find(n => n.id === id)
    if (notification) {
      notification.read = true
      this.saveToStorage()
      this.notifySubscribers()
    }
  }

  // Mark all notifications as read
  markAllAsRead() {
    this.notifications.forEach(n => n.read = true)
    this.saveToStorage()
    this.notifySubscribers()
  }

  // Delete notification
  delete(id: string) {
    this.notifications = this.notifications.filter(n => n.id !== id)
    this.saveToStorage()
    this.notifySubscribers()
  }

  // Clear all notifications
  clear() {
    this.notifications = []
    this.saveToStorage()
    this.notifySubscribers()
  }

  // Get notification count by type
  getCountByType(type: NotificationType) {
    return this.notifications.filter(n => n.type === type).length
  }

  // Get unread count
  getUnreadCount() {
    return this.notifications.filter(n => !n.read).length
  }
}

// Create singleton instance
export const notificationService = new NotificationService()

// Predefined notification templates
export const notificationTemplates = {
  // Vehicle notifications
  vehicleAdded: (serialNumber: string) => ({
    type: 'success' as const,
    title: 'Vehicle Added',
    message: `Vehicle ${serialNumber} has been added to the fleet`,
    priority: 'medium' as const,
    category: 'vehicle' as const,
    actionUrl: '/vehicles',
    actionLabel: 'View Vehicles'
  }),

  vehicleUpdated: (serialNumber: string) => ({
    type: 'info' as const,
    title: 'Vehicle Updated',
    message: `Vehicle ${serialNumber} information has been updated`,
    priority: 'low' as const,
    category: 'vehicle' as const,
    actionUrl: '/vehicles',
    actionLabel: 'View Vehicles'
  }),

  vehicleDeleted: (serialNumber: string) => ({
    type: 'warning' as const,
    title: 'Vehicle Deleted',
    message: `Vehicle ${serialNumber} has been removed from the fleet`,
    priority: 'medium' as const,
    category: 'vehicle' as const
  }),

  // Maintenance notifications
  maintenanceOverdue: (serialNumber: string, daysPastDue: number) => ({
    type: 'error' as const,
    title: 'Maintenance Overdue',
    message: `Vehicle ${serialNumber} is ${daysPastDue} days past due for maintenance`,
    priority: 'high' as const,
    category: 'maintenance' as const,
    actionUrl: '/maintenance',
    actionLabel: 'Schedule Maintenance'
  }),

  maintenanceDue: (serialNumber: string, kmUntilDue: number) => ({
    type: 'warning' as const,
    title: 'Maintenance Due Soon',
    message: `Vehicle ${serialNumber} needs maintenance in ${kmUntilDue} km`,
    priority: 'high' as const,
    category: 'maintenance' as const,
    actionUrl: '/maintenance',
    actionLabel: 'Schedule Maintenance'
  }),

  maintenanceCompleted: (serialNumber: string) => ({
    type: 'success' as const,
    title: 'Maintenance Completed',
    message: `Maintenance for vehicle ${serialNumber} has been completed`,
    priority: 'medium' as const,
    category: 'maintenance' as const,
    actionUrl: '/maintenance',
    actionLabel: 'View History'
  }),

  // Driver notifications
  driverAdded: (driverName: string) => ({
    type: 'success' as const,
    title: 'Driver Added',
    message: `Driver ${driverName} has been added to the system`,
    priority: 'medium' as const,
    category: 'driver' as const,
    actionUrl: '/drivers',
    actionLabel: 'View Drivers'
  }),

  licenseExpiring: (driverName: string, daysUntilExpiry: number) => ({
    type: 'warning' as const,
    title: 'License Expiring Soon',
    message: `Driver ${driverName}'s license expires in ${daysUntilExpiry} days`,
    priority: 'high' as const,
    category: 'driver' as const,
    actionUrl: '/drivers',
    actionLabel: 'Update License'
  }),

  licenseExpired: (driverName: string) => ({
    type: 'error' as const,
    title: 'License Expired',
    message: `Driver ${driverName}'s license has expired`,
    priority: 'high' as const,
    category: 'driver' as const,
    actionUrl: '/drivers',
    actionLabel: 'Renew License'
  }),

  // Fuel notifications
  fuelRecordAdded: (serialNumber: string, quantity: number) => ({
    type: 'info' as const,
    title: 'Fuel Record Added',
    message: `${quantity}L fuel added to vehicle ${serialNumber}`,
    priority: 'low' as const,
    category: 'fuel' as const,
    actionUrl: '/fuel',
    actionLabel: 'View Records'
  }),

  highFuelConsumption: (serialNumber: string, consumption: number) => ({
    type: 'warning' as const,
    title: 'High Fuel Consumption',
    message: `Vehicle ${serialNumber} showing high consumption: ${consumption}L/100km`,
    priority: 'medium' as const,
    category: 'fuel' as const,
    actionUrl: '/fuel',
    actionLabel: 'Check Details'
  }),

  // System notifications
  systemMaintenance: (startTime: string, duration: string) => ({
    type: 'info' as const,
    title: 'System Maintenance',
    message: `System maintenance scheduled for ${startTime} (${duration})`,
    priority: 'medium' as const,
    category: 'system' as const
  }),

  systemError: (error: string) => ({
    type: 'error' as const,
    title: 'System Error',
    message: `System error occurred: ${error}`,
    priority: 'high' as const,
    category: 'system' as const
  }),

  dataBackupComplete: () => ({
    type: 'success' as const,
    title: 'Data Backup Complete',
    message: 'Daily data backup has been completed successfully',
    priority: 'low' as const,
    category: 'system' as const
  })
}

// Utility functions for common notification scenarios
export const notify = {
  success: (title: string, message: string, options?: Partial<Notification>) => {
    return notificationService.add({
      type: 'success',
      title,
      message,
      priority: 'medium',
      category: 'system',
      ...options
    })
  },

  error: (title: string, message: string, options?: Partial<Notification>) => {
    return notificationService.add({
      type: 'error',
      title,
      message,
      priority: 'high',
      category: 'system',
      ...options
    })
  },

  warning: (title: string, message: string, options?: Partial<Notification>) => {
    return notificationService.add({
      type: 'warning',
      title,
      message,
      priority: 'medium',
      category: 'system',
      ...options
    })
  },

  info: (title: string, message: string, options?: Partial<Notification>) => {
    return notificationService.add({
      type: 'info',
      title,
      message,
      priority: 'low',
      category: 'system',
      ...options
    })
  },

  template: (template: ReturnType<typeof notificationTemplates[keyof typeof notificationTemplates]>) => {
    return notificationService.add(template)
  }
}

// Auto-notification system for maintenance and driver license checks
export const autoNotificationSystem = {
  // Check for maintenance alerts
  checkMaintenanceAlerts: async (vehicles: any[]) => {
    for (const vehicle of vehicles) {
      const currentKM = vehicle['Current KM'] || 0
      const nextMaintenanceKM = vehicle['Next Maintenance KM'] || 0
      const kmUntilMaintenance = nextMaintenanceKM - currentKM

      if (kmUntilMaintenance <= 0) {
        // Overdue maintenance
        const daysPastDue = Math.abs(Math.floor(kmUntilMaintenance / 100)) // Rough estimation
        notify.template(notificationTemplates.maintenanceOverdue(vehicle['Serial Number'], daysPastDue))
      } else if (kmUntilMaintenance <= 1000) {
        // Due soon
        notify.template(notificationTemplates.maintenanceDue(vehicle['Serial Number'], kmUntilMaintenance))
      }
    }
  },

  // Check for driver license expiry
  checkLicenseExpiry: async (drivers: any[]) => {
    const today = new Date()
    
    for (const driver of drivers) {
      if (!driver['License Expiry']) continue
      
      const expiryDate = new Date(driver['License Expiry'])
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      
      if (daysUntilExpiry < 0) {
        // Expired
        notify.template(notificationTemplates.licenseExpired(driver.Name))
      } else if (daysUntilExpiry <= 30) {
        // Expiring soon
        notify.template(notificationTemplates.licenseExpiring(driver.Name, daysUntilExpiry))
      }
    }
  },

  // Check for high fuel consumption
  checkFuelConsumption: async (fuelRecords: any[]) => {
    const vehicles = new Map<string, { records: any[], totalConsumption: number }>()
    
    // Group by vehicle
    for (const record of fuelRecords) {
      const vehicleId = record['Vehicle ID']
      const consumption = record['Consumption (L/100km)']
      
      if (!consumption) continue
      
      if (!vehicles.has(vehicleId)) {
        vehicles.set(vehicleId, { records: [], totalConsumption: 0 })
      }
      
      const vehicleData = vehicles.get(vehicleId)!
      vehicleData.records.push(record)
      vehicleData.totalConsumption += consumption
    }
    
    // Check for high consumption
    for (const [vehicleId, data] of vehicles) {
      const avgConsumption = data.totalConsumption / data.records.length
      
      if (avgConsumption > 15) { // Threshold for high consumption
        notify.template(notificationTemplates.highFuelConsumption(vehicleId, avgConsumption))
      }
    }
  },

  // Run all checks
  runAllChecks: async (data: { vehicles: any[], drivers: any[], fuel: any[] }) => {
    await Promise.all([
      autoNotificationSystem.checkMaintenanceAlerts(data.vehicles),
      autoNotificationSystem.checkLicenseExpiry(data.drivers),
      autoNotificationSystem.checkFuelConsumption(data.fuel)
    ])
  }
}