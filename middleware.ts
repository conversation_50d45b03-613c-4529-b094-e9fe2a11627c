import { NextRequest, NextResponse } from 'next/server'

// Rate limiting storage (in production, use Redis or similar)
const rateLimit = new Map<string, { count: number; resetTime: number }>()

// Security headers configuration
const securityHeaders = {
  'X-DNS-Prefetch-Control': 'on',
  'X-XSS-Protection': '1; mode=block',
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self' data:",
    "connect-src 'self' https://*.supabase.co https://supabase.co",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "upgrade-insecure-requests"
  ].join('; ')
}

// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  apiMax: 60, // API endpoints get lower limits
  authMax: 5 // Auth endpoints get very low limits
}

// Get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const real = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (real) {
    return real
  }
  
  return request.ip || 'unknown'
}

// Rate limiting function
function isRateLimited(ip: string, limit: number): boolean {
  const now = Date.now()
  const windowMs = rateLimitConfig.windowMs
  
  // Clean up old entries
  for (const [key, value] of rateLimit.entries()) {
    if (now > value.resetTime) {
      rateLimit.delete(key)
    }
  }
  
  const current = rateLimit.get(ip)
  
  if (!current || now > current.resetTime) {
    rateLimit.set(ip, {
      count: 1,
      resetTime: now + windowMs
    })
    return false
  }
  
  if (current.count >= limit) {
    return true
  }
  
  current.count++
  return false
}

// Validate request headers
function validateHeaders(request: NextRequest): boolean {
  const userAgent = request.headers.get('user-agent')
  const contentType = request.headers.get('content-type')
  
  // Block requests without user agent (possible bots)
  if (!userAgent) {
    return false
  }
  
  // Block suspicious user agents
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /postman/i
  ]
  
  // Allow legitimate browsers and our own API requests
  const allowedPatterns = [
    /mozilla/i,
    /chrome/i,
    /safari/i,
    /firefox/i,
    /edge/i,
    /fleet-management-system/i // Our own API client
  ]
  
  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent))
  const isAllowed = allowedPatterns.some(pattern => pattern.test(userAgent))
  
  if (isSuspicious && !isAllowed) {
    return false
  }
  
  // Validate content type for POST requests
  if (request.method === 'POST' && contentType && !contentType.includes('application/json')) {
    return false
  }
  
  return true
}

// Check for malicious patterns in URL
function hasMaliciousPatterns(url: string): boolean {
  const maliciousPatterns = [
    /\.\./,  // Path traversal
    /\x00/,  // Null bytes
    /<script/i,  // XSS
    /javascript:/i,  // JavaScript protocol
    /vbscript:/i,  // VBScript protocol
    /on\w+=/i,  // Event handlers
    /eval\(/i,  // Eval function
    /expression\(/i,  // CSS expressions
    /import\(/i,  // Dynamic imports
    /require\(/i,  // CommonJS requires
  ]
  
  return maliciousPatterns.some(pattern => pattern.test(url))
}

// Main middleware function
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const clientIP = getClientIP(request)

  // Skip all security checks and rate limiting in development
  if (process.env.NODE_ENV === 'development') {
    const response = NextResponse.next()
    // Skip all security headers in development to avoid CSP issues
    return response
  }
  
  // Security checks
  if (!validateHeaders(request)) {
    return new NextResponse('Forbidden', { status: 403 })
  }
  
  if (hasMaliciousPatterns(pathname)) {
    return new NextResponse('Bad Request', { status: 400 })
  }
  
  // Rate limiting
  let rateLimitValue = rateLimitConfig.max
  
  if (pathname.startsWith('/api/')) {
    rateLimitValue = rateLimitConfig.apiMax
    
    // Extra strict for auth endpoints
    if (pathname.includes('/auth') || pathname.includes('/login')) {
      rateLimitValue = rateLimitConfig.authMax
    }
  }
  
  if (isRateLimited(clientIP, rateLimitValue)) {
    return new NextResponse('Too Many Requests', { 
      status: 429,
      headers: {
        'Retry-After': String(Math.ceil(rateLimitConfig.windowMs / 1000)),
        ...securityHeaders
      }
    })
  }
  
  // Create response
  const response = NextResponse.next()
  
  // Add security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })
  
  // Add rate limit headers
  const rateLimitData = rateLimit.get(clientIP)
  if (rateLimitData) {
    response.headers.set('X-RateLimit-Limit', String(rateLimitValue))
    response.headers.set('X-RateLimit-Remaining', String(Math.max(0, rateLimitValue - rateLimitData.count)))
    response.headers.set('X-RateLimit-Reset', String(Math.ceil(rateLimitData.resetTime / 1000)))
  }
  
  // Add security headers for API routes
  if (pathname.startsWith('/api/')) {
    response.headers.set('Cache-Control', 'no-store, max-age=0')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
  }
  
  return response
}

// Configure which paths the middleware runs on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
