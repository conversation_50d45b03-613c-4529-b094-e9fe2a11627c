"use client"

import { memo } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { LucideIcon } from "lucide-react"

interface StatsCardProps {
  title: string
  value: string | number
  description?: string
  icon: LucideIcon
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info'
  loading?: boolean
  className?: string
}

const variantStyles = {
  default: {
    card: "bg-white/60 backdrop-blur-sm border-gray-200",
    icon: "bg-gray-100 text-gray-600",
    value: "text-gray-800",
    trend: {
      up: "text-green-600",
      down: "text-red-600",
      neutral: "text-gray-600"
    }
  },
  success: {
    card: "bg-green-50/60 backdrop-blur-sm border-green-200",
    icon: "bg-green-100 text-green-600",
    value: "text-green-800",
    trend: {
      up: "text-green-600",
      down: "text-red-600",
      neutral: "text-green-600"
    }
  },
  warning: {
    card: "bg-orange-50/60 backdrop-blur-sm border-orange-200",
    icon: "bg-orange-100 text-orange-600",
    value: "text-orange-800",
    trend: {
      up: "text-green-600",
      down: "text-red-600",
      neutral: "text-orange-600"
    }
  },
  danger: {
    card: "bg-red-50/60 backdrop-blur-sm border-red-200",
    icon: "bg-red-100 text-red-600",
    value: "text-red-800",
    trend: {
      up: "text-green-600",
      down: "text-red-600",
      neutral: "text-red-600"
    }
  },
  info: {
    card: "bg-blue-50/60 backdrop-blur-sm border-blue-200",
    icon: "bg-blue-100 text-blue-600",
    value: "text-blue-800",
    trend: {
      up: "text-green-600",
      down: "text-red-600",
      neutral: "text-blue-600"
    }
  }
}

const StatsCardSkeleton = memo(function StatsCardSkeleton() {
  return (
    <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
        <div className="p-2 rounded-lg bg-gray-200 animate-pulse">
          <div className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-8 w-16 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="h-3 w-32 bg-gray-200 rounded animate-pulse" />
      </CardContent>
    </Card>
  )
})

export const StatsCard = memo(function StatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  variant = 'default',
  loading = false,
  className
}: StatsCardProps) {
  const styles = variantStyles[variant]
  
  if (loading) {
    return <StatsCardSkeleton />
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return val.toLocaleString()
    }
    return val
  }

  const getTrendIcon = () => {
    if (!trend) return null
    
    switch (trend.direction) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      case 'neutral':
        return '→'
      default:
        return null
    }
  }

  return (
    <Card className={cn(
      "card-hover border-0 shadow-lg transition-all duration-200 hover:shadow-xl",
      styles.card,
      className
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-700">
          {title}
        </CardTitle>
        <div className={cn("p-2 rounded-lg", styles.icon)}>
          <Icon className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className={cn("text-2xl font-bold", styles.value)}>
          {formatValue(value)}
        </div>
        
        {description && (
          <p className="text-xs text-gray-600 mt-1">
            {description}
          </p>
        )}
        
        {trend && (
          <div className="flex items-center mt-2">
            <Badge
              variant="outline"
              className={cn(
                "text-xs px-2 py-1 border-none",
                styles.trend[trend.direction]
              )}
            >
              {getTrendIcon()} {trend.value > 0 ? '+' : ''}{trend.value}
            </Badge>
            <span className="text-xs text-gray-500 ml-2">
              {trend.label}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
})

// Compound component for multiple stats
interface StatsGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4 | 5 | 6
  gap?: 'sm' | 'md' | 'lg'
  className?: string
}

export const StatsGrid = memo(function StatsGrid({
  children,
  columns = 4,
  gap = 'md',
  className
}: StatsGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  }

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  }

  return (
    <div className={cn(
      "grid",
      gridCols[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
})

// Hook for calculating trends
export const useTrend = (current: number, previous: number) => {
  const difference = current - previous
  const percentage = previous > 0 ? (difference / previous) * 100 : 0
  
  const direction: 'up' | 'down' | 'neutral' = 
    percentage > 0 ? 'up' : percentage < 0 ? 'down' : 'neutral'
  
  return {
    value: Math.round(percentage),
    direction,
    absolute: Math.abs(difference)
  }
}