import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient } from '@/lib/supabase-api-client'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'

type MaintenanceRecord = Database['public']['Tables']['maintenance_records']['Row']
type MaintenanceInsert = Database['public']['Tables']['maintenance_records']['Insert']
type MaintenanceUpdate = Database['public']['Tables']['maintenance_records']['Update']

// Query keys
export const maintenanceKeys = {
  all: ['maintenance'] as const,
  lists: () => [...maintenanceKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...maintenanceKeys.lists(), { filters }] as const,
  details: () => [...maintenanceKeys.all, 'detail'] as const,
  detail: (id: string) => [...maintenanceKeys.details(), id] as const,
}

// ==================== QUERIES ====================

export function useMaintenance(filters?: Record<string, any>) {
  return useQuery({
    queryKey: maintenanceKeys.list(filters || {}),
    queryFn: () => supabaseApiClient.getMaintenance(),
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useMaintenanceRecord(id: string) {
  return useQuery({
    queryKey: maintenanceKeys.detail(id),
    queryFn: () => supabaseApiClient.getMaintenanceRecord(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// ==================== MUTATIONS ====================

export function useAddMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (maintenanceData: MaintenanceInsert) => 
      supabaseApiClient.addMaintenance(maintenanceData),
    
    onSuccess: (newRecord) => {
      // Invalidate and refetch maintenance list
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      // Add the new record to the cache
      queryClient.setQueryData(
        maintenanceKeys.detail(newRecord.id),
        newRecord
      )
      
      toast.success('تم إضافة سجل الصيانة بنجاح')
    },
    
    onError: (error) => {
      console.error('Add maintenance error:', error)
      toast.error('فشل في إضافة سجل الصيانة')
    }
  })
}

export function useUpdateMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: MaintenanceUpdate }) =>
      supabaseApiClient.updateMaintenance(id, data),
    
    onSuccess: (updatedRecord) => {
      // Update the specific record in cache
      queryClient.setQueryData(
        maintenanceKeys.detail(updatedRecord.id),
        updatedRecord
      )
      
      // Invalidate maintenance list to reflect changes
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      toast.success('تم تحديث سجل الصيانة بنجاح')
    },
    
    onError: (error) => {
      console.error('Update maintenance error:', error)
      toast.error('فشل في تحديث سجل الصيانة')
    }
  })
}

export function useDeleteMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteMaintenance(id),
    
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: maintenanceKeys.detail(deletedId) })
      
      // Invalidate maintenance list
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      toast.success('تم حذف سجل الصيانة بنجاح')
    },
    
    onError: (error) => {
      console.error('Delete maintenance error:', error)
      toast.error('فشل في حذف سجل الصيانة')
    }
  })
}

// ==================== COMPUTED QUERIES ====================

export function useMaintenanceStats() {
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: [...maintenanceKeys.all, 'stats'],
    queryFn: () => {
      const now = new Date()
      const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      const stats = {
        total: maintenance.length,
        scheduled: maintenance.filter(m => m.maintenance_status === 'Scheduled').length,
        inProgress: maintenance.filter(m => m.maintenance_status === 'In Progress').length,
        completed: maintenance.filter(m => m.maintenance_status === 'Completed').length,
        cancelled: maintenance.filter(m => m.maintenance_status === 'Cancelled').length,
        upcomingWeek: maintenance.filter(m => {
          const scheduledDate = new Date(m.scheduled_date)
          return scheduledDate <= oneWeekFromNow && scheduledDate >= now && 
                 m.maintenance_status === 'Scheduled'
        }).length,
        overdue: maintenance.filter(m => {
          const scheduledDate = new Date(m.scheduled_date)
          return scheduledDate < now && m.maintenance_status === 'Scheduled'
        }).length,
        totalCost: maintenance
          .filter(m => m.maintenance_status === 'Completed')
          .reduce((sum, m) => sum + m.cost, 0),
        averageCost: (() => {
          const completed = maintenance.filter(m => m.maintenance_status === 'Completed')
          return completed.length > 0 
            ? Math.round(completed.reduce((sum, m) => sum + m.cost, 0) / completed.length)
            : 0
        })(),
        byType: maintenance.reduce((acc, record) => {
          acc[record.maintenance_type] = (acc[record.maintenance_type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        byVehicle: maintenance.reduce((acc, record) => {
          const plateNumber = (record as any).vehicles?.plate_number || 'Unknown'
          acc[plateNumber] = (acc[plateNumber] || 0) + 1
          return acc
        }, {} as Record<string, number>)
      }
      
      return stats
    },
    enabled: maintenance.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useMaintenanceByVehicle(vehicleId?: string) {
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: [...maintenanceKeys.all, 'by-vehicle', vehicleId],
    queryFn: () => {
      if (!vehicleId) return maintenance
      return maintenance
        .filter(record => record.vehicle_id === vehicleId)
        .sort((a, b) => new Date(b.scheduled_date).getTime() - new Date(a.scheduled_date).getTime())
    },
    enabled: !!maintenance.length,
    staleTime: 5 * 60 * 1000,
  })
}

export function useUpcomingMaintenance(days: number = 7) {
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: [...maintenanceKeys.all, 'upcoming', days],
    queryFn: () => {
      const now = new Date()
      const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
      
      return maintenance
        .filter(record => {
          const scheduledDate = new Date(record.scheduled_date)
          return scheduledDate >= now && 
                 scheduledDate <= futureDate && 
                 record.maintenance_status === 'Scheduled'
        })
        .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
    },
    enabled: !!maintenance.length,
    staleTime: 1 * 60 * 1000, // 1 minute for critical data
  })
}

export function useOverdueMaintenance() {
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: [...maintenanceKeys.all, 'overdue'],
    queryFn: () => {
      const now = new Date()
      
      return maintenance
        .filter(record => {
          const scheduledDate = new Date(record.scheduled_date)
          return scheduledDate < now && record.maintenance_status === 'Scheduled'
        })
        .sort((a, b) => new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime())
    },
    enabled: !!maintenance.length,
    staleTime: 1 * 60 * 1000, // 1 minute for critical data
  })
}

// ==================== STATUS UPDATES ====================

export function useCompleteMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      id, 
      completedDate, 
      cost, 
      nextMaintenanceKm 
    }: { 
      id: string
      completedDate: string
      cost: number
      nextMaintenanceKm?: number
    }) =>
      supabaseApiClient.updateMaintenance(id, {
        maintenance_status: 'Completed',
        completed_date: completedDate,
        cost,
        next_maintenance_km: nextMaintenanceKm
      }),
    
    onSuccess: (updatedRecord) => {
      queryClient.setQueryData(
        maintenanceKeys.detail(updatedRecord.id),
        updatedRecord
      )
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      toast.success('تم إكمال الصيانة بنجاح')
    },
    
    onError: (error) => {
      console.error('Complete maintenance error:', error)
      toast.error('فشل في إكمال الصيانة')
    }
  })
}

export function useStartMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) =>
      supabaseApiClient.updateMaintenance(id, {
        maintenance_status: 'In Progress'
      }),
    
    onSuccess: (updatedRecord) => {
      queryClient.setQueryData(
        maintenanceKeys.detail(updatedRecord.id),
        updatedRecord
      )
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      toast.success('تم بدء الصيانة')
    },
    
    onError: (error) => {
      console.error('Start maintenance error:', error)
      toast.error('فشل في بدء الصيانة')
    }
  })
}

export function useCancelMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) =>
      supabaseApiClient.updateMaintenance(id, {
        maintenance_status: 'Cancelled'
      }),
    
    onSuccess: (updatedRecord) => {
      queryClient.setQueryData(
        maintenanceKeys.detail(updatedRecord.id),
        updatedRecord
      )
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() })
      
      toast.success('تم إلغاء الصيانة')
    },
    
    onError: (error) => {
      console.error('Cancel maintenance error:', error)
      toast.error('فشل في إلغاء الصيانة')
    }
  })
}

// ==================== BULK OPERATIONS ====================

export function useBulkUpdateMaintenance() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: MaintenanceUpdate }>) => {
      const results = await Promise.allSettled(
        updates.map(({ id, data }) => supabaseApiClient.updateMaintenance(id, data))
      )
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      
      return { successful, failed, total: updates.length }
    },
    
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.all })
      
      if (result.failed > 0) {
        toast.warning(`تم تحديث ${result.successful} سجل، فشل في ${result.failed}`)
      } else {
        toast.success(`تم تحديث ${result.successful} سجل صيانة بنجاح`)
      }
    },
    
    onError: () => {
      toast.error('فشل في التحديث المجمع لسجلات الصيانة')
    }
  })
}

// ==================== REAL-TIME SUBSCRIPTIONS ====================

export function useMaintenanceSubscription() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: [...maintenanceKeys.all, 'subscription'],
    queryFn: () => {
      // Set up real-time subscription
      const subscription = supabase
        .channel('maintenance_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'maintenance_records'
          },
          (payload: any) => {
            console.log('Maintenance change detected:', payload)
            
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: maintenanceKeys.all })
            
            // Show notification for real-time updates
            if (payload.eventType === 'INSERT') {
              toast.info('تم إضافة سجل صيانة جديد')
            } else if (payload.eventType === 'UPDATE') {
              toast.info('تم تحديث سجل صيانة')
            } else if (payload.eventType === 'DELETE') {
              toast.info('تم حذف سجل صيانة')
            }
          }
        )
        .subscribe()

      return subscription
    },
    staleTime: Infinity, // Never stale
    gcTime: Infinity, // Never garbage collect
  })
}
