import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type Driver } from "@/lib/supabase-api-client"
import { queryKeys, optimisticUpdates } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useDrivers() {
  return useQuery({
    queryKey: queryKeys.drivers(),
    queryFn: () => supabaseApiClient.getDrivers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useDriver(id: string) {
  const { data: drivers } = useDrivers() // Depend on useDrivers
  return useQuery({
    queryKey: queryKeys.driver(id),
    queryFn: () => drivers?.find(d => d.ID === id) || null,
    enabled: !!id && !!drivers, // Only enable if drivers data is available
  })
}

// Mutation hooks
export function useAddDriver() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (driverData: Partial<Driver>) => supabaseApiClient.addDriver(driverData),
    onMutate: async (newDriver) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.drivers() })
      
      const previousDrivers = queryClient.getQueryData(queryKeys.drivers())
      
      optimisticUpdates.addDriver({
        ...newDriver,
        code: Date.now(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      
      return { previousDrivers }
    },
    onError: (error, newDriver, context) => {
      if (context?.previousDrivers) {
        queryClient.setQueryData(queryKeys.drivers(), context.previousDrivers)
      }
      toast.error('Failed to add driver. Please try again.')
    },
    onSuccess: () => {
      toast.success('Driver added successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.drivers() })
    },
  })
}

export function useUpdateDriver() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Driver> }) => 
      supabaseApiClient.updateDriver(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.drivers() })
      
      const previousDrivers = queryClient.getQueryData(queryKeys.drivers())
      
      optimisticUpdates.updateDriver({
        code: id,
        ...data,
        updated_at: new Date().toISOString(),
      })
      
      return { previousDrivers }
    },
    onError: (error, variables, context) => {
      if (context?.previousDrivers) {
        queryClient.setQueryData(queryKeys.drivers(), context.previousDrivers)
      }
      toast.error('Failed to update driver. Please try again.')
    },
    onSuccess: () => {
      toast.success('Driver updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.drivers() })
    },
  })
}

export function useDeleteDriver() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteDriver(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.drivers() })
      
      const previousDrivers = queryClient.getQueryData(queryKeys.drivers())
      
      optimisticUpdates.deleteDriver(id)
      
      return { previousDrivers }
    },
    onError: (error, id, context) => {
      if (context?.previousDrivers) {
        queryClient.setQueryData(queryKeys.drivers(), context.previousDrivers)
      }
      toast.error('Failed to delete driver. Please try again.')
    },
    onSuccess: () => {
      toast.success('Driver deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.drivers() })
    },
  })
}

// Search and filter hooks
export function useSearchDrivers(drivers: Driver[] | undefined, searchTerm: string, field?: string) {
  return useQuery({
    queryKey: queryKeys.search('drivers', searchTerm),
    queryFn: () => {
      if (!searchTerm || !drivers) {
        return drivers || []
      }
      
      const searchTermLower = searchTerm.toLowerCase()
      
      return drivers.filter(driver => {
        if (field) {
          return driver[field as keyof Driver]?.toString().toLowerCase().includes(searchTermLower)
        }
        
        return Object.values(driver).some(value =>
          value?.toString().toLowerCase().includes(searchTermLower)
        )
      })
    },
    enabled: !!drivers,
    staleTime: 2 * 60 * 1000,
  })
}

export function useFilteredDrivers(drivers: Driver[] | undefined, filters: {
  status?: string
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.drivers(), 'filtered', filters],
    queryFn: () => {
      if (!drivers) {
        return []
      }

      let filtered = drivers
      
      if (filters.status && filters.status !== 'all') {
        filtered = filtered.filter(d => d.Status === filters.status)
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(d =>
          d.Name.toLowerCase().includes(searchTermLower) ||
          d['License Number'].toLowerCase().includes(searchTermLower) ||
          d.Email.toLowerCase().includes(searchTermLower) ||
          d.Phone.toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered
    },
    enabled: !!drivers,
    staleTime: 3 * 60 * 1000,
  })
}

// Driver statistics hook
export function useDriverStats(drivers: Driver[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.drivers(), 'stats'],
    queryFn: () => {
      if (!drivers) {
        return {
          total: 0,
          active: 0,
          inactive: 0,
          assigned: 0,
          unassigned: 0,
          expiringSoon: 0,
          expired: 0,
        }
      }

      const today = new Date()
      const expirationWarningDays = 30
      
      return {
        total: drivers.length,
        active: drivers.filter(d => d.Status === 'Active').length,
        inactive: drivers.filter(d => d.Status === 'Inactive').length,
        assigned: drivers.filter(d => d['Assigned Vehicle ID']).length,
        unassigned: drivers.filter(d => !d['Assigned Vehicle ID']).length,
        expiringSoon: drivers.filter(d => {
          if (!d['License Expiry']) return false
          const expiryDate = new Date(d['License Expiry'])
          const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
          return daysUntilExpiry > 0 && daysUntilExpiry <= expirationWarningDays
        }).length,
        expired: drivers.filter(d => {
          if (!d['License Expiry']) return false
          const expiryDate = new Date(d['License Expiry'])
          return expiryDate < today
        }).length,
      }
    },
    enabled: !!drivers,
    staleTime: 2 * 60 * 1000,
  })
}

// Available drivers (not assigned to vehicles)
export function useAvailableDrivers(drivers: Driver[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.drivers(), 'available'],
    queryFn: () => {
      if (!drivers) {
        return []
      }
      return drivers.filter(d => d.Status === 'Active' && !d['Assigned Vehicle ID'])
    },
    enabled: !!drivers,
    staleTime: 5 * 60 * 1000,
  })
}