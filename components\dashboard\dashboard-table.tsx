"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertTriangle } from "lucide-react"
import { useDashboardData } from "@/hooks/use-dashboard"
// import { DashboardData } from "@/lib/supabase-api-client"

function getStatusBadgeVariant(status: string) {
  switch (status.toLowerCase()) {
    case 'active':
      return 'default'
    case 'maintenance':
      return 'secondary'
    case 'inactive':
      return 'destructive'
    default:
      return 'outline'
  }
}

function formatCurrency(value: number) {
  return new Intl.NumberFormat('en-EG', {
    style: 'currency',
    currency: 'EGP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value)
}

function formatDate(dateString: string) {
  if (!dateString) return 'N/A'
  try {
    return new Date(dateString).toLocaleDateString('en-GB')
  } catch {
    return dateString
  }
}

export function DashboardTable() {
  const { data: dashboardData, isLoading, error } = useDashboardData()

  if (isLoading) {
    return (
      <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-gray-800">Fleet Overview</CardTitle>
          <CardDescription className="text-gray-600">Detailed view of all vehicles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex space-x-4">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-28" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-gray-800">Fleet Overview</CardTitle>
          <CardDescription className="text-gray-600">Detailed view of all vehicles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-600 py-8">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load dashboard data</p>
            <p className="text-sm text-gray-500 mt-1">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!dashboardData || dashboardData.length === 0) {
    return (
      <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-gray-800">Fleet Overview</CardTitle>
          <CardDescription className="text-gray-600">Detailed view of all vehicles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center text-gray-500 py-8">
            <p>No dashboard data available</p>
            <p className="text-sm mt-1">Please add data to the database</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="text-gray-800">Fleet Overview</CardTitle>
        <CardDescription className="text-gray-600">
          Detailed view of all vehicles ({dashboardData.length} vehicles)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle ID</TableHead>
                <TableHead>Plate Number</TableHead>
                <TableHead>Model</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Current KM</TableHead>
                <TableHead>Driver</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Fuel Cost</TableHead>
                <TableHead>Maintenance Cost</TableHead>
                <TableHead>Next Maintenance</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dashboardData.map((vehicle: DashboardData) => (
                <TableRow key={vehicle.vehicle_id}>
                  <TableCell className="font-medium">{vehicle.vehicle_id}</TableCell>
                  <TableCell>{vehicle.plate_number}</TableCell>
                  <TableCell>{vehicle.model}</TableCell>
                  <TableCell>{vehicle.vehicle_type}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(vehicle.vehicle_status)}>
                      {vehicle.vehicle_status}
                    </Badge>
                  </TableCell>
                  <TableCell>{vehicle.current_km?.toLocaleString() || 'N/A'} km</TableCell>
                  <TableCell>{vehicle.assigned_driver || 'Unassigned'}</TableCell>
                  <TableCell>{vehicle.branch_name || 'N/A'}</TableCell>
                  <TableCell>{formatCurrency(vehicle.total_fuel_cost_egp || 0)}</TableCell>
                  <TableCell>{formatCurrency(vehicle.total_maintenance_cost_egp || 0)}</TableCell>
                  <TableCell>{vehicle.next_maintenance_km?.toLocaleString() || 'N/A'} km</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}