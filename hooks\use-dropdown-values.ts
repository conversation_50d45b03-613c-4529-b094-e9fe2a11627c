"use client"

import { useState, useEffect } from "react"

export interface DropdownValue {
  id: string
  value: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface DropdownCategory {
  id: string
  name: string
  values: DropdownValue[]
}

// Default dropdown values for the system
const DEFAULT_DROPDOWN_VALUES = {
  vehicle_type: ["Chrysler Pacifica", "Toyota Camry", "Ford F-150", "Mercedes-Benz Sprinter"],
  service_type: ["Van", "Sedan", "Truck", "SUV"],
  fuel_type: ["Gasoline 95", "Gasoline 92", "Diesel", "Electric"],
  color: ["White", "Black", "Grey", "Blue", "Red", "Silver"],
  department: ["London Cab", "Airport Shuttle", "City Tour", "Executive Transport"],
  status: ["Active", "Maintenance", "Inactive", "Out of Service"]
}

export function useDropdownValues() {
  const [categories, setCategories] = useState<DropdownCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Initialize categories from localStorage or defaults
  useEffect(() => {
    const initializeCategories = () => {
      const savedCategories = localStorage.getItem("dropdown_categories")
      
      if (savedCategories) {
        try {
          const parsed = JSON.parse(savedCategories)
          setCategories(parsed)
        } catch (error) {
          console.error("Error parsing saved categories:", error)
          loadDefaults()
        }
      } else {
        loadDefaults()
      }
      
      setIsLoading(false)
    }

    const loadDefaults = () => {
      const defaultCategories = Object.entries(DEFAULT_DROPDOWN_VALUES).map(([key, values]) => ({
        id: key,
        name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        values: values.map((value, index) => ({
          id: `${key}_${index + 1}`,
          value,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }))
      }))
      
      setCategories(defaultCategories)
    }

    initializeCategories()
  }, [])

  // Save to localStorage whenever categories change
  useEffect(() => {
    if (categories.length > 0 && !isLoading) {
      localStorage.setItem("dropdown_categories", JSON.stringify(categories))
    }
  }, [categories, isLoading])

  // Get active values for a specific category
  const getActiveValues = (categoryId: string): string[] => {
    const category = categories.find(cat => cat.id === categoryId)
    return category?.values
      .filter(val => val.isActive && val.value.trim().length > 0)
      .map(val => val.value) || []
  }

  // Get all values for a specific category (active and inactive)
  const getAllValues = (categoryId: string): DropdownValue[] => {
    const category = categories.find(cat => cat.id === categoryId)
    return category?.values || []
  }

  // Add a new value to a category
  const addValue = (categoryId: string, value: string): boolean => {
    try {
      const trimmedValue = value.trim()
      
      // Check if value is empty
      if (trimmedValue.length === 0) {
        console.error("Cannot add empty value")
        return false
      }

      // Check if value already exists
      const category = categories.find(cat => cat.id === categoryId)
      if (category?.values.some(val => val.value.toLowerCase() === trimmedValue.toLowerCase())) {
        console.error("Value already exists")
        return false
      }

      const newValue: DropdownValue = {
        id: `${categoryId}_${Date.now()}`,
        value: trimmedValue,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      setCategories(prev => prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, values: [...cat.values, newValue] }
          : cat
      ))

      return true
    } catch (error) {
      console.error("Error adding value:", error)
      return false
    }
  }

  // Update an existing value
  const updateValue = (categoryId: string, valueId: string, newValue: string): boolean => {
    try {
      setCategories(prev => prev.map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              values: cat.values.map(val => 
                val.id === valueId 
                  ? { ...val, value: newValue.trim(), updatedAt: new Date().toISOString() }
                  : val
              )
            }
          : cat
      ))

      return true
    } catch (error) {
      console.error("Error updating value:", error)
      return false
    }
  }

  // Delete a value
  const deleteValue = (categoryId: string, valueId: string): boolean => {
    try {
      setCategories(prev => prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, values: cat.values.filter(val => val.id !== valueId) }
          : cat
      ))

      return true
    } catch (error) {
      console.error("Error deleting value:", error)
      return false
    }
  }

  // Toggle value status (active/inactive)
  const toggleValueStatus = (categoryId: string, valueId: string): boolean => {
    try {
      setCategories(prev => prev.map(cat => 
        cat.id === categoryId 
          ? {
              ...cat,
              values: cat.values.map(val => 
                val.id === valueId 
                  ? { ...val, isActive: !val.isActive, updatedAt: new Date().toISOString() }
                  : val
              )
            }
          : cat
      ))

      return true
    } catch (error) {
      console.error("Error toggling value status:", error)
      return false
    }
  }

  return {
    categories,
    isLoading,
    getActiveValues,
    getAllValues,
    addValue,
    updateValue,
    deleteValue,
    toggleValueStatus
  }
}