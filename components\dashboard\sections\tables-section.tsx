"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { DashboardData } from "@/lib/supabase-api-client"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface TablesSectionProps {
  data: DashboardData[]
}

export function TablesSection({ data }: TablesSectionProps) {

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Fleet Summary</h2>
        <p className="text-gray-600">Summary statistics for your fleet by branch</p>
      </div>

      {/* Fleet Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Fleet Summary by Branch</CardTitle>
          <CardDescription>
            Comprehensive statistics and performance metrics for each branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          {(() => {
            // Calculate summary by branch
            const branchSummary = data.reduce((acc, vehicle) => {
              const branch = vehicle.branch_name || "Unknown"
              if (!acc[branch]) {
                acc[branch] = {
                  totalVehicles: 0,
                  activeVehicles: 0,
                  assignedDrivers: new Set(),
                  totalFuelCost: 0,
                  totalMaintenanceCost: 0
                }
              }
              
              acc[branch].totalVehicles++
              if (vehicle.vehicle_status === "Active") {
                acc[branch].activeVehicles++
              }
              if (vehicle.assigned_driver && vehicle.assigned_driver.trim() !== "") {
                acc[branch].assignedDrivers.add(vehicle.assigned_driver)
              }
              acc[branch].totalFuelCost += parseFloat(vehicle.total_fuel_cost_egp?.toString() || "0") || 0
              acc[branch].totalMaintenanceCost += parseFloat(vehicle.total_maintenance_cost_egp?.toString() || "0") || 0
              
              return acc
            }, {} as Record<string, any>)

            const summaryData = Object.entries(branchSummary).map(([branch, stats]) => ({
              branch,
              totalVehicles: stats.totalVehicles,
              activeVehicles: stats.activeVehicles,
              assignedDrivers: stats.assignedDrivers.size,
              totalFuelCost: stats.totalFuelCost,
              totalMaintenanceCost: stats.totalMaintenanceCost
            }))

            return (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Branch</TableHead>
                      <TableHead>Total Vehicles</TableHead>
                      <TableHead>Active Vehicles</TableHead>
                      <TableHead>Assigned Drivers</TableHead>
                      <TableHead>Total Fuel Cost (EGP)</TableHead>
                      <TableHead>Total Maintenance Cost (EGP)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {summaryData.map((branch, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">
                          {branch.branch}
                        </TableCell>
                        <TableCell>{branch.totalVehicles}</TableCell>
                        <TableCell>{branch.activeVehicles}</TableCell>
                        <TableCell>{branch.assignedDrivers}</TableCell>
                        <TableCell>{branch.totalFuelCost.toLocaleString()}</TableCell>
                        <TableCell>{branch.totalMaintenanceCost.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )
          })()}
        </CardContent>
      </Card>
    </div>
  )
}