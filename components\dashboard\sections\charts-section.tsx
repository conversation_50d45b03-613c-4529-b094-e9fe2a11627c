"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { DashboardData } from "@/lib/supabase-api-client"
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts"

interface ChartsSectionProps {
  data: DashboardData[]
}

export function ChartsSection({ data }: ChartsSectionProps) {
  // Prepare data for different charts
  
  // Fuel Cost per KM by Vehicle
  const fuelCostData = data
    .filter(vehicle => vehicle.plate_number && vehicle.fuel_efficiency_egp_per_km)
    .map(vehicle => ({
      plate_number: vehicle.plate_number,
      fuel_efficiency: parseFloat(vehicle.fuel_efficiency_egp_per_km?.toString() || "0") || 0
    }))
    .sort((a, b) => b.fuel_efficiency - a.fuel_efficiency)
    .slice(0, 10) // Top 10 vehicles

  // Maintenance Cost by Vehicle
  const maintenanceCostData = data
    .filter(vehicle => vehicle.plate_number && vehicle.total_maintenance_cost_egp)
    .map(vehicle => ({
      plate_number: vehicle.plate_number,
      maintenance_cost: parseFloat(vehicle.total_maintenance_cost_egp?.toString() || "0") || 0
    }))
    .sort((a, b) => b.maintenance_cost - a.maintenance_cost)
    .slice(0, 10) // Top 10 vehicles

  // Vehicle Status Distribution
  const statusData = data.reduce((acc, vehicle) => {
    const status = vehicle.vehicle_status || "Unknown"
    acc[status] = (acc[status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const statusChartData = Object.entries(statusData).map(([status, count]) => ({
    status,
    count,
    percentage: ((count / data.length) * 100).toFixed(1)
  }))

  // Vehicle Type Distribution
  const typeData = data.reduce((acc, vehicle) => {
    const type = vehicle.vehicle_type || "Unknown"
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const typeChartData = Object.entries(typeData).map(([type, count]) => ({
    type,
    count,
    percentage: ((count / data.length) * 100).toFixed(1)
  }))

  // Vehicles per Branch
  const branchData = data.reduce((acc, vehicle) => {
    const branch = vehicle.branch_name || "Unknown"
    acc[branch] = (acc[branch] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const branchChartData = Object.entries(branchData).map(([branch, count]) => ({
    branch,
    count
  }))

  // Total Fuel Consumed by Vehicle
  const fuelConsumptionData = data
    .filter(vehicle => vehicle.plate_number && vehicle.total_fuel_liters)
    .map(vehicle => ({
      plate_number: vehicle.plate_number,
      fuel_liters: parseFloat(vehicle.total_fuel_liters?.toString() || "0") || 0
    }))
    .sort((a, b) => b.fuel_liters - a.fuel_liters)
    .slice(0, 10) // Top 10 vehicles

  // Fuel vs Maintenance Cost
  const costComparisonData = data
    .filter(vehicle => 
      vehicle.plate_number && 
      (vehicle.total_fuel_cost_egp || vehicle.total_maintenance_cost_egp)
    )
    .map(vehicle => ({
      plate_number: vehicle.plate_number,
      fuel_cost: parseFloat(vehicle.total_fuel_cost_egp?.toString() || "0") || 0,
      maintenance_cost: parseFloat(vehicle.total_maintenance_cost_egp?.toString() || "0") || 0
    }))
    .slice(0, 10) // Top 10 vehicles

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Fleet Analytics</h2>
        <p className="text-gray-600">Visual insights into your fleet performance</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Fuel Cost per KM */}
        <Card>
          <CardHeader>
            <CardTitle>Fuel Cost per KM by Vehicle</CardTitle>
            <CardDescription>
              Top 10 vehicles by fuel cost efficiency (EGP per KM)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={fuelCostData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="plate_number" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="fuel_efficiency" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Maintenance Cost by Vehicle */}
        <Card>
          <CardHeader>
            <CardTitle>Maintenance Cost by Vehicle</CardTitle>
            <CardDescription>
              Top 10 vehicles by total maintenance cost
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={maintenanceCostData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="plate_number" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="maintenance_cost" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vehicle Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Operational Status</CardTitle>
            <CardDescription>
              Distribution of vehicles by operational status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ status, percentage }) => `${status}: ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {statusChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vehicle Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Type Distribution</CardTitle>
            <CardDescription>
              Distribution of vehicle types in the fleet
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={typeChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ type, percentage }) => `${type}: ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {typeChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vehicles per Branch */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicles per Branch</CardTitle>
            <CardDescription>
              Number of vehicles per branch for operational distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={branchChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="branch" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#0088FE" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Total Fuel Consumed */}
        <Card>
          <CardHeader>
            <CardTitle>Total Fuel Consumed by Vehicle</CardTitle>
            <CardDescription>
              Top 10 vehicles by fuel consumption in liters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={fuelConsumptionData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="plate_number" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="fuel_liters" fill="#FFBB28" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Fuel vs Maintenance Cost */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Fuel vs Maintenance Cost by Vehicle</CardTitle>
            <CardDescription>
              Compare fuel and maintenance expenses for top 10 vehicles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={costComparisonData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="plate_number" 
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="fuel_cost" stackId="a" fill="#8884d8" name="Fuel Cost (EGP)" />
                <Bar dataKey="maintenance_cost" stackId="a" fill="#82ca9d" name="Maintenance Cost (EGP)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}