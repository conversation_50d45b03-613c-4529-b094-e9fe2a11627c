import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type Branch } from "@/lib/supabase-api-client"
import { queryKeys, optimisticUpdates } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useBranches() {
  return useQuery({
    queryKey: queryKeys.branches(),
    queryFn: () => supabaseApiClient.getBranches(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useBranch(id: string) {
  const { data: branches } = useBranches() // Depend on useBranches
  return useQuery({
    queryKey: queryKeys.branch(id),
    queryFn: () => branches?.find(b => b.id === id) || null,
    enabled: !!id && !!branches, // Only enable if branches data is available
  })
}

// Mutation hooks
export function useAddBranch() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (branchData: Partial<Branch>) => supabaseApiClient.addBranch(branchData),
    onMutate: async (newBranch) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.branches() })
      
      // Snapshot the previous value
      const previousBranches = queryClient.getQueryData(queryKeys.branches())
      
      // Optimistically update to the new value
      optimisticUpdates.addBranch({
        ...newBranch,
        branch_id: `temp_${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      
      // Return a context object with the snapshotted value
      return { previousBranches }
    },
    onError: (error, newBranch, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousBranches) {
        queryClient.setQueryData(queryKeys.branches(), context.previousBranches)
      }
      toast.error('Failed to add branch. Please try again.')
    },
    onSuccess: (data) => {
      // Update the cache with the actual branch data returned from the server
      queryClient.setQueryData(queryKeys.branches(), (oldData: any) => {
        if (!oldData) return [data]
        // Replace the optimistic entry (temp_ID) with the actual data from the server
        return oldData.map((branch: any) =>
          branch.branch_id && branch.branch_id.startsWith('temp_') ? data : branch
        )
      })
      toast.success('Branch added successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.branches() })
    },
  })
}

export function useUpdateBranch() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Branch> }) => {
      console.log("useUpdateBranch mutationFn called with:", { id, data })
      return supabaseApiClient.updateBranch(id, data)
    },
    onMutate: async ({ id, data }) => {
      console.log("useUpdateBranch onMutate called with:", { id, data })
      
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.branches() })
      
      // Snapshot the previous value
      const previousBranches = queryClient.getQueryData(queryKeys.branches())
      console.log("Previous branches data:", previousBranches)
      
      // Optimistically update to the new value
      const updatedBranch = {
        branch_id: id,
        ...data,
        updated_at: new Date().toISOString(),
      }
      
      optimisticUpdates.updateBranch(updatedBranch)
      console.log("Optimistic update applied:", updatedBranch)
      
      return { previousBranches, id, data }
    },
    onError: (error, variables, context) => {
      console.error("useUpdateBranch onError:", error, variables, context)
      
      // Rollback optimistic update
      if (context?.previousBranches) {
        queryClient.setQueryData(queryKeys.branches(), context.previousBranches)
        console.log("Rolled back to previous data")
      }
      
      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Failed to update branch. Please try again.'
      toast.error(errorMessage)
    },
    onSuccess: (data, variables, context) => {
      console.log("useUpdateBranch onSuccess:", data, variables)
      
      // Update cache with actual server response
      queryClient.setQueryData(queryKeys.branches(), (oldData: any) => {
        if (!oldData) return [data]
        return oldData.map((branch: any) =>
          branch.branch_id === variables.id ? { ...branch, ...data } : branch
        )
      })
      
      toast.success('Branch updated successfully!')
    },
    onSettled: (data, error, variables, context) => {
      console.log("useUpdateBranch onSettled:", { data, error, variables })
      
      // Always refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.branches() })
    },
  })
}

export function useDeleteBranch() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteBranch(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.branches() })
      
      const previousBranches = queryClient.getQueryData(queryKeys.branches())
      
      optimisticUpdates.deleteBranch(id)
      
      return { previousBranches }
    },
    onError: (error, id, context) => {
      if (context?.previousBranches) {
        queryClient.setQueryData(queryKeys.branches(), context.previousBranches)
      }
      toast.error('Failed to delete branch. Please try again.')
    },
    onSuccess: () => {
      toast.success('Branch deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.branches() })
    },
  })
}

// Search hook
export function useSearchBranches(branches: Branch[] | undefined, searchTerm: string, field?: string) {
  return useQuery({
    queryKey: queryKeys.search('branches', searchTerm),
    queryFn: () => {
      if (!searchTerm || !branches) {
        return branches || [] // Return all branches if no search term, or empty array if no data
      }
      
      const searchTermLower = searchTerm.toLowerCase()
      
      return branches.filter(branch => {
        if (field) {
          return branch[field as keyof Branch]?.toString().toLowerCase().includes(searchTermLower)
        }
        
        return Object.values(branch).some(value =>
          value?.toString().toLowerCase().includes(searchTermLower)
        )
      })
    },
    enabled: !!branches, // Only enable if branches data is available
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
  })
}

// Filtered branches hook
export function useFilteredBranches(branches: Branch[] | undefined, filters: {
  branch_status?: string
  location?: string
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.branches(), 'filtered', filters],
    queryFn: () => {
      if (!branches) {
        return []
      }

      let filtered = branches
      
      if (filters.branch_status && filters.branch_status !== 'all') {
        filtered = filtered.filter(b => b.branch_status === filters.branch_status)
      }
      
      if (filters.location && filters.location !== 'all') {
        filtered = filtered.filter(b => b.location === filters.location)
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(b =>
          b.name?.toLowerCase().includes(searchTermLower) ||
          b.location?.toLowerCase().includes(searchTermLower) ||
          b.address?.toLowerCase().includes(searchTermLower) ||
          b.email?.toLowerCase().includes(searchTermLower) ||
          b.phone?.toLowerCase().includes(searchTermLower) ||
          b.branch_status?.toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered
    },
    enabled: !!branches, // Only enable if branches data is available
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

// Branch statistics hook
export function useBranchStats(branches: Branch[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.branches(), 'stats'],
    queryFn: () => {
      if (!branches) {
        return {
          total: 0,
          active: 0,
          inactive: 0,
          locations: 0,
        }
      }

      const uniqueLocations = new Set(branches.map(b => b.location)).size

      return {
        total: branches.length,
        active: branches.filter(b => b.branch_status === 'Active').length,
        inactive: branches.filter(b => b.branch_status === 'Inactive').length,
        locations: uniqueLocations,
      }
    },
    enabled: !!branches,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}