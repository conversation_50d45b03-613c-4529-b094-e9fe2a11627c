"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useOptimizedDashboardData, usePerformanceMonitor } from "@/hooks/use-optimized-dashboard-data"
import { RefreshCw, Zap, Clock, Database } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export function OptimizedFleetDashboard() {
  const {
    data,
    isLoading,
    error,
    isFetching,
    refresh,
    forceRefresh,
    getPerformanceStats,
    dataUpdatedAt
  } = useOptimizedDashboardData()

  const [performanceStats, setPerformanceStats] = useState<any>(null)
  const [showPerformanceDetails, setShowPerformanceDetails] = useState(false)

  // Update performance stats periodically
  useEffect(() => {
    const updateStats = () => {
      const stats = getPerformanceStats()
      setPerformanceStats(stats)
    }

    updateStats()
    const interval = setInterval(updateStats, 5000) // Update every 5 seconds
    return () => clearInterval(interval)
  }, [getPerformanceStats])

  // Calculate dashboard statistics
  const stats = {
    totalVehicles: data.length,
    activeVehicles: data.filter(v => v.vehicle_status === 'Active').length,
    inactiveVehicles: data.filter(v => v.vehicle_status === 'Inactive').length,
    maintenanceNeeded: data.filter(v => v.current_km >= v.next_maintenance_km).length,
    totalFuelCost: data.reduce((sum, v) => sum + (v.total_fuel_cost_egp || 0), 0),
    totalMaintenanceCost: data.reduce((sum, v) => sum + (v.total_maintenance_cost_egp || 0), 0)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const getPerformanceColor = (responseTime: number) => {
    if (responseTime < 2000) return "text-green-600"
    if (responseTime < 5000) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="space-y-6">
      {/* Performance Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">لوحة القيادة المحسنة</h1>
          <p className="text-muted-foreground">
            آخر تحديث: {dataUpdatedAt ? new Date(dataUpdatedAt).toLocaleString('ar-EG') : 'لم يتم التحديث بعد'}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Performance Indicator */}
          {performanceStats && (
            <Badge 
              variant="outline" 
              className="cursor-pointer"
              onClick={() => setShowPerformanceDetails(!showPerformanceDetails)}
            >
              <Zap className="h-3 w-3 mr-1" />
              {performanceStats.averageResponseTime?.toFixed(0)}ms
              <span className="mx-1">•</span>
              {performanceStats.cacheHitRate}
            </Badge>
          )}

          {/* Loading Indicator */}
          {isFetching && (
            <Badge variant="secondary">
              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
              جاري التحديث...
            </Badge>
          )}

          {/* Refresh Buttons */}
          <Button
            onClick={refresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            تحديث
          </Button>

          <Button
            onClick={forceRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <Database className="h-4 w-4 mr-2" />
            تحديث قسري
          </Button>
        </div>
      </div>

      {/* Performance Details */}
      {showPerformanceDetails && performanceStats && (
        <Alert>
          <Zap className="h-4 w-4" />
          <AlertDescription>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2">
              <div>
                <div className="text-sm font-medium">متوسط وقت الاستجابة</div>
                <div className={`text-lg font-bold ${getPerformanceColor(performanceStats.averageResponseTime)}`}>
                  {performanceStats.averageResponseTime?.toFixed(0)}ms
                </div>
              </div>
              <div>
                <div className="text-sm font-medium">معدل نجاح التخزين المؤقت</div>
                <div className="text-lg font-bold text-green-600">
                  {performanceStats.cacheHitRate}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium">إجمالي الطلبات</div>
                <div className="text-lg font-bold text-blue-600">
                  {performanceStats.totalRequests}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium">حالة القائمة</div>
                <div className="text-lg font-bold text-purple-600">
                  {performanceStats.queue?.queueLength || 0}
                </div>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            خطأ في تحميل البيانات: {error.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      )}

      {/* Dashboard Statistics */}
      {!isLoading && data.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Vehicles */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المركبات</CardTitle>
              <div className="h-4 w-4 text-muted-foreground">🚗</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalVehicles}</div>
              <p className="text-xs text-muted-foreground">
                جميع المركبات في النظام
              </p>
            </CardContent>
          </Card>

          {/* Active Vehicles */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المركبات النشطة</CardTitle>
              <div className="h-4 w-4 text-green-600">✅</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.activeVehicles}</div>
              <p className="text-xs text-muted-foreground">
                {((stats.activeVehicles / stats.totalVehicles) * 100).toFixed(1)}% من الإجمالي
              </p>
            </CardContent>
          </Card>

          {/* Inactive Vehicles */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المركبات غير النشطة</CardTitle>
              <div className="h-4 w-4 text-red-600">❌</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.inactiveVehicles}</div>
              <p className="text-xs text-muted-foreground">
                {((stats.inactiveVehicles / stats.totalVehicles) * 100).toFixed(1)}% من الإجمالي
              </p>
            </CardContent>
          </Card>

          {/* Maintenance Needed */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تحتاج صيانة</CardTitle>
              <div className="h-4 w-4 text-yellow-600">🔧</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.maintenanceNeeded}</div>
              <p className="text-xs text-muted-foreground">
                مركبات تحتاج صيانة فورية
              </p>
            </CardContent>
          </Card>

          {/* Total Fuel Cost */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي تكلفة الوقود</CardTitle>
              <div className="h-4 w-4 text-blue-600">⛽</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(stats.totalFuelCost)}
              </div>
              <p className="text-xs text-muted-foreground">
                التكلفة الإجمالية للوقود
              </p>
            </CardContent>
          </Card>

          {/* Total Maintenance Cost */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي تكلفة الصيانة</CardTitle>
              <div className="h-4 w-4 text-purple-600">🔧</div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(stats.totalMaintenanceCost)}
              </div>
              <p className="text-xs text-muted-foreground">
                التكلفة الإجمالية للصيانة
              </p>
            </CardContent>
          </Card>

          {/* Performance Score */}
          {performanceStats && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">نقاط الأداء</CardTitle>
                <Zap className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {((performanceStats.cacheHits / Math.max(performanceStats.totalRequests, 1)) * 100).toFixed(0)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  كفاءة التخزين المؤقت
                </p>
              </CardContent>
            </Card>
          )}

          {/* Data Freshness */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">حداثة البيانات</CardTitle>
              <Clock className="h-4 w-4 text-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">
                {dataUpdatedAt ? 
                  Math.round((Date.now() - dataUpdatedAt) / 1000) : 0}s
              </div>
              <p className="text-xs text-muted-foreground">
                منذ آخر تحديث
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recent Vehicles Table (Sample) */}
      {!isLoading && data.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>المركبات الحديثة</CardTitle>
            <CardDescription>
              عرض أول 10 مركبات من البيانات المحملة ({data.length} إجمالي)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-right p-2">رقم اللوحة</th>
                    <th className="text-right p-2">الموديل</th>
                    <th className="text-right p-2">النوع</th>
                    <th className="text-right p-2">الحالة</th>
                    <th className="text-right p-2">الكيلومترات</th>
                    <th className="text-right p-2">السائق</th>
                  </tr>
                </thead>
                <tbody>
                  {data.slice(0, 10).map((vehicle, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{vehicle.plate_number}</td>
                      <td className="p-2">{vehicle.model}</td>
                      <td className="p-2">{vehicle.vehicle_type}</td>
                      <td className="p-2">
                        <Badge variant={vehicle.vehicle_status === 'Active' ? 'default' : 'secondary'}>
                          {vehicle.vehicle_status}
                        </Badge>
                      </td>
                      <td className="p-2">{vehicle.current_km?.toLocaleString()}</td>
                      <td className="p-2">{vehicle.assigned_driver || 'غير محدد'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Data State */}
      {!isLoading && data.length === 0 && !error && (
        <Card>
          <CardContent className="text-center py-8">
            <div className="text-muted-foreground">
              لا توجد بيانات متاحة
            </div>
            <Button onClick={refresh} className="mt-4">
              إعادة المحاولة
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}