"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Car, Wrench, Fuel, Users, AlertTriangle, CheckCircle } from "lucide-react"
import { useDashboardStats } from "@/hooks/use-dashboard"
import { Skeleton } from "@/components/ui/skeleton"

export function StatsCards() {
  const { data: stats, isLoading, error } = useDashboardStats()

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, index) => (
          <Card key={index} className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-8 rounded-lg" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="col-span-full card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Failed to load dashboard statistics</p>
              <p className="text-sm text-gray-500 mt-1">{error.message}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const statsConfig = [
    {
      title: "Total Vehicles",
      value: stats?.totalVehicles?.toString() || "0",
      change: `${stats?.activeVehicles || 0} active`,
      icon: Car,
      gradient: "gradient-bg-primary",
      iconColor: "text-blue-600",
    },
    {
      title: "Active Drivers",
      value: stats?.activeDrivers?.toString() || "0",
      change: "Currently assigned",
      icon: Users,
      gradient: "gradient-bg-success",
      iconColor: "text-green-600",
    },
    {
      title: "Pending Maintenance",
      value: stats?.pendingMaintenance?.toString() || "0",
      change: "Vehicles in maintenance",
      icon: Wrench,
      gradient: "gradient-bg-warning",
      iconColor: "text-orange-600",
    },
    {
      title: "Fuel Efficiency",
      value: `${stats?.avgFuelEfficiency || "0"} EGP/km`,
      change: "Average cost per km",
      icon: Fuel,
      gradient: "gradient-bg-secondary",
      iconColor: "text-purple-600",
    },
    {
      title: "Overdue Services",
      value: stats?.overdueServices?.toString() || "0",
      change: "Requires attention",
      icon: AlertTriangle,
      gradient: "gradient-bg-danger",
      iconColor: "text-red-600",
    },
    {
      title: "Completed Services",
      value: stats?.completedServices?.toString() || "0",
      change: "Total completed",
      icon: CheckCircle,
      gradient: "gradient-bg-accent",
      iconColor: "text-pink-600",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {statsConfig.map((stat) => (
        <Card key={stat.title} className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">{stat.title}</CardTitle>
            <div className={`p-2 rounded-lg ${stat.gradient}`}>
              <stat.icon className={`h-4 w-4 ${stat.iconColor}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-800">{stat.value}</div>
            <p className="text-xs text-gray-600 mt-1">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
