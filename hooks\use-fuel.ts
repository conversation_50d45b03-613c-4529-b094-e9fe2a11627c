
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type FuelRecord } from "@/lib/supabase-api-client"
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useFuel() {
  return useQuery({
    queryKey: queryKeys.fuel(),
    queryFn: () => supabaseApiClient.getFuel(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useFuelRecord(id: string) {
  const { data: fuelRecords } = useFuel() // Depend on useFuel
  return useQuery({
    queryKey: queryKeys.fuelRecord(id),
    queryFn: () => fuelRecords?.find(f => f.ID === id) || null,
    enabled: !!id && !!fuelRecords, // Only enable if fuelRecords data is available
  })
}

// Mutation hooks
export function useAddFuel() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (fuelData: Partial<FuelRecord>) => supabaseApiClient.addFuel(fuelData),
    onMutate: async (newFuel) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.fuel() })
      
      const previousFuel = queryClient.getQueryData(queryKeys.fuel())
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.fuel(), (old: any) => {
        if (!old) return [{ ...newFuel, ID: `temp_${Date.now()}` }]
        return [...old, { ...newFuel, ID: `temp_${Date.now()}` }]
      })
      
      return { previousFuel }
    },
    onError: (error, newFuel, context) => {
      if (context?.previousFuel) {
        queryClient.setQueryData(queryKeys.fuel(), context.previousFuel)
      }
      toast.error('Failed to add fuel record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Fuel record added successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.fuel() })
    },
  })
}

export function useUpdateFuel() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<FuelRecord> }) => 
      supabaseApiClient.updateFuel(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.fuel() })
      
      const previousFuel = queryClient.getQueryData(queryKeys.fuel())
      
      queryClient.setQueryData(queryKeys.fuel(), (old: any) => {
        if (!old) return []
        return old.map((record: any) => 
          record.ID === id ? { ...record, ...data } : record
        )
      })
      
      return { previousFuel }
    },
    onError: (error, variables, context) => {
      if (context?.previousFuel) {
        queryClient.setQueryData(queryKeys.fuel(), context.previousFuel)
      }
      toast.error('Failed to update fuel record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Fuel record updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.fuel() })
    },
  })
}

export function useDeleteFuel() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteFuel(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.fuel() })
      
      const previousFuel = queryClient.getQueryData(queryKeys.fuel())
      
      queryClient.setQueryData(queryKeys.fuel(), (old: any) => {
        if (!old) return []
        return old.filter((record: any) => record.ID !== id)
      })
      
      return { previousFuel }
    },
    onError: (error, id, context) => {
      if (context?.previousFuel) {
        queryClient.setQueryData(queryKeys.fuel(), context.previousFuel)
      }
      toast.error('Failed to delete fuel record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Fuel record deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.fuel() })
    },
  })
}

// Search and filter hooks
export function useFilteredFuel(fuelRecords: FuelRecord[] | undefined, filters: {
  vehicleId?: string
  station?: string
  dateRange?: { start: string; end: string }
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.fuel(), 'filtered', filters],
    queryFn: () => {
      if (!fuelRecords) {
        return []
      }

      let filtered = fuelRecords
      
      if (filters.vehicleId) {
        filtered = filtered.filter(f => f['Vehicle ID'] === filters.vehicleId)
      }
      
      if (filters.station && filters.station !== 'all') {
        filtered = filtered.filter(f => f.Station === filters.station)
      }
      
      if (filters.dateRange) {
        const startDate = new Date(filters.dateRange.start)
        const endDate = new Date(filters.dateRange.end)
        filtered = filtered.filter(f => {
          const fuelDate = new Date(f.Date)
          return fuelDate >= startDate && fuelDate <= endDate
        })
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(f =>
          f.Station.toLowerCase().includes(searchTermLower) ||
          f['Vehicle ID'].toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered.sort((a, b) =>
        new Date(b.Date).getTime() - new Date(a.Date).getTime()
      )
    },
    enabled: !!fuelRecords,
    staleTime: 3 * 60 * 1000,
  })
}

// Fuel statistics hook
export function useFuelStats(fuelRecords: FuelRecord[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.fuel(), 'stats'],
    queryFn: () => {
      if (!fuelRecords) {
        return {
          totalRecords: 0,
          totalQuantity: 0,
          totalCost: 0,
          totalDistance: 0,
          averageConsumption: 0,
          thisMonth: { records: 0, quantity: 0, cost: 0, distance: 0 },
          lastMonth: { records: 0, quantity: 0, cost: 0, distance: 0 },
          averageCostPerLiter: 0,
        }
      }

      const today = new Date()
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      
      const thisMonthRecords = fuelRecords.filter(f => {
        const fuelDate = new Date(f.Date)
        return fuelDate >= thisMonth
      })
      
      const lastMonthRecords = fuelRecords.filter(f => {
        const fuelDate = new Date(f.Date)
        return fuelDate >= lastMonth && fuelDate < thisMonth
      })
      
      return {
        totalRecords: fuelRecords.length,
        totalQuantity: fuelRecords.reduce((sum, f) => sum + (f['Quantity (L)'] || 0), 0),
        totalCost: fuelRecords.reduce((sum, f) => sum + (f.Cost || 0), 0),
        totalDistance: fuelRecords.reduce((sum, f) => sum + (f['Distance (km)'] || 0), 0),
        averageConsumption: (() => {
          const recordsWithConsumption = fuelRecords.filter(f => f['Consumption (L/100km)'])
          const totalConsumption = recordsWithConsumption.reduce((sum, f) => sum + f['Consumption (L/100km)'], 0)
          return recordsWithConsumption.length > 0 ? totalConsumption / recordsWithConsumption.length : 0
        })(),
        thisMonth: {
          records: thisMonthRecords.length,
          quantity: thisMonthRecords.reduce((sum, f) => sum + (f['Quantity (L)'] || 0), 0),
          cost: thisMonthRecords.reduce((sum, f) => sum + (f.Cost || 0), 0),
          distance: thisMonthRecords.reduce((sum, f) => sum + (f['Distance (km)'] || 0), 0),
        },
        lastMonth: {
          records: lastMonthRecords.length,
          quantity: lastMonthRecords.reduce((sum, f) => sum + (f['Quantity (L)'] || 0), 0),
          cost: lastMonthRecords.reduce((sum, f) => sum + (f.cost || 0), 0),
          distance: lastMonthRecords.reduce((sum, f) => sum + (f.distance_km || 0), 0),
        },
        averageCostPerLiter: (() => {
          const totalQuantity = fuelRecords.reduce((sum, f) => sum + (f.quantity_liters || 0), 0)
          const totalCost = fuelRecords.reduce((sum, f) => sum + (f.cost || 0), 0)
          return totalQuantity > 0 ? totalCost / totalQuantity : 0
        })(),
      }
    },
    enabled: !!fuelRecords,
    staleTime: 2 * 60 * 1000,
  })
}

// Vehicle fuel history hook
export function useVehicleFuelHistory(fuelRecords: FuelRecord[] | undefined, vehicleId: string) {
  return useQuery({
    queryKey: [...queryKeys.fuel(), 'vehicle', vehicleId],
    queryFn: () => {
      if (!fuelRecords) {
        return []
      }
      return fuelRecords
        .filter(f => f.vehicle_id === vehicleId)
        .sort((a, b) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )
    },
    enabled: !!vehicleId && !!fuelRecords,
    staleTime: 5 * 60 * 1000,
  })
}

// Fuel consumption analysis hook
export function useFuelConsumptionAnalysis(fuelRecords: FuelRecord[] | undefined, vehicleId?: string) {
  return useQuery({
    queryKey: [...queryKeys.fuel(), 'consumption-analysis', vehicleId],
    queryFn: () => {
      if (!fuelRecords) {
        return []
      }

      let filteredFuel = fuelRecords
      if (vehicleId) {
        filteredFuel = fuelRecords.filter(f => f.vehicle_id === vehicleId)
      }
      
      // Group by vehicle
      const vehicleConsumption = filteredFuel.reduce((acc, record) => {
        const vehicleId = record.vehicle_id
        if (!acc[vehicleId]) {
          acc[vehicleId] = {
            vehicleId,
            records: [],
            totalQuantity: 0,
            totalDistance: 0,
            totalCost: 0,
            averageConsumption: 0,
          }
        }
        
        acc[vehicleId].records.push(record)
        acc[vehicleId].totalQuantity += record.quantity_liters || 0
        acc[vehicleId].totalDistance += record.distance_km || 0
        acc[vehicleId].totalCost += record.cost || 0
        
        return acc
      }, {} as Record<string, any>)
      
      // Calculate averages
      Object.values(vehicleConsumption).forEach((vehicle: any) => {
        if (vehicle.totalDistance > 0) {
          vehicle.averageConsumption = (vehicle.totalQuantity / vehicle.totalDistance) * 100
        }
      })
      
      return Object.values(vehicleConsumption)
    },
    enabled: !!fuelRecords,
    staleTime: 5 * 60 * 1000,
  })
}

// Fuel stations hook
export function useFuelStations(fuelRecords: FuelRecord[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.fuel(), 'stations'],
    queryFn: () => {
      if (!fuelRecords) {
        return []
      }

      const stations = fuelRecords.reduce((acc, record) => {
        const station = record.station
        if (!acc[station]) {
          acc[station] = {
            name: station,
            records: 0,
            totalQuantity: 0,
            totalCost: 0,
            averagePrice: 0,
          }
        }
        
        acc[station].records += 1
        acc[station].totalQuantity += record.quantity_liters || 0
        acc[station].totalCost += record.cost || 0
        
        return acc
      }, {} as Record<string, any>)
      
      // Calculate average prices
      Object.values(stations).forEach((station: any) => {
        if (station.totalQuantity > 0) {
          station.averagePrice = station.totalCost / station.totalQuantity
        }
      })
      
      return Object.values(stations).sort((a: any, b: any) => b.records - a.records)
    },
    enabled: !!fuelRecords,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}