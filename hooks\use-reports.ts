import { useQuery } from '@tanstack/react-query'
import { supabaseApiClient } from "@/lib/supabase-api-client"
import { queryKeys } from '@/lib/react-query'

// Vehicle reports
export function useVehicleReport(filters: {
  vehicle_status?: string
  type?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: queryKeys.vehicleReport(filters),
    queryFn: async () => {
      const vehicles = await supabaseApiClient.getVehicles()
      
      let filteredVehicles = vehicles
      
      if (filters.vehicle_status && filters.vehicle_status !== 'all') {
        filteredVehicles = filteredVehicles.filter(v => v.vehicle_status === filters.vehicle_status)
      }
      
      if (filters.type && filters.type !== 'all') {
        filteredVehicles = filteredVehicles.filter(v => v.vehicle_type === filters.type)
      }
      
      if (filters.dateRange) {
        const startDate = new Date(filters.dateRange.start)
        const endDate = new Date(filters.dateRange.end)
        filteredVehicles = filteredVehicles.filter(v => {
          const createdDate = new Date(v.created_at)
          return createdDate >= startDate && createdDate <= endDate
        })
      }
      
      // Calculate statistics
      const stats = {
        totalVehicles: filteredVehicles.length,
        activeVehicles: filteredVehicles.filter(v => v.status === 'Active').length,
        maintenanceVehicles: filteredVehicles.filter(v => v.status === 'Maintenance').length,
        inactiveVehicles: filteredVehicles.filter(v => v.status === 'Inactive').length,
        avgKilometers: filteredVehicles.reduce((sum, v) => sum + (v.current_km || 0), 0) / filteredVehicles.length,
        upcomingMaintenance: filteredVehicles.filter(v => {
          const currentKM = v.current_km || 0
          const nextMaintenanceKM = v.next_maintenance_km || 0
          return (nextMaintenanceKM - currentKM) <= 1000 && (nextMaintenanceKM - currentKM) > 0
        }).length,
        overdueMaintenance: filteredVehicles.filter(v => {
          const currentKM = v.current_km || 0
          const nextMaintenanceKM = v.next_maintenance_km || 0
          return (nextMaintenanceKM - currentKM) <= 0
        }).length,
        vehiclesByType: filteredVehicles.reduce((acc, v) => {
          acc[v.vehicle_type] = (acc[v.vehicle_type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        vehiclesByYear: filteredVehicles.reduce((acc, v) => {
          const year = v.model || 'Unknown' // Assuming 'model' represents the year
          acc[year] = (acc[year] || 0) + 1
          return acc
        }, {} as Record<string | number, number>),
        fuelTypeDistribution: filteredVehicles.reduce((acc, v) => {
          acc[v.fuel_type] = (acc[v.fuel_type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
      }
      
      return {
        vehicles: filteredVehicles,
        statistics: stats,
        generatedAt: new Date().toISOString(),
      }
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

// Maintenance reports
export function useMaintenanceReport(filters: {
  maintenance_status?: string
  type?: string
  vehicle_id?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: [...queryKeys.reports(), 'maintenance', filters],
    queryFn: async () => {
      const maintenance = await supabaseApiClient.getMaintenance()
      const vehicles = await supabaseApiClient.getVehicles()
      
      let filteredMaintenance = maintenance
      
      if (filters.maintenance_status && filters.maintenance_status !== 'all') {
        filteredMaintenance = filteredMaintenance.filter(m => m.maintenance_status === filters.maintenance_status)
      }
      
      if (filters.type && filters.type !== 'all') {
        filteredMaintenance = filteredMaintenance.filter(m => m.maintenance_type === filters.type)
      }

      if (filters.vehicleId) {
        filteredMaintenance = filteredMaintenance.filter(m => m.vehicle_id === filters.vehicleId)
      }

      if (filters.dateRange) {
        const startDate = new Date(filters.dateRange.start)
        const endDate = new Date(filters.dateRange.end)
        filteredMaintenance = filteredMaintenance.filter(m => {
          const scheduledDate = new Date(m.scheduled_date)
          return scheduledDate >= startDate && scheduledDate <= endDate
        })
      }
      
      // Calculate statistics
      const completedMaintenance = filteredMaintenance.filter(m => m.maintenance_status === 'Completed')
      const totalCost = completedMaintenance.reduce((sum, m) => sum + (m.cost || 0), 0)

      const stats = {
        totalRecords: filteredMaintenance.length,
        pending: filteredMaintenance.filter(m => m.maintenance_status === 'Pending').length,
        inProgress: filteredMaintenance.filter(m => m.maintenance_status === 'In Progress').length,
        completed: completedMaintenance.length,
        cancelled: filteredMaintenance.filter(m => m.maintenance_status === 'Cancelled').length,
        totalCost,
        averageCost: completedMaintenance.length > 0 ? totalCost / completedMaintenance.length : 0,
        maintenanceByType: filteredMaintenance.reduce((acc, m) => {
          acc[m.maintenance_type] = (acc[m.maintenance_type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        maintenanceByVehicle: filteredMaintenance.reduce((acc, m) => {
          const vehicle = vehicles.find(v => v.id === m.vehicle_id)
          const vehicleName = vehicle ? vehicle.vin_number : m.vehicle_id // Using vin_number as serial number
          acc[vehicleName] = (acc[vehicleName] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        costByType: completedMaintenance.reduce((acc, m) => {
          acc[m.maintenance_type] = (acc[m.maintenance_type] || 0) + (m.cost || 0)
          return acc
        }, {} as Record<string, number>),
      }
      
      return {
        maintenance: filteredMaintenance,
        statistics: stats,
        generatedAt: new Date().toISOString(),
      }
    },
    staleTime: 3 * 60 * 1000,
  })
}

// Fuel reports
export function useFuelReport(filters: {
  vehicleId?: string
  station?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: [...queryKeys.reports(), 'fuel', filters],
    queryFn: async () => {
      const fuel = await supabaseApiClient.getFuel()
      const vehicles = await supabaseApiClient.getVehicles()
      
      let filteredFuel = fuel
      
      if (filters.vehicleId) {
        filteredFuel = filteredFuel.filter(f => f.vehicle_id === filters.vehicleId)
      }

      if (filters.station && filters.station !== 'all') {
        filteredFuel = filteredFuel.filter(f => f.station === filters.station)
      }

      if (filters.dateRange) {
        const startDate = new Date(filters.dateRange.start)
        const endDate = new Date(filters.dateRange.end)
        filteredFuel = filteredFuel.filter(f => {
          const fuelDate = new Date(f.date)
          return fuelDate >= startDate && fuelDate <= endDate
        })
      }
      
      // Calculate statistics
      const totalQuantity = filteredFuel.reduce((sum, f) => sum + (f.quantity_liters || 0), 0)
      const totalCost = filteredFuel.reduce((sum, f) => sum + (f.cost || 0), 0)
      const totalDistance = filteredFuel.reduce((sum, f) => sum + (f.distance_km || 0), 0)
      
      const stats = {
        totalRecords: filteredFuel.length,
        totalQuantity,
        totalCost,
        totalDistance,
        averageCostPerLiter: totalQuantity > 0 ? totalCost / totalQuantity : 0,
        averageConsumption: (() => {
          const recordsWithConsumption = filteredFuel.filter(f => f.quantity_liters && f.distance_km && f.quantity_liters > 0 && f.distance_km > 0)
          const totalConsumption = recordsWithConsumption.reduce((sum, f) => sum + ((f.quantity_liters / f.distance_km) * 100), 0)
          return recordsWithConsumption.length > 0 ? totalConsumption / recordsWithConsumption.length : 0
        })(),
        fuelByVehicle: filteredFuel.reduce((acc, f) => {
          const vehicle = vehicles.find(v => v.id === f.vehicle_id)
          const vehicleName = vehicle ? vehicle.vin_number : f.vehicle_id // Using vin_number as serial number
          if (!acc[vehicleName]) {
            acc[vehicleName] = { quantity: 0, cost: 0, distance: 0, records: 0 }
          }
          acc[vehicleName].quantity += f.quantity_liters || 0
          acc[vehicleName].cost += f.cost || 0
          acc[vehicleName].distance += f.distance_km || 0
          acc[vehicleName].records += 1
          return acc
        }, {} as Record<string, any>),
        fuelByStation: filteredFuel.reduce((acc, f) => {
          if (!acc[f.station]) {
            acc[f.station] = { quantity: 0, cost: 0, records: 0 }
          }
          acc[f.station].quantity += f.quantity_liters || 0
          acc[f.station].cost += f.cost || 0
          acc[f.station].records += 1
          return acc
        }, {} as Record<string, any>),
        monthlyTrend: (() => {
          const monthlyData = filteredFuel.reduce((acc, f) => {
            const date = new Date(f.date)
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
            if (!acc[monthKey]) {
              acc[monthKey] = { quantity: 0, cost: 0, distance: 0, records: 0 }
            }
            acc[monthKey].quantity += f.quantity_liters || 0
            acc[monthKey].cost += f.cost || 0
            acc[monthKey].distance += f.distance_km || 0
            acc[monthKey].records += 1
            return acc
          }, {} as Record<string, any>)
          
          return Object.entries(monthlyData)
            .map(([month, data]) => ({ month, ...data }))
            .sort((a, b) => a.month.localeCompare(b.month))
        })(),
      }
      
      return {
        fuel: filteredFuel,
        statistics: stats,
        generatedAt: new Date().toISOString(),
      }
    },
    staleTime: 3 * 60 * 1000,
  })
}

// Driver reports
export function useDriverReport(filters: {
  status?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: [...queryKeys.reports(), 'drivers', filters],
    queryFn: async () => {
      const drivers = await supabaseApiClient.getDrivers()
      const vehicles = await supabaseApiClient.getVehicles()
      
      let filteredDrivers = drivers
      
      if (filters.status && filters.status !== 'all') {
        filteredDrivers = filteredDrivers.filter(d => d.status === filters.status)
      }
      
      if (filters.dateRange) {
        const startDate = new Date(filters.dateRange.start)
        const endDate = new Date(filters.dateRange.end)
        filteredDrivers = filteredDrivers.filter(d => {
          const hireDate = new Date(d.hire_date)
          return hireDate >= startDate && hireDate <= endDate
        })
      }
      
      // Calculate statistics
      const today = new Date()
      const expirationWarningDays = 30
      
      const stats = {
        totalDrivers: filteredDrivers.length,
        activeDrivers: filteredDrivers.filter(d => d.status === 'Available').length,
        inactiveDrivers: filteredDrivers.filter(d => d.status === 'Inactive').length,
        assignedDrivers: filteredDrivers.filter(d => d.assigned_vehicle_id).length,
        unassignedDrivers: filteredDrivers.filter(d => !d.assigned_vehicle_id).length,
        expiringSoon: filteredDrivers.filter(d => {
          if (!d.license_expiry) return false
          const expiryDate = new Date(d.license_expiry)
          const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
          return daysUntilExpiry > 0 && daysUntilExpiry <= expirationWarningDays
        }).length,
        expired: filteredDrivers.filter(d => {
          if (!d.license_expiry) return false
          const expiryDate = new Date(d.license_expiry)
          return expiryDate < today
        }).length,
        driverVehicleAssignments: filteredDrivers.map(driver => {
          const vehicle = vehicles.find(v => v.id === driver.assigned_vehicle_id)
          return {
            ...driver,
            vehicleInfo: vehicle ? {
              serialNumber: vehicle.vin_number, // Using vin_number as serial number
              type: vehicle.vehicle_type,
              status: vehicle.status,
            } : null,
          }
        }),
        hiringTrend: (() => {
          const monthlyData = filteredDrivers.reduce((acc, d) => {
            const date = new Date(d.hire_date)
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
            acc[monthKey] = (acc[monthKey] || 0) + 1
            return acc
          }, {} as Record<string, number>)
          
          return Object.entries(monthlyData)
            .map(([month, count]) => ({ month, count }))
            .sort((a, b) => a.month.localeCompare(b.month))
        })(),
      }
      
      return {
        drivers: filteredDrivers,
        statistics: stats,
        generatedAt: new Date().toISOString(),
      }
    },
    staleTime: 3 * 60 * 1000,
  })
}

// Dashboard summary report
export function useDashboardReport() {
  return useQuery({
    queryKey: [...queryKeys.reports(), 'dashboard'],
    queryFn: async () => {
      const [vehicles, drivers, maintenance, fuel] = await Promise.all([
        supabaseApiClient.getVehicles(),
        supabaseApiClient.getDrivers(),
        supabaseApiClient.getMaintenance(),
        supabaseApiClient.getFuel(),
      ])
      
      const today = new Date()
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      
      return {
        vehicles: {
          total: vehicles.length,
          active: vehicles.filter(v => v.status === 'Active').length,
          maintenance: vehicles.filter(v => v.status === 'Maintenance').length,
          inactive: vehicles.filter(v => v.status === 'Inactive').length,
          overdueMaintenance: vehicles.filter(v => {
            const currentKM = v.current_km || 0
            const nextMaintenanceKM = v.next_maintenance_km || 0
            return (nextMaintenanceKM - currentKM) <= 0
          }).length,
        },
        drivers: {
          total: drivers.length,
          active: drivers.filter(d => d.status === 'Available').length,
          assigned: drivers.filter(d => d.assigned_vehicle_id).length,
          expiringSoon: drivers.filter(d => {
            if (!d.license_expiry) return false
            const expiryDate = new Date(d.license_expiry)
            const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
            return daysUntilExpiry > 0 && daysUntilExpiry <= 30
          }).length,
        },
        maintenance: {
          total: maintenance.length,
          pending: maintenance.filter(m => m.maintenance_status === 'Pending').length,
          inProgress: maintenance.filter(m => m.maintenance_status === 'In Progress').length,
          completed: maintenance.filter(m => m.maintenance_status === 'Completed').length,
          thisMonth: maintenance.filter(m => {
            const createdDate = new Date(m.created_at)
            return createdDate >= thisMonth
          }).length,
          totalCost: maintenance
            .filter(m => m.maintenance_status === 'Completed')
            .reduce((sum, m) => sum + (m.cost || 0), 0),
        },
        fuel: {
          totalRecords: fuel.length,
          thisMonth: fuel.filter(f => {
            const fuelDate = new Date(f.date)
            return fuelDate >= thisMonth
          }).length,
          totalCost: fuel.reduce((sum, f) => sum + (f.cost || 0), 0),
          totalQuantity: fuel.reduce((sum, f) => sum + (f.quantity_liters || 0), 0),
          averageConsumption: (() => {
            const recordsWithConsumption = fuel.filter(f => f.quantity_liters && f.distance_km && f.quantity_liters > 0 && f.distance_km > 0)
            const totalConsumption = recordsWithConsumption.reduce((sum, f) => sum + ((f.quantity_liters / f.distance_km) * 100), 0)
            return recordsWithConsumption.length > 0 ? totalConsumption / recordsWithConsumption.length : 0
          })(),
        },
        generatedAt: new Date().toISOString(),
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Export data hook
export function useExportData(type: 'vehicles' | 'drivers' | 'maintenance' | 'fuel') {
  return useQuery({
    queryKey: [...queryKeys.reports(), 'export', type],
    queryFn: async () => {
      let data
      
      switch (type) {
        case 'vehicles':
          data = await supabaseApiClient.getVehicles()
          break
        case 'drivers':
          data = await supabaseApiClient.getDrivers()
          break
        case 'maintenance':
          data = await supabaseApiClient.getMaintenance()
          break
        case 'fuel':
          data = await supabaseApiClient.getFuel()
          break
        default:
          throw new Error('Invalid export type')
      }
      
      return {
        data,
        type,
        exportedAt: new Date().toISOString(),
      }
    },
    enabled: false, // Only run when manually triggered
  })
}