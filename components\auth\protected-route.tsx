"use client"

import type React from "react"
import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { Loader2 } from "lucide-react"

const publicRoutes = ["/login"]
const protectedRoutes = ["/dashboard", "/vehicles", "/maintenance", "/fuel", "/drivers", "/reports", "/users", "/settings"]

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: "Super Admin" | "Manager" | "Employee"
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth()
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading) {
      const isPublicRoute = publicRoutes.includes(pathname)
      const isProtectedRoute = protectedRoutes.some((route) => pathname.startsWith(route))

      if (!user && isProtectedRoute) {
        router.push("/login")
      } else if (user && isPublicRoute) {
        router.push("/dashboard")
      } else if (user && requiredRole && user.role !== requiredRole) {
        // If user doesn't have required role, redirect to dashboard
        router.push("/dashboard")
      }
    }
  }, [user, isLoading, pathname, router, requiredRole])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // Check role access after loading
  if (user && requiredRole && user.role !== requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h1>
          <p className="text-gray-600 mb-4">You don't have permission to access this page.</p>
          <p className="text-sm text-gray-500">Required role: {requiredRole}</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
