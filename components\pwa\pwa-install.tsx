"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { X, Download, Smartphone, Monitor, RefreshCw } from "lucide-react"
import { toast } from "sonner"

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export function PWAInstall() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if app is running in standalone mode
    const checkStandalone = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches ||
                              (window.navigator as any).standalone === true
      setIsStandalone(isStandaloneMode)
      setIsInstalled(isStandaloneMode)
    }

    checkStandalone()

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      setIsInstallable(true)
      
      // Show install prompt after a delay if not already shown
      setTimeout(() => {
        const hasShownPrompt = localStorage.getItem('pwa-install-prompt-shown')
        if (!hasShownPrompt && !isInstalled) {
          setShowInstallPrompt(true)
        }
      }, 10000) // Show after 10 seconds
    }

    // Listen for app installation
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setShowInstallPrompt(false)
      setDeferredPrompt(null)
      
      toast.success('App installed successfully!', {
        description: 'You can now access Fleet Management System from your home screen.'
      })
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [isInstalled])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        localStorage.setItem('pwa-install-prompt-shown', 'true')
        toast.success('Installation started!', {
          description: 'The app is being installed on your device.'
        })
      } else {
        toast.info('Installation cancelled', {
          description: 'You can install the app later from your browser menu.'
        })
      }
      
      setDeferredPrompt(null)
      setIsInstallable(false)
      setShowInstallPrompt(false)
    } catch (error) {
      console.error('Installation failed:', error)
      toast.error('Installation failed', {
        description: 'There was an error installing the app. Please try again.'
      })
    }
  }

  const handleDismiss = () => {
    setShowInstallPrompt(false)
    localStorage.setItem('pwa-install-prompt-shown', 'true')
  }

  const getDeviceType = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    if (/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      return 'mobile'
    }
    return 'desktop'
  }

  const getInstallInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    const deviceType = getDeviceType()
    
    if (userAgent.includes('chrome')) {
      return deviceType === 'mobile' 
        ? 'Tap "Add to Home Screen" in Chrome menu'
        : 'Click the install button in the address bar'
    } else if (userAgent.includes('firefox')) {
      return deviceType === 'mobile'
        ? 'Tap "Add to Home Screen" in Firefox menu'
        : 'Click "Install" in the address bar'
    } else if (userAgent.includes('safari')) {
      return 'Tap Share button and select "Add to Home Screen"'
    } else if (userAgent.includes('edge')) {
      return 'Click "Install" in the address bar'
    }
    
    return 'Use your browser\'s install option'
  }

  // Don't show anything if already installed
  if (isInstalled || isStandalone) {
    return null
  }

  return (
    <>
      {/* Install prompt banner */}
      {showInstallPrompt && (
        <div className="fixed bottom-4 right-4 z-50 max-w-sm animate-in slide-in-from-bottom">
          <Card className="shadow-lg border-0 bg-white">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Download className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-sm">Install App</CardTitle>
                    <CardDescription className="text-xs">
                      Get faster access
                    </CardDescription>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismiss}
                  className="h-8 w-8 p-0 hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <p className="text-xs text-gray-600 mb-3">
                Install Fleet Management System for quick access and offline features.
              </p>
              
              <div className="flex space-x-2">
                <Button
                  onClick={handleInstallClick}
                  size="sm"
                  className="flex-1"
                  disabled={!isInstallable}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Install
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDismiss}
                  className="flex-1"
                >
                  Later
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Install button for header/menu */}
      {isInstallable && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleInstallClick}
          className="text-xs"
        >
          <Download className="h-4 w-4 mr-1" />
          Install App
        </Button>
      )}
    </>
  )
}

// PWA Status component for settings/about page
export function PWAStatus() {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isOnline, setIsOnline] = useState(true)
  const [serviceWorkerStatus, setServiceWorkerStatus] = useState<'active' | 'inactive' | 'updating'>('inactive')

  useEffect(() => {
    // Check installation status
    const checkInstallation = () => {
      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches ||
                              (window.navigator as any).standalone === true
      setIsInstalled(isStandaloneMode)
    }

    // Check online status
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    // Check service worker status
    const checkServiceWorker = () => {
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(registration => {
          if (registration) {
            if (registration.active) {
              setServiceWorkerStatus('active')
            } else if (registration.installing || registration.waiting) {
              setServiceWorkerStatus('updating')
            }
          }
        })
      }
    }

    checkInstallation()
    setIsOnline(navigator.onLine)
    checkServiceWorker()

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const updateServiceWorker = () => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(registration => {
        if (registration) {
          registration.update()
          setServiceWorkerStatus('updating')
          toast.info('Updating app...', {
            description: 'The app is being updated to the latest version.'
          })
        }
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Smartphone className="h-5 w-5 mr-2" />
          App Status
        </CardTitle>
        <CardDescription>
          Progressive Web App features and status
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Installation Status</span>
          <Badge variant={isInstalled ? "default" : "outline"}>
            {isInstalled ? 'Installed' : 'Not Installed'}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Network Status</span>
          <Badge variant={isOnline ? "default" : "destructive"}>
            {isOnline ? 'Online' : 'Offline'}
          </Badge>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Service Worker</span>
          <div className="flex items-center space-x-2">
            <Badge variant={serviceWorkerStatus === 'active' ? "default" : "outline"}>
              {serviceWorkerStatus === 'active' ? 'Active' : 
               serviceWorkerStatus === 'updating' ? 'Updating' : 'Inactive'}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={updateServiceWorker}
              className="h-6 w-6 p-0"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div className="pt-2 border-t">
          <h4 className="text-sm font-medium mb-2">Available Features</h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span>Offline Access</span>
              <Badge variant="outline" className="text-xs">
                {serviceWorkerStatus === 'active' ? 'Available' : 'Limited'}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Background Sync</span>
              <Badge variant="outline" className="text-xs">
                {serviceWorkerStatus === 'active' ? 'Available' : 'Unavailable'}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span>Push Notifications</span>
              <Badge variant="outline" className="text-xs">
                {isInstalled ? 'Available' : 'Limited'}
              </Badge>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}