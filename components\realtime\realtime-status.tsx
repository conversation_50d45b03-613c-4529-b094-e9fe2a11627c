"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { usePerformance } from '@/context/performance-context'
import { realtimeManager } from '@/lib/realtime-manager'
import { 
  Wifi, 
  WifiOff, 
  Activity, 
  Users, 
  Car, 
  Wrench, 
  Fuel,
  Building,
  Signal,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface RealtimeStats {
  isConnected: boolean
  activeSubscriptions: number
  reconnectAttempts: number
  byTable: Record<string, number>
}

export function RealtimeStatus() {
  const { isRealtimeEnabled, toggleRealtime, metrics } = usePerformance()
  const [stats, setStats] = useState<RealtimeStats>({
    isConnected: false,
    activeSubscriptions: 0,
    reconnectAttempts: 0,
    byTable: {}
  })
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)

  // Update stats periodically
  useEffect(() => {
    const updateStats = () => {
      const realtimeStats = realtimeManager.getStats()
      setStats(realtimeStats)
      setLastUpdate(new Date())
    }

    updateStats()
    const interval = setInterval(updateStats, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  // Get connection status
  const getConnectionStatus = () => {
    if (!isRealtimeEnabled) {
      return { status: 'disabled', color: 'gray', icon: WifiOff, text: 'معطل' }
    } else if (stats.isConnected) {
      return { status: 'connected', color: 'green', icon: Wifi, text: 'متصل' }
    } else if (stats.reconnectAttempts > 0) {
      return { status: 'reconnecting', color: 'yellow', icon: Activity, text: 'يعيد الاتصال' }
    } else {
      return { status: 'disconnected', color: 'red', icon: WifiOff, text: 'منقطع' }
    }
  }

  const connectionStatus = getConnectionStatus()
  const StatusIcon = connectionStatus.icon

  // Table icons mapping
  const tableIcons = {
    vehicles: Car,
    drivers: Users,
    maintenance_records: Wrench,
    fuel_records: Fuel,
    branches: Building,
    profiles: Users
  }

  // Table names mapping
  const tableNames = {
    vehicles: 'المركبات',
    drivers: 'السائقين',
    maintenance_records: 'الصيانة',
    fuel_records: 'الوقود',
    branches: 'الفروع',
    profiles: 'المستخدمين'
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse">
            <StatusIcon className={`h-5 w-5 text-${connectionStatus.color}-600`} />
            <CardTitle>حالة التحديثات الفورية</CardTitle>
          </div>
          <Badge 
            variant={
              connectionStatus.status === 'connected' ? 'default' :
              connectionStatus.status === 'reconnecting' ? 'secondary' :
              connectionStatus.status === 'disabled' ? 'outline' : 'destructive'
            }
          >
            {connectionStatus.text}
          </Badge>
        </div>
        <CardDescription>
          مراقبة حالة الاتصال والاشتراكات الفورية
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Connection Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Signal className="h-4 w-4 text-blue-600" />
            <div>
              <div className="text-sm font-medium">حالة الاتصال</div>
              <div className="text-xs text-muted-foreground">
                {stats.isConnected ? 'متصل ونشط' : 'غير متصل'}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <Activity className="h-4 w-4 text-green-600" />
            <div>
              <div className="text-sm font-medium">الاشتراكات النشطة</div>
              <div className="text-xs text-muted-foreground">
                {stats.activeSubscriptions} اشتراك
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <Clock className="h-4 w-4 text-purple-600" />
            <div>
              <div className="text-sm font-medium">آخر تحديث</div>
              <div className="text-xs text-muted-foreground">
                {lastUpdate ? lastUpdate.toLocaleTimeString('ar-SA') : 'غير محدد'}
              </div>
            </div>
          </div>
        </div>

        {/* Reconnection Status */}
        {stats.reconnectAttempts > 0 && (
          <div className="flex items-center space-x-2 space-x-reverse p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <div className="text-sm text-yellow-800">
              محاولة إعادة الاتصال رقم {stats.reconnectAttempts}...
            </div>
          </div>
        )}

        {/* Subscriptions by Table */}
        {Object.keys(stats.byTable).length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-3">الاشتراكات حسب الجدول</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {Object.entries(stats.byTable).map(([table, count]) => {
                const TableIcon = tableIcons[table as keyof typeof tableIcons] || Activity
                const tableName = tableNames[table as keyof typeof tableNames] || table
                
                return (
                  <div 
                    key={table}
                    className="flex items-center space-x-2 space-x-reverse p-2 bg-gray-50 rounded-lg"
                  >
                    <TableIcon className="h-4 w-4 text-gray-600" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium truncate">{tableName}</div>
                      <div className="text-xs text-muted-foreground">{count} اشتراك</div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Control Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-2 space-x-reverse">
            {stats.isConnected ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm text-muted-foreground">
              {stats.isConnected ? 'النظام يعمل بشكل طبيعي' : 'يوجد مشكلة في الاتصال'}
            </span>
          </div>

          <Button
            onClick={toggleRealtime}
            variant={isRealtimeEnabled ? "destructive" : "default"}
            size="sm"
          >
            {isRealtimeEnabled ? (
              <>
                <WifiOff className="h-4 w-4 mr-2" />
                إيقاف
              </>
            ) : (
              <>
                <Wifi className="h-4 w-4 mr-2" />
                تفعيل
              </>
            )}
          </Button>
        </div>

        {/* Performance Impact */}
        {isRealtimeEnabled && (
          <div className="text-xs text-muted-foreground bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center space-x-1 space-x-reverse mb-1">
              <Activity className="h-3 w-3" />
              <span className="font-medium">تأثير على الأداء:</span>
            </div>
            <ul className="space-y-1 mr-4">
              <li>• استهلاك إضافي للبيانات: ~{stats.activeSubscriptions * 0.1} KB/دقيقة</li>
              <li>• تحديث فوري للبيانات عند التغيير</li>
              <li>• تحسين تجربة المستخدم مع البيانات الحديثة</li>
            </ul>
          </div>
        )}

        {/* Troubleshooting */}
        {!stats.isConnected && isRealtimeEnabled && (
          <div className="text-xs text-muted-foreground bg-red-50 p-3 rounded-lg">
            <div className="flex items-center space-x-1 space-x-reverse mb-1">
              <AlertCircle className="h-3 w-3" />
              <span className="font-medium">استكشاف الأخطاء:</span>
            </div>
            <ul className="space-y-1 mr-4">
              <li>• تحقق من اتصال الإنترنت</li>
              <li>• تأكد من صحة إعدادات Supabase</li>
              <li>• جرب إعادة تحميل الصفحة</li>
              <li>• تواصل مع الدعم الفني إذا استمرت المشكلة</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Real-time indicator component for header
export function RealtimeIndicator() {
  const { isRealtimeEnabled, metrics } = usePerformance()

  if (!isRealtimeEnabled) {
    return (
      <div className="flex items-center space-x-1 space-x-reverse text-gray-500">
        <WifiOff className="h-4 w-4" />
        <span className="text-xs">غير متصل</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-1 space-x-reverse">
      {metrics.realtimeConnected ? (
        <>
          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
          <Wifi className="h-4 w-4 text-green-600" />
          <span className="text-xs text-green-600">متصل</span>
        </>
      ) : (
        <>
          <div className="h-2 w-2 bg-red-500 rounded-full" />
          <WifiOff className="h-4 w-4 text-red-600" />
          <span className="text-xs text-red-600">منقطع</span>
        </>
      )}
    </div>
  )
}

export default RealtimeStatus
