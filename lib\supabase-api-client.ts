import { supabase, Database } from './supabase'
import { toast } from 'sonner'

// Type definitions
type Tables = Database['public']['Tables']
type Vehicle = Tables['vehicles']['Row']
type Driver = Tables['drivers']['Row']
type MaintenanceRecord = Tables['maintenance_records']['Row']
type FuelRecord = Tables['fuel_records']['Row']
type Branch = Tables['branches']['Row']
type Profile = Tables['profiles']['Row']
type User = Profile

// Enhanced error handling
class SupabaseAPIError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message)
    this.name = 'SupabaseAPIError'
  }
}

// Helper function for error handling with toast
function handleError(error: any, operation: string) {
  const message = error?.message || 'An unknown error occurred'
  toast.error(`Error ${operation}: ${message}`)
  throw new SupabaseAPIError(`Failed to ${operation}`, error)
}

class SupabaseAPIClient {
  // ==================== VEHICLES ====================
  
  async getVehicles(): Promise<Vehicle[]> {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch vehicles')
      return []
    }
  }

  async getVehicle(id: string): Promise<Vehicle | null> {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      handleError(error, 'fetch vehicle')
      return null
    }
  }

  async addVehicle(vehicleData: Tables['vehicles']['Insert']): Promise<Vehicle> {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .insert(vehicleData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة المركبة بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add vehicle')
      throw error
    }
  }

  async updateVehicle(id: string, vehicleData: Tables['vehicles']['Update']): Promise<Vehicle> {
    try {
      const { data, error } = await supabase
        .from('vehicles')
        .update(vehicleData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث المركبة بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update vehicle')
      throw error
    }
  }

  async deleteVehicle(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('vehicles')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف المركبة بنجاح')
    } catch (error) {
      handleError(error, 'delete vehicle')
      throw error
    }
  }

  // ==================== DRIVERS ====================
  
  async getDrivers(): Promise<Driver[]> {
    try {
      const { data, error } = await supabase
        .from('drivers')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch drivers')
      return []
    }
  }

  async getDriver(id: string): Promise<Driver | null> {
    try {
      const { data, error } = await supabase
        .from('drivers')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      handleError(error, 'fetch driver')
      return null
    }
  }

  async addDriver(driverData: Tables['drivers']['Insert']): Promise<Driver> {
    try {
      const { data, error } = await supabase
        .from('drivers')
        .insert(driverData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة السائق بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add driver')
      throw error
    }
  }

  async updateDriver(id: string, driverData: Tables['drivers']['Update']): Promise<Driver> {
    try {
      const { data, error } = await supabase
        .from('drivers')
        .update(driverData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث السائق بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update driver')
      throw error
    }
  }

  async deleteDriver(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('drivers')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف السائق بنجاح')
    } catch (error) {
      handleError(error, 'delete driver')
      throw error
    }
  }

  // ==================== MAINTENANCE RECORDS ====================
  
  async getMaintenanceRecords(): Promise<MaintenanceRecord[]> {
    try {
      const { data, error } = await supabase
        .from('maintenance_records')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch maintenance records')
      return []
    }
  }

  async addMaintenanceRecord(recordData: Tables['maintenance_records']['Insert']): Promise<MaintenanceRecord> {
    try {
      const { data, error } = await supabase
        .from('maintenance_records')
        .insert(recordData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة سجل الصيانة بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add maintenance record')
      throw error
    }
  }

  async updateMaintenanceRecord(id: string, recordData: Tables['maintenance_records']['Update']): Promise<MaintenanceRecord> {
    try {
      const { data, error } = await supabase
        .from('maintenance_records')
        .update(recordData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث سجل الصيانة بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update maintenance record')
      throw error
    }
  }

  async deleteMaintenanceRecord(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('maintenance_records')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف سجل الصيانة بنجاح')
    } catch (error) {
      handleError(error, 'delete maintenance record')
      throw error
    }
  }

  // ==================== FUEL RECORDS ====================
  
  async getFuelRecords(): Promise<FuelRecord[]> {
    try {
      const { data, error } = await supabase
        .from('fuel_records')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch fuel records')
      return []
    }
  }

  async addFuelRecord(recordData: Tables['fuel_records']['Insert']): Promise<FuelRecord> {
    try {
      const { data, error } = await supabase
        .from('fuel_records')
        .insert(recordData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة سجل الوقود بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add fuel record')
      throw error
    }
  }

  async updateFuelRecord(id: string, recordData: Tables['fuel_records']['Update']): Promise<FuelRecord> {
    try {
      const { data, error } = await supabase
        .from('fuel_records')
        .update(recordData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث سجل الوقود بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update fuel record')
      throw error
    }
  }

  async deleteFuelRecord(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('fuel_records')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف سجل الوقود بنجاح')
    } catch (error) {
      handleError(error, 'delete fuel record')
      throw error
    }
  }

  // ==================== BRANCHES ====================

  async getBranches(): Promise<Branch[]> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch branches')
      return []
    }
  }

  async getBranch(id: string): Promise<Branch | null> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      handleError(error, 'fetch branch')
      return null
    }
  }

  async addBranch(branchData: Tables['branches']['Insert']): Promise<Branch> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .insert(branchData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة الفرع بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add branch')
      throw error
    }
  }

  async updateBranch(id: string, branchData: Tables['branches']['Update']): Promise<Branch> {
    try {
      const { data, error } = await supabase
        .from('branches')
        .update(branchData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث الفرع بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update branch')
      throw error
    }
  }

  async deleteBranch(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('branches')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف الفرع بنجاح')
    } catch (error) {
      handleError(error, 'delete branch')
      throw error
    }
  }

  // ==================== USERS/PROFILES ====================

  async getUsers(): Promise<Profile[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      handleError(error, 'fetch users')
      return []
    }
  }

  async getUser(id: string): Promise<Profile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      handleError(error, 'fetch user')
      return null
    }
  }

  async addUser(userData: Tables['profiles']['Insert']): Promise<Profile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .insert(userData)
        .select()
        .single()

      if (error) throw error
      toast.success('تم إضافة المستخدم بنجاح')
      return data
    } catch (error) {
      handleError(error, 'add user')
      throw error
    }
  }

  async updateUser(id: string, userData: Tables['profiles']['Update']): Promise<Profile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update(userData)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      toast.success('تم تحديث المستخدم بنجاح')
      return data
    } catch (error) {
      handleError(error, 'update user')
      throw error
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', id)

      if (error) throw error
      toast.success('تم حذف المستخدم بنجاح')
    } catch (error) {
      handleError(error, 'delete user')
      throw error
    }
  }

  // ==================== DASHBOARD DATA ====================

  async getDashboardData(): Promise<any[]> {
    try {
      // Get basic statistics for dashboard
      const [vehicles, drivers, maintenanceRecords, fuelRecords] = await Promise.all([
        this.getVehicles(),
        this.getDrivers(),
        this.getMaintenanceRecords(),
        this.getFuelRecords()
      ])

      // Calculate dashboard metrics
      const dashboardData = [
        {
          id: 'vehicles',
          title: 'Total Vehicles',
          value: vehicles.length,
          change: '+0%',
          data: vehicles
        },
        {
          id: 'drivers',
          title: 'Total Drivers',
          value: drivers.length,
          change: '+0%',
          data: drivers
        },
        {
          id: 'maintenance',
          title: 'Maintenance Records',
          value: maintenanceRecords.length,
          change: '+0%',
          data: maintenanceRecords
        },
        {
          id: 'fuel',
          title: 'Fuel Records',
          value: fuelRecords.length,
          change: '+0%',
          data: fuelRecords
        },
        {
          id: 'active_vehicles',
          title: 'Active Vehicles',
          value: vehicles.filter(v => v.vehicle_status === 'Active').length,
          change: '+0%',
          data: vehicles.filter(v => v.vehicle_status === 'Active')
        },
        {
          id: 'maintenance_due',
          title: 'Maintenance Due',
          value: vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
          change: '+0%',
          data: vehicles.filter(v => v.vehicle_status === 'Maintenance')
        }
      ]

      return dashboardData
    } catch (error) {
      handleError(error, 'fetch dashboard data')
      return []
    }
  }

  // ==================== REPORTS ====================

  async getReports(): Promise<any[]> {
    try {
      const [vehicles, maintenanceRecords, fuelRecords] = await Promise.all([
        this.getVehicles(),
        this.getMaintenanceRecords(),
        this.getFuelRecords()
      ])

      // Generate basic reports
      const reports = [
        {
          id: 'vehicle_status',
          title: 'Vehicle Status Report',
          data: vehicles.reduce((acc, vehicle) => {
            acc[vehicle.vehicle_status] = (acc[vehicle.vehicle_status] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        },
        {
          id: 'fuel_consumption',
          title: 'Fuel Consumption Report',
          data: fuelRecords.reduce((acc, record) => {
            const month = new Date(record.date).toISOString().slice(0, 7)
            acc[month] = (acc[month] || 0) + record.quantity_liters
            return acc
          }, {} as Record<string, number>)
        },
        {
          id: 'maintenance_costs',
          title: 'Maintenance Costs Report',
          data: maintenanceRecords.reduce((acc, record) => {
            const month = new Date(record.maintenance_date).toISOString().slice(0, 7)
            acc[month] = (acc[month] || 0) + record.cost
            return acc
          }, {} as Record<string, number>)
        }
      ]

      return reports
    } catch (error) {
      handleError(error, 'fetch reports')
      return []
    }
  }
}

// Export singleton instance
export const supabaseApiClient = new SupabaseAPIClient()
export type { Vehicle, Driver, MaintenanceRecord, FuelRecord, Branch, Profile, User }
