#!/usr/bin/env node

/**
 * Fleet Management System - System Health Check
 * Verifies all components are working correctly
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

async function checkEnvironmentVariables() {
  log('\n🔍 Checking Environment Variables...', 'blue')
  
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY', 
    'SUPABASE_SERVICE_ROLE_KEY'
  ]

  let allPresent = true

  for (const varName of requiredVars) {
    const value = process.env[varName]
    if (value) {
      log(`   ✅ ${varName}: ${value.substring(0, 20)}...`, 'green')
    } else {
      log(`   ❌ ${varName}: Missing`, 'red')
      allPresent = false
    }
  }

  // Check .env.local file
  const envLocalPath = path.join(process.cwd(), '.env.local')
  if (fs.existsSync(envLocalPath)) {
    log('   ✅ .env.local file exists', 'green')
  } else {
    log('   ❌ .env.local file missing', 'red')
    allPresent = false
  }

  return allPresent
}

async function checkSupabaseConnection() {
  log('\n📡 Checking Supabase Connection...', 'blue')

  if (!supabaseUrl || !supabaseAnonKey) {
    log('   ❌ Missing Supabase credentials', 'red')
    return false
  }

  try {
    const supabase = createClient(supabaseUrl, supabaseAnonKey)
    
    // Test basic connection
    const { data, error } = await supabase.from('profiles').select('count').limit(1)
    
    if (error && !error.message.includes('row-level security')) {
      throw error
    }

    log('   ✅ Supabase connection successful', 'green')
    log(`   📍 Project URL: ${supabaseUrl}`, 'blue')
    
    return true
  } catch (error) {
    log(`   ❌ Supabase connection failed: ${error.message}`, 'red')
    return false
  }
}

async function checkDatabaseTables() {
  log('\n🗄️  Checking Database Tables...', 'blue')

  const supabase = createClient(supabaseUrl, supabaseServiceKey)
  const requiredTables = [
    'profiles',
    'branches', 
    'vehicles',
    'drivers',
    'maintenance_records',
    'fuel_records'
  ]

  let allTablesExist = true

  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase.from(table).select('*').limit(1)
      
      if (error && error.message.includes('does not exist')) {
        log(`   ❌ Table '${table}' does not exist`, 'red')
        allTablesExist = false
      } else {
        log(`   ✅ Table '${table}' exists`, 'green')
      }
    } catch (error) {
      log(`   ❌ Error checking table '${table}': ${error.message}`, 'red')
      allTablesExist = false
    }
  }

  return allTablesExist
}

async function checkRLSPolicies() {
  log('\n🔐 Checking RLS Policies...', 'blue')

  const supabase = createClient(supabaseUrl, supabaseAnonKey)
  const tables = ['profiles', 'branches', 'vehicles', 'drivers', 'maintenance_records', 'fuel_records']

  let rlsWorking = true

  for (const table of tables) {
    try {
      // Try to access table without authentication (should fail with RLS)
      const { data, error } = await supabase.from(table).select('*').limit(1)
      
      if (error && error.message.includes('row-level security')) {
        log(`   ✅ RLS enabled on '${table}'`, 'green')
      } else if (error) {
        log(`   ⚠️  RLS status unclear on '${table}': ${error.message}`, 'yellow')
      } else {
        log(`   ⚠️  RLS might not be properly configured on '${table}'`, 'yellow')
      }
    } catch (error) {
      log(`   ❌ Error checking RLS on '${table}': ${error.message}`, 'red')
      rlsWorking = false
    }
  }

  return rlsWorking
}

async function checkAuthUsers() {
  log('\n👥 Checking Test Users...', 'blue')

  const supabase = createClient(supabaseUrl, supabaseServiceKey)
  const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

  let usersExist = 0

  for (const email of testEmails) {
    try {
      const { data, error } = await supabase.auth.admin.listUsers()
      
      if (error) {
        log(`   ❌ Error checking users: ${error.message}`, 'red')
        continue
      }

      const userExists = data.users.some(user => user.email === email)
      
      if (userExists) {
        log(`   ✅ Test user exists: ${email}`, 'green')
        usersExist++
      } else {
        log(`   ❌ Test user missing: ${email}`, 'red')
      }
    } catch (error) {
      log(`   ❌ Error checking user ${email}: ${error.message}`, 'red')
    }
  }

  return usersExist > 0
}

async function checkProjectFiles() {
  log('\n📁 Checking Project Files...', 'blue')

  const requiredFiles = [
    'package.json',
    'next.config.js',
    'tailwind.config.js',
    'app/layout.tsx',
    'app/page.tsx',
    'lib/supabase.ts',
    'supabase/migrations/001_initial_schema.sql',
    'supabase/migrations/002_row_level_security.sql'
  ]

  let allFilesExist = true

  for (const file of requiredFiles) {
    const filePath = path.join(process.cwd(), file)
    if (fs.existsSync(filePath)) {
      log(`   ✅ ${file}`, 'green')
    } else {
      log(`   ❌ ${file} missing`, 'red')
      allFilesExist = false
    }
  }

  return allFilesExist
}

async function checkDependencies() {
  log('\n📦 Checking Dependencies...', 'blue')

  try {
    const packageJsonPath = path.join(process.cwd(), 'package.json')
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

    const requiredDeps = [
      '@supabase/supabase-js',
      'next',
      'react',
      'typescript',
      '@tanstack/react-query'
    ]

    let allDepsInstalled = true

    for (const dep of requiredDeps) {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        log(`   ✅ ${dep}`, 'green')
      } else {
        log(`   ❌ ${dep} missing`, 'red')
        allDepsInstalled = false
      }
    }

    // Check if node_modules exists
    const nodeModulesPath = path.join(process.cwd(), 'node_modules')
    if (fs.existsSync(nodeModulesPath)) {
      log('   ✅ node_modules directory exists', 'green')
    } else {
      log('   ❌ node_modules directory missing - run npm install', 'red')
      allDepsInstalled = false
    }

    return allDepsInstalled
  } catch (error) {
    log(`   ❌ Error checking dependencies: ${error.message}`, 'red')
    return false
  }
}

async function performSystemCheck() {
  log('🏥 Fleet Management System - Health Check', 'bold')
  log('=' .repeat(50), 'blue')

  const checks = [
    { name: 'Environment Variables', fn: checkEnvironmentVariables },
    { name: 'Project Files', fn: checkProjectFiles },
    { name: 'Dependencies', fn: checkDependencies },
    { name: 'Supabase Connection', fn: checkSupabaseConnection },
    { name: 'Database Tables', fn: checkDatabaseTables },
    { name: 'RLS Policies', fn: checkRLSPolicies },
    { name: 'Test Users', fn: checkAuthUsers }
  ]

  const results = []

  for (const check of checks) {
    try {
      const result = await check.fn()
      results.push({ name: check.name, passed: result })
    } catch (error) {
      log(`\n❌ ${check.name} check failed: ${error.message}`, 'red')
      results.push({ name: check.name, passed: false })
    }
  }

  // Summary
  log('\n' + '=' .repeat(50), 'blue')
  log('📊 Health Check Summary', 'bold')
  log('=' .repeat(50), 'blue')

  const passed = results.filter(r => r.passed).length
  const total = results.length

  results.forEach(result => {
    const status = result.passed ? '✅' : '❌'
    const color = result.passed ? 'green' : 'red'
    log(`${status} ${result.name}`, color)
  })

  log(`\n📈 Overall Score: ${passed}/${total} (${Math.round(passed/total*100)}%)`, 'bold')

  if (passed === total) {
    log('\n🎉 All checks passed! System is ready to use.', 'green')
    log('\n🚀 Next steps:', 'blue')
    log('   1. Run: npm run dev', 'blue')
    log('   2. Open: http://localhost:3000', 'blue')
    log('   3. Login with test credentials', 'blue')
  } else {
    log('\n⚠️  Some checks failed. Please review the issues above.', 'yellow')
    log('\n🔧 Suggested fixes:', 'blue')
    
    if (!results.find(r => r.name === 'Environment Variables')?.passed) {
      log('   - Copy .env.example to .env.local and fill in values', 'blue')
    }
    if (!results.find(r => r.name === 'Dependencies')?.passed) {
      log('   - Run: npm install', 'blue')
    }
    if (!results.find(r => r.name === 'Database Tables')?.passed) {
      log('   - Run: node scripts/quick-setup.js', 'blue')
    }
    if (!results.find(r => r.name === 'Test Users')?.passed) {
      log('   - Run: node scripts/quick-setup.js', 'blue')
    }
  }

  return passed === total
}

// Run the health check
if (require.main === module) {
  performSystemCheck().catch(error => {
    log(`\n❌ Health check failed: ${error.message}`, 'red')
    process.exit(1)
  })
}

module.exports = { performSystemCheck }
