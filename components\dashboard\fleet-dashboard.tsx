"use client"

import { useDashboardData } from "@/hooks/use-dashboard-data"
import { KPISection } from "./sections/kpi-section"
import { ChartsSection } from "./sections/charts-section"
import { TablesSection } from "./sections/tables-section"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"

interface FleetDashboardProps {
  onRefresh?: () => void
}

export function FleetDashboard({ onRefresh }: FleetDashboardProps) {
  const { data: dashboardData, isLoading, error } = useDashboardData()

  console.log("🔍 FleetDashboard render - data:", dashboardData?.length, "items")

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading dashboard data...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-red-600 mb-2">Error loading dashboard data</p>
              <p className="text-sm text-muted-foreground">{error.message}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!dashboardData || !Array.isArray(dashboardData) || dashboardData.length === 0) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-muted-foreground">No dashboard data available</p>
              <p className="text-sm text-muted-foreground mt-1">
                Try syncing data from the sync button above
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  console.log("✅ FleetDashboard - Rendering with", dashboardData.length, "vehicles")

  return (
    <div className="space-y-8">
      {/* KPI Cards Section */}
      <KPISection data={dashboardData} />
      
      {/* Charts Section */}
      <ChartsSection data={dashboardData} />
      
      {/* Tables Section */}
      <TablesSection data={dashboardData} />
    </div>
  )
}