"use client"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingButton } from "@/components/ui/loading-button"

import { supabaseApiClient, type User, type Branch } from "@/lib/supabase-api-client"
import { useBranches } from "@/hooks/use-branches"

const userSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  role: z.string().min(1, "Role is required"),
  branchId: z.string().optional(),
  password: z.string().min(6, "Password must be at least 6 characters").optional(),
  status: z.string().optional(), // Add status to schema
  lastLogin: z.string().optional(), // Add lastLogin to schema
})

type UserFormData = z.infer<typeof userSchema>

interface UserFormProps {
  user?: User
  onSuccess: () => void
  onCancel: () => void
}

export function UserForm({ user, onSuccess, onCancel }: UserFormProps) {
  const { data: branches = [], isLoading: loadingBranches, error: branchesError } = useBranches()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      fullName: user?.full_name || "",
      email: user?.email || "",
      role: user?.role || "Super Admin",
      branchId: user?.branch_id || "none",
      password: "",
    },
  })

  const watchedRole = watch("role")

  // const { execute, isLoading } = useAsyncOperation({
  //   successMessage: user ? "User updated successfully" : "User created successfully",
  //   onSuccess: () => {
  //     reset()
  //     onSuccess()
  //   },
  // })
  const [isLoading, setIsLoading] = useState(false)

  const onSubmit = async (data: UserFormData) => {
    setIsLoading(true)
    try {
      const userData: any = {
        full_name: data.fullName,
        email: data.email,
        role: data.role,
        branch_id: data.role === "Super Admin" ? undefined : data.branchId !== "none" ? data.branchId : undefined,
        user_status: "Active",
      }

      // Add password for new users
      if (!user && data.password) {
        userData.password = data.password
      }

      if (user) {
        await supabaseApiClient.updateUser(user.id, userData)
      } else {
        await supabaseApiClient.addUser(userData)
      }

      reset()
      onSuccess()
    } catch (error) {
      console.error('Error saving user:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="fullName" className="text-gray-700">
            Full Name *
          </Label>
          <Input
            id="fullName"
            autoComplete="name"
            {...register("fullName")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.fullName ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
            placeholder="Enter full name"
          />
          {errors.fullName && <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>}
        </div>

        <div>
          <Label htmlFor="email" className="text-gray-700">
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            autoComplete="email"
            {...register("email")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.email ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
            placeholder="<EMAIL>"
          />
          {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
        </div>

        <div>
          <Label htmlFor="role" className="text-gray-700">
            Role *
          </Label>
          <Select 
            value={watchedRole || "Super Admin"} 
            onValueChange={(value) => setValue("role", value)}
          >
            {" "}
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Super Admin">Super Admin</SelectItem>
              <SelectItem value="Manager">Manager</SelectItem>
              <SelectItem value="Employee">Employee</SelectItem>
            </SelectContent>
          </Select>
          {errors.role && <p className="text-red-500 text-sm mt-1">{errors.role.message}</p>}
        </div>

        {watchedRole !== "Super Admin" && (
          <div>
            <Label htmlFor="branchId" className="text-gray-700">
              Branch
            </Label>
            <Select
              value={watch("branchId") || "none"}
              onValueChange={(value) => setValue("branchId", value)}
              disabled={loadingBranches}
            >
              <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder={loadingBranches ? "Loading branches..." : "Select branch"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No specific branch</SelectItem>
                {branchesError ? (
                  <SelectItem value="error" disabled>
                    Error loading branches
                  </SelectItem>
                ) : (
                  branches.map((branch) => (
                    <SelectItem key={branch.branch_id} value={branch.branch_id}>
                      {branch.name} - {branch.location}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        )}

        {!user && (
          <div>
            <Label htmlFor="password" className="text-gray-700">
              Temporary Password *
            </Label>
            <Input
              id="password"
              type="password"
              autoComplete="new-password"
              {...register("password")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.password ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="Temporary password (min 6 characters)"
            />
            {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
          </div>
        )}
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <LoadingButton type="submit" loading={isLoading} className="gradient-bg-primary text-blue-700 hover:opacity-90">
          {user ? "Update User" : "Create User"}
        </LoadingButton>
      </div>
    </form>
  )
}
