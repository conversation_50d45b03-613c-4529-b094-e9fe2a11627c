"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { VehicleForm } from "@/components/forms/vehicle-form"
import { useVehicles, useFilteredVehicles, useVehicleStats, useDeleteVehicle } from "@/hooks/use-vehicles"
import { type Vehicle } from "@/lib/supabase-api-client"
import { Plus, <PERSON>, Filter, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car } from "lucide-react"

export default function VehiclesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingVehicle, setEditingVehicle] = useState<Vehicle | null>(null)
  const [deletingVehicle, setDeletingVehicle] = useState<Vehicle | null>(null)

  // React Query hooks
  const { data: allVehicles, isLoading: loadingAllVehicles, error: allVehiclesError } = useVehicles()

  const { data: filteredVehicles = [], isLoading: loadingFilteredVehicles, error: filteredVehiclesError } = useFilteredVehicles(
    allVehicles,
    {
      vehicle_status: statusFilter,
      searchTerm,
    }
  )

  const { data: vehicleStats, isLoading: loadingVehicleStats, error: vehicleStatsError } = useVehicleStats(allVehicles)
  const deleteVehicleMutation = useDeleteVehicle()

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "maintenance":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getMaintenanceStatus = (vehicle: Vehicle) => {
    const currentKM = vehicle.current_km || 0
    // Assuming next maintenance is 5000km from current
    const nextMaintenanceKM = currentKM + 5000
    const kmUntilMaintenance = nextMaintenanceKM - currentKM

    if (kmUntilMaintenance <= 0) {
      return { status: "Overdue", color: "text-red-600", icon: AlertTriangle }
    } else if (kmUntilMaintenance <= 1000) {
      return { status: "Due Soon", color: "text-yellow-600", icon: AlertTriangle }
    } else {
      return { status: "Good", color: "text-green-600", icon: CheckCircle }
    }
  }

  const handleEdit = (vehicle: Vehicle) => {
    setEditingVehicle(vehicle)
  }

  const handleDelete = (vehicle: Vehicle) => {
    setDeletingVehicle(vehicle)
  }

  const confirmDelete = () => {
    if (deletingVehicle) {
      deleteVehicleMutation.mutate(deletingVehicle.vehicle_id, {
        onSuccess: () => {
          setDeletingVehicle(null)
        },
      })
    }
  }

  const handleFormSuccess = () => {
    setIsAddDialogOpen(false)
    setEditingVehicle(null)
  }

  // Show error state if needed
  // Combine loading and error states
  const loadingVehicles = loadingAllVehicles || loadingFilteredVehicles || loadingVehicleStats;
  const error = allVehiclesError || filteredVehiclesError || vehicleStatsError;

  if (error) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Error loading vehicles</h2>
              <p className="text-gray-600 mb-4">There was a problem loading the vehicle data.</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Vehicle Management</h1>
              <p className="text-gray-600 mt-2">Manage your fleet vehicles and their information</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Vehicle
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Vehicles</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Car className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{filteredVehicles.length}</div>
                <p className="text-xs text-gray-600 mt-1">In your fleet</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Active Vehicles</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {vehicleStats?.active || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Currently operational</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">In Maintenance</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-warning">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {vehicleStats?.maintenance || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Under service</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Overdue Maintenance</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-danger">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {vehicleStats?.overdueMaintenance || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Requires attention</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Due Soon</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Car className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {vehicleStats?.dueSoon || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Maintenance upcoming</p>
              </CardContent>
            </Card>
          </div>

          {/* Vehicles Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">Fleet Vehicles</CardTitle>
              <CardDescription className="text-gray-600">Manage and monitor all vehicles in your fleet</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search vehicles by plate number, vehicle type, or status..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Maintenance">Maintenance</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                  <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingVehicles ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading vehicles...</span>
                </div>
              ) : (
                /* Vehicles Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Plate Number</TableHead>
                        <TableHead className="text-gray-700">Vehicle Type</TableHead>
                        <TableHead className="text-gray-700">Model</TableHead>
                        <TableHead className="text-gray-700">Status</TableHead>
                        <TableHead className="text-gray-700">Current KM</TableHead>
                        <TableHead className="text-gray-700">Maintenance Status</TableHead>
                        <TableHead className="text-gray-700">Fuel Type</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredVehicles.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                            {searchTerm || statusFilter !== "all"
                              ? "No vehicles found matching your criteria"
                              : "No vehicles added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredVehicles.map((vehicle) => {
                          const maintenanceStatus = getMaintenanceStatus(vehicle)
                          const MaintenanceIcon = maintenanceStatus.icon

                          return (
                            <TableRow key={vehicle.vehicle_id} className="border-gray-100 hover:bg-blue-50/30">
                              <TableCell className="font-medium text-gray-800">{vehicle.plate_number}</TableCell>
                              <TableCell className="text-gray-700">{vehicle.vehicle_type}</TableCell>
                              <TableCell className="text-gray-700">{vehicle.model}</TableCell>
                              <TableCell>
                                <Badge className={getStatusColor(vehicle.vehicle_status)}>{vehicle.vehicle_status}</Badge>
                              </TableCell>
                              <TableCell className="text-gray-700">
                                {(vehicle.current_km || 0).toLocaleString()} km
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <MaintenanceIcon className={`h-4 w-4 mr-2 ${maintenanceStatus.color}`} />
                                  <span className={`text-sm ${maintenanceStatus.color}`}>
                                    {maintenanceStatus.status}
                                  </span>
                                </div>
                              </TableCell>
                              <TableCell className="text-gray-700">{vehicle.fuel_type}</TableCell>
                              <TableCell>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEdit(vehicle)}
                                    className="hover:bg-blue-100 hover:text-blue-700"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDelete(vehicle)}
                                    className="hover:bg-red-100 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add Vehicle Dialog */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[900px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg overflow-y-auto max-h-[90vh]">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Add New Vehicle</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Add a new vehicle to your fleet management system.
                </DialogDescription>
              </DialogHeader>
              <VehicleForm onSuccess={handleFormSuccess} onCancel={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          {/* Edit Vehicle Dialog */}
          <Dialog open={!!editingVehicle} onOpenChange={() => setEditingVehicle(null)}>
            <DialogContent className="sm:max-w-[900px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg overflow-y-auto max-h-[90vh]">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Edit Vehicle</DialogTitle>
                <DialogDescription className="text-gray-600">Update the vehicle information below.</DialogDescription>
              </DialogHeader>
              {editingVehicle && (
                <VehicleForm
                  vehicle={editingVehicle}
                  onSuccess={handleFormSuccess}
                  onCancel={() => setEditingVehicle(null)}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <ConfirmationDialog
            open={!!deletingVehicle}
            onOpenChange={() => setDeletingVehicle(null)}
            title="Delete Vehicle"
            description={`Are you sure you want to delete vehicle "${deletingVehicle?.plate_number}"? This action cannot be undone.`}
            confirmText="Delete Vehicle"
            onConfirm={confirmDelete}
            variant="destructive"
            loading={deleteVehicleMutation.isPending}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
