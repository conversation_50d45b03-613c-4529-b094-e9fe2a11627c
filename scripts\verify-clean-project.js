#!/usr/bin/env node

/**
 * Fleet Management System - Project Cleanliness Verification
 * Verifies that all Google Sheets references have been removed
 * and the project is clean and Supabase-only
 */

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Search for Google Sheets references in files
function searchInFile(filePath, searchTerms) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    const found = []
    
    for (const term of searchTerms) {
      const regex = new RegExp(term, 'gi')
      const matches = content.match(regex)
      if (matches) {
        found.push({ term, count: matches.length })
      }
    }
    
    return found
  } catch (error) {
    return []
  }
}

// Get all files recursively
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath)

  files.forEach(file => {
    const fullPath = path.join(dirPath, file)
    
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and .git
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(file)) {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles)
      }
    } else {
      // Only check relevant file types
      if (/\.(js|ts|tsx|jsx|md|json|env)$/.test(file)) {
        arrayOfFiles.push(fullPath)
      }
    }
  })

  return arrayOfFiles
}

// Verify project cleanliness
function verifyProjectCleanliness() {
  log('🧹 Fleet Management System - Project Cleanliness Verification', 'bold')
  log('=' .repeat(60), 'blue')

  const searchTerms = [
    'google.*sheets',
    'googleapis',
    'google.*apps.*script',
    'migration.*from.*sheets',
    'export.*from.*sheets',
    'sheets.*api',
    'GOOGLE_APPS_SCRIPT',
    'GOOGLE_SERVICE_ACCOUNT',
    'GOOGLE_PRIVATE_KEY'
  ]

  const projectRoot = process.cwd()
  const allFiles = getAllFiles(projectRoot)
  
  log(`\n🔍 Scanning ${allFiles.length} files for Google Sheets references...`, 'blue')
  
  const foundReferences = []
  let totalReferences = 0

  for (const filePath of allFiles) {
    const relativePath = path.relative(projectRoot, filePath)

    // Skip files that are allowed to contain Google Sheets references
    const skipFiles = [
      'verify-clean-project.js',
      'CLEANUP_REPORT.md',
      'update-api-imports.js'
    ]

    if (skipFiles.some(skipFile => relativePath.includes(skipFile))) {
      continue
    }

    const references = searchInFile(filePath, searchTerms)

    if (references.length > 0) {
      foundReferences.push({ file: relativePath, references })
      totalReferences += references.reduce((sum, ref) => sum + ref.count, 0)
    }
  }

  // Check for specific files that should not exist
  log('\n📁 Checking for files that should have been removed...', 'blue')
  
  const shouldNotExist = [
    'scripts/export-from-sheets.js',
    'scripts/google-apps-script.js',
    'scripts/full-migration.js',
    'scripts/migrate-data.js',
    'lib/google-sheets-api.ts',
    'lib/optimized-google-sheets-api.ts',
    'lib/export-utils.ts',
    'docs/DATA_MIGRATION_GUIDE.md',
    'docs/API_LAYER_MIGRATION.md'
  ]

  const existingFiles = []
  for (const filePath of shouldNotExist) {
    if (fs.existsSync(path.join(projectRoot, filePath))) {
      existingFiles.push(filePath)
    }
  }

  // Check package.json for Google-related dependencies
  log('\n📦 Checking package.json for Google-related dependencies...', 'blue')
  
  const packageJsonPath = path.join(projectRoot, 'package.json')
  const googleDependencies = []
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies }
    
    for (const [name, version] of Object.entries(allDeps)) {
      if (name.includes('google') || name.includes('sheets')) {
        googleDependencies.push(`${name}@${version}`)
      }
    }
  }

  // Check scripts in package.json
  log('\n⚙️  Checking package.json scripts...', 'blue')
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  const suspiciousScripts = []
  
  if (packageJson.scripts) {
    for (const [name, command] of Object.entries(packageJson.scripts)) {
      if (command.includes('google') || command.includes('sheets') || command.includes('migration')) {
        suspiciousScripts.push(`${name}: ${command}`)
      }
    }
  }

  // Results summary
  log('\n' + '=' .repeat(60), 'blue')
  log('📊 Project Cleanliness Report', 'bold')
  log('=' .repeat(60), 'blue')

  // File references
  if (foundReferences.length === 0) {
    log('✅ No Google Sheets references found in code', 'green')
  } else {
    log(`❌ Found ${totalReferences} Google Sheets references in ${foundReferences.length} files:`, 'red')
    foundReferences.forEach(({ file, references }) => {
      log(`   📄 ${file}:`, 'yellow')
      references.forEach(ref => {
        log(`      - "${ref.term}" (${ref.count} times)`, 'red')
      })
    })
  }

  // Files that should not exist
  if (existingFiles.length === 0) {
    log('✅ All Google Sheets files have been removed', 'green')
  } else {
    log(`❌ Found ${existingFiles.length} files that should have been removed:`, 'red')
    existingFiles.forEach(file => {
      log(`   📄 ${file}`, 'red')
    })
  }

  // Dependencies
  if (googleDependencies.length === 0) {
    log('✅ No Google-related dependencies found', 'green')
  } else {
    log(`❌ Found ${googleDependencies.length} Google-related dependencies:`, 'red')
    googleDependencies.forEach(dep => {
      log(`   📦 ${dep}`, 'red')
    })
  }

  // Scripts
  if (suspiciousScripts.length === 0) {
    log('✅ No suspicious scripts found in package.json', 'green')
  } else {
    log(`❌ Found ${suspiciousScripts.length} suspicious scripts:`, 'red')
    suspiciousScripts.forEach(script => {
      log(`   ⚙️  ${script}`, 'red')
    })
  }

  // Overall assessment
  const isClean = foundReferences.length === 0 && 
                  existingFiles.length === 0 && 
                  googleDependencies.length === 0 && 
                  suspiciousScripts.length === 0

  log('\n' + '=' .repeat(60), 'blue')
  if (isClean) {
    log('🎉 PROJECT IS CLEAN! ✨', 'green')
    log('All Google Sheets references have been successfully removed.', 'green')
    log('The project now uses Supabase exclusively.', 'green')
  } else {
    log('⚠️  PROJECT NEEDS CLEANING', 'yellow')
    log('Some Google Sheets references still exist and need to be removed.', 'yellow')
  }

  // Recommendations
  log('\n🎯 Next Steps:', 'blue')
  if (isClean) {
    log('1. Run: npm run seed (to apply sample data)', 'blue')
    log('2. Run: npm run dev (to start development)', 'blue')
    log('3. Test all functionality with Supabase', 'blue')
  } else {
    log('1. Remove the files and references listed above', 'blue')
    log('2. Run this verification script again', 'blue')
    log('3. Continue cleaning until project is clean', 'blue')
  }

  return isClean
}

// Check Supabase integration
function checkSupabaseIntegration() {
  log('\n🔍 Verifying Supabase Integration...', 'blue')
  
  const requiredFiles = [
    'lib/supabase.ts',
    'lib/supabase-api-client.ts',
    'supabase/migrations/001_initial_schema.sql',
    'supabase/migrations/002_row_level_security.sql',
    'supabase/seed.sql'
  ]

  const missingFiles = []
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(process.cwd(), file))) {
      missingFiles.push(file)
    }
  }

  if (missingFiles.length === 0) {
    log('✅ All required Supabase files are present', 'green')
  } else {
    log(`❌ Missing ${missingFiles.length} required Supabase files:`, 'red')
    missingFiles.forEach(file => {
      log(`   📄 ${file}`, 'red')
    })
  }

  return missingFiles.length === 0
}

// Main execution
function main() {
  const isClean = verifyProjectCleanliness()
  const hasSupabase = checkSupabaseIntegration()
  
  if (isClean && hasSupabase) {
    log('\n🚀 Project is ready for Supabase-only operation!', 'green')
    process.exit(0)
  } else {
    log('\n🔧 Project needs additional work before it\'s ready.', 'yellow')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { verifyProjectCleanliness, checkSupabaseIntegration }
