import { supabase } from './supabase'
import { RealtimeChannel } from '@supabase/supabase-js'
import { toast } from 'sonner'

// Types for real-time events
export interface RealtimeEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE'
  table: string
  old?: any
  new?: any
  timestamp: string
}

export interface RealtimeSubscription {
  id: string
  table: string
  channel: RealtimeChannel
  callback: (event: RealtimeEvent) => void
  isActive: boolean
}

// Real-time manager class
export class RealtimeManager {
  private subscriptions: Map<string, RealtimeSubscription> = new Map()
  private isConnected: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 1000

  constructor() {
    this.setupConnectionMonitoring()
  }

  // Setup connection monitoring
  private setupConnectionMonitoring() {
    // Monitor connection status using channel events
    const channel = supabase.channel('connection-monitor')

    channel
      .on('system', { event: 'connected' }, () => {
        console.log('✅ Real-time connection established')
        this.isConnected = true
        this.reconnectAttempts = 0
        toast.success('تم الاتصال بالتحديثات الفورية')
      })
      .on('system', { event: 'disconnected' }, () => {
        console.log('❌ Real-time connection closed')
        this.isConnected = false
        this.handleReconnection()
      })
      .on('system', { event: 'error' }, (error: any) => {
        console.error('Real-time connection error:', error)
        this.isConnected = false
        toast.error('خطأ في الاتصال بالتحديثات الفورية')
      })
      .subscribe()
  }

  // Handle reconnection logic
  private async handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      toast.error('فشل في إعادة الاتصال بالتحديثات الفورية')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`)
    
    setTimeout(() => {
      this.resubscribeAll()
    }, delay)
  }

  // Resubscribe to all active subscriptions
  private resubscribeAll() {
    const activeSubscriptions = Array.from(this.subscriptions.values())
      .filter(sub => sub.isActive)

    activeSubscriptions.forEach(sub => {
      this.unsubscribe(sub.id)
      this.subscribe(sub.table, sub.callback, sub.id)
    })
  }

  // Subscribe to table changes
  subscribe(
    table: string, 
    callback: (event: RealtimeEvent) => void,
    subscriptionId?: string
  ): string {
    const id = subscriptionId || `${table}_${Date.now()}_${Math.random()}`

    // Remove existing subscription if it exists
    if (this.subscriptions.has(id)) {
      this.unsubscribe(id)
    }

    try {
      const channel = supabase
        .channel(`${table}_changes_${id}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table
          },
          (payload) => {
            const event: RealtimeEvent = {
              eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
              table: table,
              old: payload.old,
              new: payload.new,
              timestamp: new Date().toISOString()
            }

            console.log(`Real-time event for ${table}:`, event)
            callback(event)
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`✅ Subscribed to ${table} changes`)
          } else if (status === 'CHANNEL_ERROR') {
            console.error(`❌ Failed to subscribe to ${table} changes`)
            toast.error(`فشل في الاشتراك في تحديثات ${table}`)
          }
        })

      const subscription: RealtimeSubscription = {
        id,
        table,
        channel,
        callback,
        isActive: true
      }

      this.subscriptions.set(id, subscription)
      return id
    } catch (error) {
      console.error(`Error subscribing to ${table}:`, error)
      throw error
    }
  }

  // Unsubscribe from table changes
  unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId)
    
    if (!subscription) {
      console.warn(`Subscription ${subscriptionId} not found`)
      return false
    }

    try {
      supabase.removeChannel(subscription.channel)
      subscription.isActive = false
      this.subscriptions.delete(subscriptionId)
      
      console.log(`✅ Unsubscribed from ${subscription.table} changes`)
      return true
    } catch (error) {
      console.error(`Error unsubscribing from ${subscription.table}:`, error)
      return false
    }
  }

  // Subscribe to specific row changes
  subscribeToRow(
    table: string,
    rowId: string,
    callback: (event: RealtimeEvent) => void
  ): string {
    const id = `${table}_row_${rowId}_${Date.now()}`

    try {
      const channel = supabase
        .channel(`${table}_row_${rowId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table,
            filter: `id=eq.${rowId}`
          },
          (payload) => {
            const event: RealtimeEvent = {
              eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
              table: table,
              old: payload.old,
              new: payload.new,
              timestamp: new Date().toISOString()
            }

            callback(event)
          }
        )
        .subscribe()

      const subscription: RealtimeSubscription = {
        id,
        table: `${table}_row_${rowId}`,
        channel,
        callback,
        isActive: true
      }

      this.subscriptions.set(id, subscription)
      return id
    } catch (error) {
      console.error(`Error subscribing to ${table} row ${rowId}:`, error)
      throw error
    }
  }

  // Subscribe to filtered changes
  subscribeWithFilter(
    table: string,
    filter: string,
    callback: (event: RealtimeEvent) => void
  ): string {
    const id = `${table}_filtered_${Date.now()}`

    try {
      const channel = supabase
        .channel(`${table}_filtered_${id}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table,
            filter: filter
          },
          (payload) => {
            const event: RealtimeEvent = {
              eventType: payload.eventType as 'INSERT' | 'UPDATE' | 'DELETE',
              table: table,
              old: payload.old,
              new: payload.new,
              timestamp: new Date().toISOString()
            }

            callback(event)
          }
        )
        .subscribe()

      const subscription: RealtimeSubscription = {
        id,
        table: `${table}_filtered`,
        channel,
        callback,
        isActive: true
      }

      this.subscriptions.set(id, subscription)
      return id
    } catch (error) {
      console.error(`Error subscribing to filtered ${table}:`, error)
      throw error
    }
  }

  // Get connection status
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // Get active subscriptions
  getActiveSubscriptions(): RealtimeSubscription[] {
    return Array.from(this.subscriptions.values()).filter(sub => sub.isActive)
  }

  // Cleanup all subscriptions
  cleanup(): void {
    console.log('Cleaning up all real-time subscriptions')
    
    this.subscriptions.forEach((subscription) => {
      try {
        supabase.removeChannel(subscription.channel)
      } catch (error) {
        console.error(`Error removing channel for ${subscription.table}:`, error)
      }
    })

    this.subscriptions.clear()
    this.isConnected = false
  }

  // Pause all subscriptions
  pauseAll(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.isActive = false
      try {
        supabase.removeChannel(subscription.channel)
      } catch (error) {
        console.error(`Error pausing subscription for ${subscription.table}:`, error)
      }
    })
  }

  // Resume all subscriptions
  resumeAll(): void {
    const pausedSubscriptions = Array.from(this.subscriptions.values())
    
    pausedSubscriptions.forEach((sub) => {
      this.subscribe(sub.table, sub.callback, sub.id)
    })
  }

  // Get subscription statistics
  getStats() {
    const subscriptions = Array.from(this.subscriptions.values())
    
    return {
      total: subscriptions.length,
      active: subscriptions.filter(sub => sub.isActive).length,
      inactive: subscriptions.filter(sub => !sub.isActive).length,
      byTable: subscriptions.reduce((acc, sub) => {
        acc[sub.table] = (acc[sub.table] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

// Export singleton instance
export const realtimeManager = new RealtimeManager()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    realtimeManager.cleanup()
  })
}

export default realtimeManager
