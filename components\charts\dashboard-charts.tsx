"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertTriangle } from "lucide-react"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  ResponsiveContainer,
} from "recharts"
import {
  useVehicleStatusDistribution,
  useVehicleLocationDistribution,
  useVehicleTypeDistribution,
} from "@/hooks/use-dashboard"

// Vehicle Status Chart
export function VehicleStatusChart() {
  const { data: statusData, isLoading, error } = useVehicleStatusDistribution()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Status</CardTitle>
          <CardDescription>Distribution of vehicle statuses</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Status</CardTitle>
          <CardDescription>Distribution of vehicle statuses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Failed to load status data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Status</CardTitle>
        <CardDescription>Distribution of vehicle statuses</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            count: {
              label: "Count",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px]"
        >
          <PieChart>
            <Pie
              data={statusData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ status, count }) => `${status}: ${count}`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {statusData?.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

// Vehicle Location Chart
export function VehicleLocationChart() {
  const { data: locationData, isLoading, error } = useVehicleLocationDistribution()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Locations</CardTitle>
          <CardDescription>Vehicles by branch location</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Locations</CardTitle>
          <CardDescription>Vehicles by branch location</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Failed to load location data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Locations</CardTitle>
        <CardDescription>Vehicles by branch location</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            count: {
              label: "Count",
              color: "hsl(var(--chart-2))",
            },
          }}
          className="h-[300px]"
        >
          <BarChart data={locationData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="location" />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey="count" fill="#8884d8" />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

// Vehicle Types Chart
export function VehicleTypesChart() {
  const { data: typeData, isLoading, error } = useVehicleTypeDistribution()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Types</CardTitle>
          <CardDescription>Distribution by vehicle type</CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Types</CardTitle>
          <CardDescription>Distribution by vehicle type</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Failed to load type data</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#00ff00"]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Types</CardTitle>
        <CardDescription>Distribution by vehicle type</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            count: {
              label: "Count",
              color: "hsl(var(--chart-3))",
            },
          }}
          className="h-[300px]"
        >
          <PieChart>
            <Pie
              data={typeData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ type, count }) => `${type}: ${count}`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {typeData?.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}

// Maintenance Timeline Chart
export function MaintenanceTimelineChart() {
  // Fallback data for maintenance timeline (this would need to be implemented separately)
  const maintenanceTimelineData = [
    { month: "Jan", scheduled: 12, completed: 10 },
    { month: "Feb", scheduled: 15, completed: 14 },
    { month: "Mar", scheduled: 8, completed: 8 },
    { month: "Apr", scheduled: 18, completed: 16 },
    { month: "May", scheduled: 22, completed: 20 },
    { month: "Jun", scheduled: 14, completed: 12 },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Maintenance Timeline</CardTitle>
        <CardDescription>Scheduled vs completed maintenance over time</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            scheduled: {
              label: "Scheduled",
              color: "hsl(var(--chart-1))",
            },
            completed: {
              label: "Completed",
              color: "hsl(var(--chart-2))",
            },
          }}
          className="h-[300px]"
        >
            <LineChart data={maintenanceTimelineData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <ChartTooltip content={<ChartTooltipContent />} />
              <Line
                type="monotone"
                dataKey="scheduled"
                stroke="#8884d8"
                strokeWidth={3}
                dot={{ fill: "#8884d8", strokeWidth: 2, r: 4 }}
              />
              <Line
                type="monotone"
                dataKey="completed"
                stroke="#34D399"
                strokeWidth={3}
                dot={{ fill: "#34D399", strokeWidth: 2, r: 4 }}
              />
            </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}