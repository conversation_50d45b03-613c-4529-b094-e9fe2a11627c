"use client"

import { MainLayout } from "@/components/layout/main-layout"
import { DropdownAdmin } from "@/components/admin/dropdown-admin"
import { DropdownStats } from "@/components/admin/dropdown-stats"
import { BulkActions } from "@/components/admin/bulk-actions"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Database, Upload, Shield } from "lucide-react"

export default function SettingsPage() {
  return (
    <ProtectedRoute requiredRole="Super Admin">
      <MainLayout>
        <div className="space-y-8">
          <div className="gradient-bg-primary p-6 rounded-2xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Database className="h-8 w-8 text-blue-700" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">System Settings</h1>
                <p className="text-gray-600 mt-2">
                  Manage dropdown values and system configuration
                </p>
              </div>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 lg:w-[500px]">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="manage" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Manage
              </TabsTrigger>
              <TabsTrigger value="protection" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Protection
              </TabsTrigger>
              <TabsTrigger value="bulk" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Bulk Actions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <DropdownStats />
            </TabsContent>

            <TabsContent value="manage" className="space-y-6">
              <DropdownAdmin />
            </TabsContent>

            <TabsContent value="protection" className="space-y-6">
              <div className="p-6 bg-white rounded-lg shadow">
                <h3 className="text-lg font-semibold mb-4">Dashboard Protection</h3>
                <p className="text-gray-600">Dashboard protection features are managed through Supabase RLS policies.</p>
              </div>
            </TabsContent>

            <TabsContent value="bulk" className="space-y-6">
              <BulkActions />
            </TabsContent>
          </Tabs>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}