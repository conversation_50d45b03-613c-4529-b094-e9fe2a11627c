"use client"

import { useState, useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/hooks/use-toast"
import { useDropdownValues, type DropdownValue } from "@/hooks/use-dropdown-values"
import { DropdownSearch } from "./dropdown-search"
import { Plus, Edit2, Trash2, Settings, Car, Wrench, Fuel, Palette, Building, Activity } from "lucide-react"

// Define dropdown categories with their icons and descriptions
const DROPDOWN_CATEGORIES = [
  {
    id: "vehicle_type",
    name: "Vehicle Type",
    description: "Types of vehicles in the fleet",
    icon: Car,
    color: "text-blue-600",
    defaultValues: ["Chrysler Pacifica", "Toyota Camry", "Ford F-150", "Mercedes-Benz Sprinter"]
  },
  {
    id: "service_type", 
    name: "Service Type",
    description: "Service categories for vehicles",
    icon: Wrench,
    color: "text-orange-600",
    defaultValues: ["Van", "Sedan", "Truck", "SUV"]
  },
  {
    id: "fuel_type",
    name: "Fuel Type", 
    description: "Available fuel types",
    icon: Fuel,
    color: "text-green-600",
    defaultValues: ["Gasoline 95", "Gasoline 92", "Diesel", "Electric"]
  },
  {
    id: "color",
    name: "Color",
    description: "Available vehicle colors",
    icon: Palette,
    color: "text-pink-600",
    defaultValues: ["White", "Black", "Grey", "Blue", "Red", "Silver"]
  },
  {
    id: "department",
    name: "Department",
    description: "Organizational departments",
    icon: Building,
    color: "text-purple-600",
    defaultValues: ["London Cab", "Airport Shuttle", "City Tour", "Executive Transport"]
  },
  {
    id: "status",
    name: "Status",
    description: "Vehicle status options",
    icon: Activity,
    color: "text-indigo-600",
    defaultValues: ["Active", "Maintenance", "Inactive", "Out of Service"]
  }
]

interface CategoryWithMeta {
  id: string
  name: string
  description: string
  icon: any
  color: string
  values: DropdownValue[]
}

export function DropdownAdmin() {
  const { categories, isLoading: dataLoading, addValue, updateValue, deleteValue, toggleValueStatus } = useDropdownValues()
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null)
  const [editingValue, setEditingValue] = useState<DropdownValue | null>(null)
  const [newValue, setNewValue] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  
  // Search and filter states
  const [searchQuery, setSearchQuery] = useState("")
  const [filterCategory, setFilterCategory] = useState<string | null>(null)
  const [filterStatus, setFilterStatus] = useState<string | null>(null)
  
  const { toast } = useToast()

  // Combine categories with metadata and apply filters
  const categoriesWithMeta: CategoryWithMeta[] = useMemo(() => {
    let filteredCategories = DROPDOWN_CATEGORIES.map(meta => {
      const categoryData = categories.find(cat => cat.id === meta.id)
      return {
        ...meta,
        values: categoryData?.values || []
      }
    })

    // Apply category filter
    if (filterCategory) {
      filteredCategories = filteredCategories.filter(cat => cat.id === filterCategory)
    }

    // Apply search and status filters to values within categories
    filteredCategories = filteredCategories.map(category => ({
      ...category,
      values: category.values.filter(value => {
        // Search filter
        const matchesSearch = !searchQuery || 
          value.value.toLowerCase().includes(searchQuery.toLowerCase())

        // Status filter
        const matchesStatus = !filterStatus || 
          (filterStatus === "active" && value.isActive) ||
          (filterStatus === "inactive" && !value.isActive)

        return matchesSearch && matchesStatus
      })
    }))

    // Remove categories with no matching values when filters are applied
    if (searchQuery || filterStatus) {
      filteredCategories = filteredCategories.filter(cat => cat.values.length > 0)
    }

    return filteredCategories
  }, [categories, searchQuery, filterCategory, filterStatus])

  // Calculate total results for search display
  const totalResults = categoriesWithMeta.reduce((sum, cat) => sum + cat.values.length, 0)

  const handleAddValue = async () => {
    if (!selectedCategoryId || !newValue.trim()) return

    setIsLoading(true)
    try {
      const success = addValue(selectedCategoryId, newValue.trim())
      
      if (success) {
        const categoryName = DROPDOWN_CATEGORIES.find(cat => cat.id === selectedCategoryId)?.name || "category"
        setNewValue("")
        setIsAddDialogOpen(false)
        setSelectedCategoryId(null)
        
        toast({
          title: "Value Added",
          description: `"${newValue}" has been added to ${categoryName}`,
        })
      } else {
        throw new Error("Failed to add value")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add value. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditValue = async () => {
    if (!editingValue || !newValue.trim()) return

    setIsLoading(true)
    try {
      // Find which category this value belongs to
      const categoryId = categoriesWithMeta.find(cat => 
        cat.values.some(val => val.id === editingValue.id)
      )?.id

      if (!categoryId) {
        throw new Error("Category not found")
      }

      const success = updateValue(categoryId, editingValue.id, newValue.trim())
      
      if (success) {
        setNewValue("")
        setIsEditDialogOpen(false)
        setEditingValue(null)
        
        toast({
          title: "Value Updated",
          description: `Value has been updated successfully`,
        })
      } else {
        throw new Error("Failed to update value")
      }
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to update value. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteValue = async (categoryId: string, valueId: string) => {
    setIsLoading(true)
    try {
      const success = deleteValue(categoryId, valueId)
      
      if (success) {
        toast({
          title: "Value Deleted",
          description: "Value has been deleted successfully",
        })
      } else {
        throw new Error("Failed to delete value")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete value. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleStatus = async (categoryId: string, valueId: string) => {
    setIsLoading(true)
    try {
      const success = toggleValueStatus(categoryId, valueId)
      
      if (success) {
        toast({
          title: "Status Updated",
          description: "Value status has been updated successfully",
        })
      } else {
        throw new Error("Failed to update status")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update status. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (dataLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="glass-effect rounded-xl p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-3">
              {Array.from({ length: 4 }).map((_, j) => (
                <div key={j} className="h-12 bg-gray-100 rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <DropdownSearch
        onSearch={setSearchQuery}
        onFilterCategory={setFilterCategory}
        onFilterStatus={setFilterStatus}
        searchQuery={searchQuery}
        selectedCategory={filterCategory}
        selectedStatus={filterStatus}
        totalResults={totalResults}
      />
      
      {categoriesWithMeta.length === 0 ? (
        <div className="glass-effect rounded-xl p-12 text-center">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Settings className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">No Results Found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterCategory || filterStatus
              ? "No dropdown values match your current filters."
              : "No dropdown categories available."
            }
          </p>
          {(searchQuery || filterCategory || filterStatus) && (
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery("")
                setFilterCategory(null)
                setFilterStatus(null)
              }}
              className="mt-2"
            >
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {categoriesWithMeta.map((category) => {
          const IconComponent = category.icon
          return (
            <Card key={category.id} className="glass-effect hover:shadow-lg transition-all duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/50">
                    <IconComponent className={`h-5 w-5 ${category.color}`} />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {category.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {category.values.filter(v => v.isActive).length} active values
                  </span>
                  <Dialog 
                    open={isAddDialogOpen && selectedCategoryId === category.id} 
                    onOpenChange={(open) => {
                      setIsAddDialogOpen(open)
                      if (open) {
                        setSelectedCategoryId(category.id)
                        setNewValue("")
                      } else {
                        setSelectedCategoryId(null)
                      }
                    }}
                  >
                    <DialogTrigger asChild>
                      <Button size="sm" className="gradient-bg-primary text-blue-700 hover:opacity-90">
                        <Plus className="h-4 w-4 mr-1" />
                        Add Value
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New {category.name}</DialogTitle>
                        <DialogDescription>
                          Add a new value to the {category.name} dropdown list.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="new-value">Value</Label>
                          <Input
                            id="new-value"
                            value={newValue}
                            onChange={(e) => setNewValue(e.target.value)}
                            placeholder={`Enter new ${category.name.toLowerCase()}`}
                            className="mt-1"
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button 
                          variant="outline" 
                          onClick={() => {
                            setIsAddDialogOpen(false)
                            setSelectedCategoryId(null)
                            setNewValue("")
                          }}
                        >
                          Cancel
                        </Button>
                        <LoadingButton 
                          onClick={handleAddValue}
                          loading={isLoading}
                          disabled={!newValue.trim()}
                          className="gradient-bg-primary text-blue-700 hover:opacity-90"
                        >
                          Add Value
                        </LoadingButton>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>

                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {category.values.map((value) => (
                    <div 
                      key={value.id} 
                      className="flex items-center justify-between p-3 bg-white/30 rounded-lg border border-white/20"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{value.value}</span>
                        <Badge 
                          variant={value.isActive ? "default" : "secondary"}
                          className={value.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                        >
                          {value.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleToggleStatus(category.id, value.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Activity className="h-4 w-4" />
                        </Button>
                        
                        <Dialog 
                          open={isEditDialogOpen && editingValue?.id === value.id} 
                          onOpenChange={(open) => {
                            setIsEditDialogOpen(open)
                            if (open) {
                              setEditingValue(value)
                              setNewValue(value.value)
                            } else {
                              setEditingValue(null)
                              setNewValue("")
                            }
                          }}
                        >
                          <DialogTrigger asChild>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                              <Edit2 className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Edit {category.name}</DialogTitle>
                              <DialogDescription>
                                Update the value for this {category.name.toLowerCase()}.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="edit-value">Value</Label>
                                <Input
                                  id="edit-value"
                                  value={newValue}
                                  onChange={(e) => setNewValue(e.target.value)}
                                  className="mt-1"
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button 
                                variant="outline" 
                                onClick={() => {
                                  setIsEditDialogOpen(false)
                                  setEditingValue(null)
                                  setNewValue("")
                                }}
                              >
                                Cancel
                              </Button>
                              <LoadingButton 
                                onClick={handleEditValue}
                                loading={isLoading}
                                disabled={!newValue.trim()}
                                className="gradient-bg-primary text-blue-700 hover:opacity-90"
                              >
                                Update Value
                              </LoadingButton>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-red-100 hover:text-red-700">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Value</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete "{value.value}"? This action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction 
                                onClick={() => handleDeleteValue(category.id, value.id)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )
        })}
        </div>
      )}
    </div>
  )
}