"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RefreshCw, RotateCcw } from "lucide-react"
import { supabaseApiClient } from "@/lib/supabase-api-client"
import { useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"
import { FleetDashboard } from "@/components/dashboard/fleet-dashboard"
import { SimpleDashboardTest } from "@/components/dashboard/simple-dashboard-test"

export default function DashboardPage() {
  const [isSyncing, setIsSyncing] = useState(false)
  const queryClient = useQueryClient()

  const handleSyncDashboard = async () => {
    setIsSyncing(true)
    try {
      const result = await supabaseApiClient.syncDashboardData()
      
      // Check and ensure Dashboard protection is applied after sync
      try {
        const protectionStatus = await supabaseApiClient.checkDashboardProtection()
        if (!protectionStatus.isProtected) {
          await supabaseApiClient.applyDashboardProtection()
          console.log("Dashboard protection re-applied after sync")
        }
      } catch (protectionError) {
        console.warn("Failed to check/apply Dashboard protection:", protectionError)
      }
      
      // Invalidate all dashboard-related queries to refresh the data
      await queryClient.invalidateQueries({ queryKey: ["dashboard-data"] })
      await queryClient.invalidateQueries({ queryKey: ["dashboard-stats"] })
      await queryClient.invalidateQueries({ queryKey: ["vehicle-status-distribution"] })
      await queryClient.invalidateQueries({ queryKey: ["vehicle-location-distribution"] })
      await queryClient.invalidateQueries({ queryKey: ["vehicle-type-distribution"] })
      
      toast.success("Dashboard data synced successfully!", {
        description: result.message || "All dashboard data has been updated from the latest vehicle, driver, fuel, and maintenance records."
      })
    } catch (error) {
      console.error("Failed to sync dashboard:", error)
      toast.error("Failed to sync dashboard data", {
        description: error instanceof Error ? error.message : "An unexpected error occurred while syncing dashboard data."
      })
    } finally {
      setIsSyncing(false)
    }
  }

  const handleRefreshData = async () => {
    // Clear cache and refresh the queries
    await queryClient.removeQueries({ queryKey: ["dashboard-data"] })
    await queryClient.invalidateQueries({ queryKey: ["dashboard-data"] })
    await queryClient.invalidateQueries({ queryKey: ["dashboard-stats"] })
    await queryClient.invalidateQueries({ queryKey: ["vehicle-status-distribution"] })
    await queryClient.invalidateQueries({ queryKey: ["vehicle-location-distribution"] })
    await queryClient.invalidateQueries({ queryKey: ["vehicle-type-distribution"] })
    
    toast.success("Dashboard refreshed successfully!")
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          <div className="gradient-bg-primary p-6 rounded-2xl">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold text-gray-800">Dashboard</h1>
                <p className="text-gray-600 mt-2">Real-time overview of your fleet management system</p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefreshData}
                  className="bg-white/80 hover:bg-white"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSyncDashboard}
                  disabled={isSyncing}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <RotateCcw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
                  {isSyncing ? 'Syncing...' : 'Sync Data'}
                </Button>
              </div>
            </div>
          </div>

          <FleetDashboard onRefresh={handleRefreshData} />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
