"use client"

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Bug, Home, ArrowLeft } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Report to error monitoring service
    if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
      try {
        const errorData = {
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
        
        window.navigator.sendBeacon(
          '/api/error-report',
          JSON.stringify(errorData)
        )
      } catch (reportError) {
        console.error('Failed to report error:', reportError)
      }
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  render() {
    if (this.state.hasError) {
      const { error } = this.state
      
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={error!} resetError={this.handleReset} />
      }
      
      return <DefaultErrorFallback error={error!} resetError={this.handleReset} />
    }

    return this.props.children
  }
}

function DefaultErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  const handleReload = () => {
    window.location.reload()
  }
  
  const handleGoHome = () => {
    window.location.href = '/dashboard'
  }
  
  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        <Card className="shadow-xl border-0 border-l-4 border-l-red-500">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-xl font-bold text-gray-800">
              Something went wrong
            </CardTitle>
            <CardDescription className="text-gray-600">
              An unexpected error occurred. We've been notified and are working to fix it.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="bg-red-50 p-3 rounded-lg">
              <p className="text-sm text-red-700 font-medium">
                Error: {error.message}
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={resetError}
                className="flex items-center justify-center"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              
              <Button
                onClick={handleReload}
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={handleGoHome}
                variant="outline"
                size="sm"
              >
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
              
              <Button
                onClick={handleGoBack}
                variant="outline"
                size="sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {isDevelopment && (
          <Card className="shadow-xl border-0">
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Bug className="h-5 w-5 mr-2 text-orange-600" />
                Development Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm mb-2">Error Stack:</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                    {error.stack}
                  </pre>
                </div>
                
                {Boolean(error.cause) && (
                  <div>
                    <h4 className="font-medium text-sm mb-2">Cause:</h4>
                    <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                      {String(error.cause)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// Specific error fallbacks for different scenarios
export function APIErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="flex items-center justify-center py-8">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-2">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
          </div>
          <CardTitle className="text-lg">API Error</CardTitle>
          <CardDescription>
            Failed to communicate with the server
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            {error.message}
          </p>
          <Button onClick={resetError} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

export function FormErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <div className="flex items-center">
        <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
        <div className="flex-1">
          <p className="text-sm font-medium text-red-800">
            Form Error
          </p>
          <p className="text-sm text-red-700">
            {error.message}
          </p>
        </div>
        <Button
          onClick={resetError}
          variant="outline"
          size="sm"
          className="ml-2"
        >
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

export function TableErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {
  return (
    <div className="text-center py-8">
      <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        Failed to load data
      </h3>
      <p className="text-sm text-gray-600 mb-4">
        {error.message}
      </p>
      <Button onClick={resetError} size="sm">
        <RefreshCw className="h-4 w-4 mr-2" />
        Try Again
      </Button>
    </div>
  )
}

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>
) {
  return function WithErrorBoundaryComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    )
  }
}

// Hook for handling errors in functional components
export function useErrorHandler() {
  const handleError = (error: Error, errorInfo?: any) => {
    console.error('Error handled:', error, errorInfo)
    
    // Report to error monitoring service
    if (typeof window !== 'undefined' && window.navigator?.sendBeacon) {
      try {
        const errorData = {
          message: error.message,
          stack: error.stack,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          ...errorInfo
        }
        
        window.navigator.sendBeacon(
          '/api/error-report',
          JSON.stringify(errorData)
        )
      } catch (reportError) {
        console.error('Failed to report error:', reportError)
      }
    }
  }

  return { handleError }
}

export default ErrorBoundary