# دليل إعداد Supabase لنظام إدارة الأسطول

## 🚀 إعداد مشروع Supabase جديد

### الخطوة 1: إنشاء مشروع Supabase

1. **انتقل إلى [Supabase Dashboard](https://supabase.com/dashboard)**
2. **انقر على "New Project"**
3. **اختر Organization أو أنشئ واحدة جديدة**
4. **املأ تفاصيل المشروع:**
   - **Name**: Fleet Management System
   - **Database Password**: اختر كلمة مرور قوية واحفظها
   - **Region**: اختر أقرب منطقة جغرافية
   - **Pricing Plan**: اختر الخطة المناسبة

### الخطوة 2: الحصول على مفاتيح API

بعد إنشاء المشروع، ستحتاج إلى:

1. **انتقل إلى Settings > API**
2. **انسخ القيم التالية:**
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon public key**: للاستخدام في Frontend
   - **service_role secret**: للاستخدام في Backend (احتفظ بها سرية)

### الخطوة 3: تكوين متغيرات البيئة

1. **انسخ ملف `.env.example` إلى `.env.local`:**
```bash
cp .env.example .env.local
```

2. **املأ القيم في `.env.local`:**
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
```

## 🗄️ إعداد قاعدة البيانات

### الطريقة 1: استخدام Supabase CLI (موصى بها)

1. **تثبيت Supabase CLI:**
```bash
npm install -g supabase
```

2. **تسجيل الدخول:**
```bash
supabase login
```

3. **ربط المشروع المحلي:**
```bash
supabase link --project-ref your-project-ref
```

4. **تطبيق Migrations:**
```bash
supabase db push
```

5. **تطبيق البيانات الأولية:**
```bash
supabase db reset --linked
```

### الطريقة 2: استخدام SQL Editor في Dashboard

1. **انتقل إلى SQL Editor في Supabase Dashboard**
2. **انسخ والصق محتوى الملفات التالية بالترتيب:**
   - `supabase/migrations/001_initial_schema.sql`
   - `supabase/migrations/002_row_level_security.sql`
   - `supabase/seed.sql`

## 🔐 إعداد المصادقة

### تكوين Auth Settings

1. **انتقل إلى Authentication > Settings**
2. **تأكد من التكوينات التالية:**
   - **Enable email confirmations**: false (للتطوير)
   - **Enable phone confirmations**: false
   - **Site URL**: `http://localhost:3000`
   - **Redirect URLs**: أضف `http://localhost:3000/auth/callback`

### إعداد Email Templates (اختياري)

1. **انتقل إلى Authentication > Email Templates**
2. **خصص قوالب البريد الإلكتروني حسب الحاجة**

## 👥 إنشاء المستخدمين الأوليين

### إنشاء Super Admin

1. **انتقل إلى Authentication > Users**
2. **انقر على "Add user"**
3. **املأ البيانات:**
   - **Email**: <EMAIL>
   - **Password**: كلمة مرور قوية
   - **Email Confirm**: true
   - **User Metadata**:
     ```json
     {
       "full_name": "System Administrator",
       "role": "Super Admin"
     }
     ```

### إنشاء Manager

```json
{
  "email": "<EMAIL>",
  "password": "secure_password",
  "user_metadata": {
    "full_name": "Branch Manager",
    "role": "Manager"
  }
}
```

### إنشاء Employee

```json
{
  "email": "<EMAIL>",
  "password": "secure_password", 
  "user_metadata": {
    "full_name": "Fleet Employee",
    "role": "Employee"
  }
}
```

## 🔒 تكوين Row Level Security

### التحقق من تفعيل RLS

تأكد من أن RLS مفعل على جميع الجداول:

```sql
-- التحقق من حالة RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

### اختبار السياسات

```sql
-- اختبار سياسة Super Admin
SET request.jwt.claims TO '{"sub": "super-admin-uuid", "role": "authenticated"}';
SELECT * FROM profiles;

-- اختبار سياسة Manager
SET request.jwt.claims TO '{"sub": "manager-uuid", "role": "authenticated"}';
SELECT * FROM vehicles;
```

## 📊 إعداد Real-time

### تفعيل Real-time على الجداول

1. **انتقل إلى Database > Replication**
2. **فعل Real-time للجداول التالية:**
   - vehicles
   - drivers
   - maintenance_records
   - fuel_records
   - profiles

### تكوين Real-time Filters

```sql
-- إعداد فلاتر Real-time للأمان
ALTER PUBLICATION supabase_realtime ADD TABLE vehicles;
ALTER PUBLICATION supabase_realtime ADD TABLE drivers;
ALTER PUBLICATION supabase_realtime ADD TABLE maintenance_records;
ALTER PUBLICATION supabase_realtime ADD TABLE fuel_records;
```

## 🔧 تثبيت Dependencies

### إضافة Supabase إلى المشروع

```bash
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs @supabase/auth-helpers-react @supabase/auth-ui-react @supabase/auth-ui-shared
```

### تحديث package.json

```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.39.0",
    "@supabase/auth-helpers-nextjs": "^0.8.7",
    "@supabase/auth-helpers-react": "^0.4.2",
    "@supabase/auth-ui-react": "^0.4.6",
    "@supabase/auth-ui-shared": "^0.1.8"
  }
}
```

## 🧪 اختبار الإعداد

### اختبار الاتصال

```javascript
// test-connection.js
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('branches')
      .select('*')
      .limit(1)
    
    if (error) throw error
    console.log('✅ Connection successful:', data)
  } catch (error) {
    console.error('❌ Connection failed:', error)
  }
}

testConnection()
```

### اختبار المصادقة

```javascript
// test-auth.js
async function testAuth() {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your_password'
    })
    
    if (error) throw error
    console.log('✅ Authentication successful:', data.user)
  } catch (error) {
    console.error('❌ Authentication failed:', error)
  }
}

testAuth()
```

## 📈 مراقبة الأداء

### إعداد Monitoring

1. **انتقل إلى Settings > Database**
2. **فعل Query Performance Insights**
3. **راقب الاستعلامات البطيئة**

### تحسين الفهارس

```sql
-- مراقبة استخدام الفهارس
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

## 🔄 النسخ الاحتياطي

### إعداد النسخ الاحتياطي التلقائي

1. **انتقل إلى Settings > Database**
2. **فعل Point-in-time Recovery**
3. **اختر فترة الاحتفاظ المناسبة**

### النسخ الاحتياطي اليدوي

```bash
# تصدير قاعدة البيانات
supabase db dump --linked > backup.sql

# استيراد قاعدة البيانات
supabase db reset --linked
```

## ⚠️ نصائح الأمان

### أفضل الممارسات

1. **لا تشارك Service Role Key أبداً**
2. **استخدم Environment Variables للمفاتيح**
3. **فعل RLS على جميع الجداول**
4. **راجع السياسات بانتظام**
5. **استخدم HTTPS في الإنتاج**

### مراجعة الأمان

```sql
-- التحقق من السياسات المفعلة
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public';
```

## 🚀 الانتقال إلى الإنتاج

### قائمة التحقق

- [ ] تم تطبيق جميع Migrations
- [ ] تم اختبار جميع السياسات
- [ ] تم تكوين النسخ الاحتياطي
- [ ] تم تحديث Environment Variables
- [ ] تم اختبار الأداء
- [ ] تم مراجعة الأمان

### تكوين الإنتاج

```env
# Production Environment Variables
NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_prod_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_prod_service_key
NODE_ENV=production
```

هذا الدليل يوفر إعداداً شاملاً لـ Supabase مع نظام إدارة الأسطول. تأكد من اتباع جميع الخطوات بعناية لضمان إعداد آمن وفعال.
