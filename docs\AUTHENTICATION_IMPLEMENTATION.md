# دليل تطبيق نظام المصادقة مع Supabase

## 📋 نظرة عامة

تم تطوير نظام مصادقة شامل باستخدام Supabase Auth مع Row Level Security (RLS) وإدارة الأدوار والصلاحيات.

## 🔧 المكونات الرئيسية

### 1. Supabase Client (`lib/supabase.ts`)
- إعداد Supabase client مع TypeScript types
- دوال مساعدة للمصادقة
- إعداد Real-time subscriptions
- دوال إدارة الجلسات

### 2. Auth Context (`context/supabase-auth-context.tsx`)
- إدارة حالة المستخدم
- دوال تسجيل الدخول والخروج
- نظام الصلاحيات
- تحديث البيانات التلقائي

### 3. Protected Routes (`components/auth/supabase-protected-route.tsx`)
- حماية الصفحات بناءً على الأدوار
- حماية بناءً على الصلاحيات
- حماية بناءً على الفروع
- مكونات التحكم في الوصول

### 4. Login Form (`components/auth/supabase-login-form.tsx`)
- واجهة تسجيل دخول محسنة
- معالجة الأخطاء
- حسابات تجريبية للاختبار

### 5. Middleware (`middleware-supabase.ts`)
- حماية الطرق على مستوى الخادم
- التحقق من الجلسات
- إعادة توجيه تلقائية

## 🔐 نظام الأدوار والصلاحيات

### الأدوار المتاحة

#### 1. Super Admin
**الصلاحيات الكاملة:**
- `manage_all_data` - إدارة جميع البيانات
- `manage_users` - إدارة المستخدمين
- `assign_vehicles` - تخصيص المركبات
- `access_all_reports` - الوصول لجميع التقارير
- `view_all_data` - عرض جميع البيانات
- `manage_branches` - إدارة الفروع
- `delete_records` - حذف السجلات
- `export_data` - تصدير البيانات
- `system_settings` - إعدادات النظام

#### 2. Manager
**صلاحيات الفرع:**
- `manage_branch_vehicles` - إدارة مركبات الفرع
- `view_branch_data` - عرض بيانات الفرع
- `assign_drivers` - تخصيص السائقين
- `access_branch_reports` - تقارير الفرع
- `manage_branch_drivers` - إدارة سائقي الفرع
- `schedule_maintenance` - جدولة الصيانة
- `add_fuel_records` - إضافة سجلات الوقود

#### 3. Employee
**صلاحيات محدودة:**
- `input_data` - إدخال البيانات
- `view_assigned_data` - عرض البيانات المخصصة
- `add_fuel_records` - إضافة سجلات الوقود
- `view_own_reports` - عرض التقارير الشخصية

## 🛡️ Row Level Security (RLS)

### السياسات المطبقة

#### 1. سياسات المستخدمين (profiles)
```sql
-- Super Admin يمكنه رؤية جميع المستخدمين
CREATE POLICY "Super Admin can view all profiles" ON profiles
  FOR SELECT USING (is_super_admin());

-- Manager يمكنه رؤية مستخدمي فرعه
CREATE POLICY "Manager can view branch profiles" ON profiles
  FOR SELECT USING (user_in_same_branch());

-- Employee يمكنه رؤية ملفه الشخصي فقط
CREATE POLICY "Employee can view own profile" ON profiles
  FOR SELECT USING (id = auth.uid());
```

#### 2. سياسات المركبات (vehicles)
```sql
-- Super Admin: جميع المركبات
-- Manager: مركبات فرعه فقط
-- Employee: مركبات فرعه للقراءة فقط
```

#### 3. سياسات السائقين (drivers)
```sql
-- نفس منطق المركبات
-- مع إضافة صلاحيات التعديل للمدراء
```

## 🚀 كيفية الاستخدام

### 1. تطبيق Auth Provider

```tsx
// app/layout.tsx
import { SupabaseAuthProvider } from '@/context/supabase-auth-context'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar">
      <body>
        <SupabaseAuthProvider>
          {children}
        </SupabaseAuthProvider>
      </body>
    </html>
  )
}
```

### 2. حماية الصفحات

```tsx
// app/admin/page.tsx
import { SupabaseProtectedRoute } from '@/components/auth/supabase-protected-route'

export default function AdminPage() {
  return (
    <SupabaseProtectedRoute requiredRole="Super Admin">
      <div>محتوى صفحة الإدارة</div>
    </SupabaseProtectedRoute>
  )
}
```

### 3. حماية بناءً على الصلاحيات

```tsx
import { PermissionBasedAccess } from '@/components/auth/supabase-protected-route'

function VehicleManagement() {
  return (
    <PermissionBasedAccess 
      requiredPermission="manage_branch_vehicles"
      fallback={<div>ليس لديك صلاحية لإدارة المركبات</div>}
    >
      <VehicleForm />
    </PermissionBasedAccess>
  )
}
```

### 4. حماية بناءً على الأدوار

```tsx
import { RoleBasedAccess } from '@/components/auth/supabase-protected-route'

function UserManagement() {
  return (
    <RoleBasedAccess 
      allowedRoles={['Super Admin', 'Manager']}
      fallback={<div>غير مصرح لك بإدارة المستخدمين</div>}
    >
      <UserForm />
    </RoleBasedAccess>
  )
}
```

### 5. استخدام Auth Hook

```tsx
import { useSupabaseAuth } from '@/context/supabase-auth-context'

function UserProfile() {
  const { user, profile, logout, hasPermission } = useSupabaseAuth()

  if (!user) return <div>غير مسجل دخول</div>

  return (
    <div>
      <h1>مرحباً {user.full_name}</h1>
      <p>الدور: {user.role}</p>
      
      {hasPermission('manage_users') && (
        <button>إدارة المستخدمين</button>
      )}
      
      <button onClick={logout}>تسجيل الخروج</button>
    </div>
  )
}
```

## 🔄 تدفق المصادقة

### 1. تسجيل الدخول
```mermaid
graph TD
    A[المستخدم يدخل البيانات] --> B[استدعاء login()]
    B --> C[Supabase Auth]
    C --> D{نجح التسجيل؟}
    D -->|نعم| E[جلب Profile]
    D -->|لا| F[عرض خطأ]
    E --> G{المستخدم نشط؟}
    G -->|نعم| H[تحديث الحالة]
    G -->|لا| I[تسجيل خروج]
    H --> J[إعادة توجيه للداشبورد]
```

### 2. التحقق من الصلاحيات
```mermaid
graph TD
    A[طلب الوصول لصفحة] --> B[Middleware]
    B --> C{مسجل دخول؟}
    C -->|لا| D[إعادة توجيه للتسجيل]
    C -->|نعم| E[جلب Profile]
    E --> F{المستخدم نشط؟}
    F -->|لا| G[صفحة غير مصرح]
    F -->|نعم| H{يملك الصلاحية؟}
    H -->|لا| I[صفحة غير مصرح]
    H -->|نعم| J[السماح بالوصول]
```

## 🧪 الاختبار

### حسابات تجريبية

```javascript
const testAccounts = [
  {
    email: '<EMAIL>',
    password: 'password',
    role: 'Super Admin'
  },
  {
    email: '<EMAIL>', 
    password: 'password',
    role: 'Manager'
  },
  {
    email: '<EMAIL>',
    password: 'password', 
    role: 'Employee'
  }
]
```

### اختبار الصلاحيات

```tsx
// Test component
function PermissionTest() {
  const { hasPermission } = useSupabaseAuth()
  
  const permissions = [
    'manage_all_data',
    'manage_users', 
    'view_branch_data',
    'input_data'
  ]
  
  return (
    <div>
      {permissions.map(permission => (
        <div key={permission}>
          {permission}: {hasPermission(permission) ? '✅' : '❌'}
        </div>
      ))}
    </div>
  )
}
```

## 🔧 التكوين

### متغيرات البيئة المطلوبة

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### إعدادات Supabase

1. **تفعيل RLS على جميع الجداول**
2. **إنشاء السياسات المطلوبة**
3. **تكوين Auth Settings**
4. **إضافة المستخدمين الأوليين**

## 🚨 الأمان

### أفضل الممارسات

1. **عدم تخزين Service Role Key في Frontend**
2. **استخدام RLS لحماية البيانات**
3. **التحقق من الصلاحيات في كل طلب**
4. **تسجيل محاولات الوصول غير المصرح**
5. **تحديث كلمات المرور بانتظام**

### مراقبة الأمان

```sql
-- مراقبة محاولات تسجيل الدخول الفاشلة
SELECT 
  created_at,
  raw_user_meta_data->>'email' as email,
  event_type
FROM auth.audit_log_entries 
WHERE event_type = 'user_signinup_failed'
ORDER BY created_at DESC;
```

## 🔄 الترقية من النظام القديم

### خطوات الترحيل

1. **إبقاء النظام القديم كـ fallback**
2. **تطبيق النظام الجديد تدريجياً**
3. **اختبار شامل للصلاحيات**
4. **ترحيل المستخدمين**
5. **إزالة النظام القديم**

### التوافق العكسي

```tsx
// Wrapper للتوافق مع النظام القديم
export function useAuth() {
  const supabaseAuth = useSupabaseAuth()
  
  return {
    user: supabaseAuth.user ? {
      id: supabaseAuth.user.id,
      fullName: supabaseAuth.user.full_name,
      email: supabaseAuth.user.email,
      role: supabaseAuth.user.role,
      branchId: supabaseAuth.user.branch_id
    } : null,
    login: supabaseAuth.login,
    logout: supabaseAuth.logout,
    loading: supabaseAuth.loading,
    hasPermission: supabaseAuth.hasPermission
  }
}
```

هذا النظام يوفر مصادقة آمنة وشاملة مع إدارة متقدمة للأدوار والصلاحيات.
