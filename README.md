# نظام إدارة الأسطول - Fleet Management System

## 📋 نظرة عامة

نظام شامل لإدارة الأسطول مبني بتقنيات حديثة مع قاعدة بيانات Supabase وواجهة Next.js. يوفر النظام إدارة كاملة للمركبات والسائقين والصيانة والوقود مع تحديثات فورية ونظام صلاحيات متقدم.

## 🚀 الميزات الرئيسية

### 📊 لوحة التحكم
- إحصائيات شاملة في الوقت الفعلي
- تنبيهات ذكية للصيانة والرخص
- رسوم بيانية تفاعلية
- مراقبة الأداء

### 🚗 إدارة المركبات
- تسجيل وتتبع جميع المركبات
- حالة المركبة (نشطة، صيانة، خارج الخدمة)
- تخصيص السائقين للمركبات
- تتبع الكيلومترات والمواصفات

### 👥 إدارة السائقين
- ملفات شخصية كاملة للسائقين
- تتبع انتهاء صلاحية الرخص
- إدارة حالات السائقين
- ربط السائقين بالمركبات

### 🔧 إدارة الصيانة
- جدولة الصيانة الدورية والطارئة
- تتبع تكاليف الصيانة
- تنبيهات الصيانة المتأخرة
- سجل كامل لأعمال الصيانة

### ⛽ إدارة الوقود
- تسجيل عمليات التزويد بالوقود
- حساب معدل الاستهلاك
- تتبع التكاليف
- تحليل كفاءة الوقود

### 🏢 إدارة الفروع
- تنظيم المركبات حسب الفروع
- إدارة مدراء الفروع
- تقارير خاصة بكل فرع

### 👤 إدارة المستخدمين
- نظام صلاحيات متدرج (مدير عام، مدير فرع، موظف)
- مصادقة آمنة مع Supabase Auth
- Row Level Security (RLS)

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 14** - React framework مع App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling framework
- **shadcn/ui** - UI components
- **React Query** - Data fetching وcaching
- **React Hook Form** - Form management
- **Recharts** - Data visualization

### Backend
- **Supabase** - Backend as a Service
- **PostgreSQL** - Database
- **Row Level Security** - Data security
- **Real-time subscriptions** - Live updates

### DevOps & Tools
- **Vercel** - Deployment platform
- **ESLint & Prettier** - Code quality
- **Husky** - Git hooks

## 📦 التثبيت والإعداد

### المتطلبات
- Node.js 18+ 
- npm أو yarn أو pnpm
- حساب Supabase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/fleet-management-system.git
cd fleet-management-system
```

2. **تثبيت Dependencies**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env.local
```

املأ المتغيرات في `.env.local`:
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key


```

4. **إعداد قاعدة البيانات والبيانات التجريبية**
```bash
# إعداد شامل (RLS + بيانات تجريبية)
npm run setup

# أو خطوة بخطوة:
npm run apply-rls  # تطبيق Row Level Security
npm run seed       # تطبيق البيانات التجريبية
```

5. **تشغيل التطبيق**
```bash
npm run dev
```

التطبيق سيكون متاحاً على `http://localhost:3000`

6. **تسجيل الدخول**

استخدم الحسابات التجريبية:
- **مدير عام:** `<EMAIL>` / `admin123`
- **مدير فرع:** `<EMAIL>` / `manager123`
- **موظف:** `<EMAIL>` / `employee123`

## 🗄️ هيكل قاعدة البيانات

### الجداول الرئيسية

#### `profiles` - ملفات المستخدمين
```sql
- id (UUID, Primary Key)
- email (Text, Unique)
- full_name (Text)
- role (Enum: Super Admin, Manager, Employee)
- branch_id (UUID, Foreign Key)
- user_status (Enum: Active, Inactive)
- last_login (Timestamp)
```

#### `branches` - الفروع
```sql
- id (UUID, Primary Key)
- name (Text)
- location (Text)
- address (Text)
- phone (Text)
- email (Text)
- manager_id (UUID, Foreign Key)
- branch_status (Enum: Active, Inactive)
```

#### `vehicles` - المركبات
```sql
- id (UUID, Primary Key)
- plate_number (Text, Unique)
- vin_number (Text)
- model (Integer)
- vehicle_type (Text)
- service_type (Text)
- department (Text)
- fuel_type (Text)
- tank_capacity_liters (Numeric)
- engine_cc (Integer)
- color (Text)
- current_km (Integer)
- branch_id (UUID, Foreign Key)
- assigned_driver_id (UUID, Foreign Key)
- vehicle_status (Enum: Active, Maintenance, Inactive, Out of Service)
```

#### `drivers` - السائقين
```sql
- id (UUID, Primary Key)
- full_name (Text)
- license_number (Text, Unique)
- license_expiry (Date)
- phone (Text)
- email (Text)
- status (Enum: Active, Available, Inactive, On Leave)
- hire_date (Date)
- branch_id (UUID, Foreign Key)
```

#### `maintenance_records` - سجلات الصيانة
```sql
- id (UUID, Primary Key)
- vehicle_id (UUID, Foreign Key)
- maintenance_type (Text)
- maintenance_status (Enum: Scheduled, In Progress, Completed, Cancelled)
- scheduled_date (Date)
- completed_date (Date)
- cost (Numeric)
- description (Text)
- technician (Text)
- next_maintenance_km (Integer)
```

#### `fuel_records` - سجلات الوقود
```sql
- id (UUID, Primary Key)
- vehicle_id (UUID, Foreign Key)
- date (Date)
- quantity_liters (Numeric)
- distance_km (Numeric)
- cost (Numeric)
- station (Text)
- consumption_per_100km (Numeric)
```

## 🔐 نظام الصلاحيات

### الأدوار والصلاحيات

#### Super Admin (مدير عام)
- إدارة جميع البيانات
- إدارة المستخدمين والفروع
- الوصول لجميع التقارير
- إعدادات النظام

#### Manager (مدير فرع)
- إدارة مركبات وسائقي الفرع
- جدولة الصيانة
- تقارير الفرع
- إدارة سجلات الوقود

#### Employee (موظف)
- إدخال البيانات المخصصة
- عرض التقارير الشخصية
- إضافة سجلات الوقود

### Row Level Security (RLS)

```sql
-- مثال: سياسة المركبات
CREATE POLICY "vehicles_branch_policy" ON vehicles
  FOR ALL USING (
    branch_id IN (
      SELECT branch_id FROM profiles 
      WHERE id = auth.uid() AND user_status = 'Active'
    )
  );
```

## 📱 واجهة المستخدم

### الصفحات الرئيسية

- **`/dashboard`** - لوحة التحكم الرئيسية
- **`/vehicles`** - إدارة المركبات
- **`/drivers`** - إدارة السائقين
- **`/maintenance`** - إدارة الصيانة
- **`/fuel`** - إدارة الوقود
- **`/reports`** - التقارير
- **`/users`** - إدارة المستخدمين (للمدراء)
- **`/settings`** - الإعدادات

### المكونات الرئيسية

```
components/
├── ui/                 # مكونات UI الأساسية
├── forms/             # نماذج الإدخال
├── tables/            # جداول البيانات
├── charts/            # الرسوم البيانية
├── auth/              # مكونات المصادقة
├── performance/       # مراقبة الأداء
└── realtime/          # التحديثات الفورية
```

## 🔄 التحديثات الفورية (Real-time)

النظام يدعم التحديثات الفورية باستخدام Supabase Realtime:

```typescript
// مثال: الاشتراك في تحديثات المركبات
const { isSubscribed } = useRealtimeSubscription('vehicles', (event) => {
  console.log('Vehicle updated:', event)
  // تحديث الواجهة تلقائياً
})
```

### الميزات الفورية
- تحديث البيانات عند التغيير
- تنبيهات فورية للأحداث المهمة
- مزامنة متعددة المستخدمين
- مراقبة الأداء في الوقت الفعلي

## 📊 التقارير والتحليلات

### أنواع التقارير
- تقارير المركبات والاستخدام
- تقارير استهلاك الوقود
- تقارير تكاليف الصيانة
- تقارير أداء السائقين
- تقارير مالية شاملة

### التصدير
- PDF للتقارير المطبوعة
- Excel للتحليل المتقدم
- CSV للبيانات الخام

## 🚀 النشر (Deployment)

### Vercel (موصى به)

1. **ربط المشروع بـ Vercel**
```bash
npm i -g vercel
vercel
```

2. **إعداد متغيرات البيئة في Vercel**
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

3. **النشر**
```bash
vercel --prod
```

### Docker (اختياري)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Coverage
npm run test:coverage
```

### حسابات تجريبية
```
Super Admin: <EMAIL> / password
Manager: <EMAIL> / password  
Employee: <EMAIL> / password
```

## 📚 الوثائق التفصيلية

- [دليل قاعدة البيانات](docs/DATABASE_SCHEMA.md)
- [دليل نظام المصادقة](docs/AUTHENTICATION_IMPLEMENTATION.md)

- [دليل تحسين الأداء](docs/PERFORMANCE_OPTIMIZATION.md)

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للـ branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للدعم الفني أو الاستفسارات:
- Email: <EMAIL>
- Documentation: [docs.fleetmanagement.com](https://docs.fleetmanagement.com)
- Issues: [GitHub Issues](https://github.com/your-username/fleet-management-system/issues)

## 🙏 شكر وتقدير

- [Supabase](https://supabase.com) - Backend platform
- [Next.js](https://nextjs.org) - React framework
- [shadcn/ui](https://ui.shadcn.com) - UI components
- [Tailwind CSS](https://tailwindcss.com) - CSS framework

---

**نظام إدارة الأسطول** - حل شامل ومتطور لإدارة الأساطيل بكفاءة وفعالية.
