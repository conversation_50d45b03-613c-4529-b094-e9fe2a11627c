-- Fix RLS policies to avoid infinite recursion
-- Run this in Supabase SQL Editor

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view their own data" ON users;
DROP POLICY IF EXISTS "Users can update their own data" ON users;
DROP POLICY IF EXISTS "Admins can manage all users" ON users;

-- Create simplified RLS policies for users table
CREATE POLICY "Enable read access for authenticated users" ON users
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable insert for authenticated users" ON users
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Fix other tables policies to avoid recursion
-- Branches
DROP POLICY IF EXISTS "Branch access based on user role" ON branches;
CREATE POLICY "Enable read access for authenticated users" ON branches
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON branches
    FOR ALL USING (auth.role() = 'authenticated');

-- Drivers
DROP POLICY IF EXISTS "Driver access based on user role and branch" ON drivers;
CREATE POLICY "Enable read access for authenticated users" ON drivers
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON drivers
    FOR ALL USING (auth.role() = 'authenticated');

-- Vehicles
DROP POLICY IF EXISTS "Vehicle access based on user role and branch" ON vehicles;
CREATE POLICY "Enable read access for authenticated users" ON vehicles
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON vehicles
    FOR ALL USING (auth.role() = 'authenticated');

-- Maintenance Records
DROP POLICY IF EXISTS "Maintenance access based on user role and branch" ON maintenance_records;
CREATE POLICY "Enable read access for authenticated users" ON maintenance_records
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON maintenance_records
    FOR ALL USING (auth.role() = 'authenticated');

-- Fuel Records
DROP POLICY IF EXISTS "Fuel access based on user role and branch" ON fuel_records;
CREATE POLICY "Enable read access for authenticated users" ON fuel_records
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON fuel_records
    FOR ALL USING (auth.role() = 'authenticated');

-- Dropdown Config
DROP POLICY IF EXISTS "Config access based on user role" ON dropdown_config;
CREATE POLICY "Enable read access for authenticated users" ON dropdown_config
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON dropdown_config
    FOR ALL USING (auth.role() = 'authenticated');

-- System Settings
DROP POLICY IF EXISTS "Settings access for admins only" ON system_settings;
CREATE POLICY "Enable read access for authenticated users" ON system_settings
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON system_settings
    FOR ALL USING (auth.role() = 'authenticated');

-- Audit Logs
DROP POLICY IF EXISTS "Audit logs read-only for admins" ON audit_logs;
CREATE POLICY "Enable read access for authenticated users" ON audit_logs
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON audit_logs
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Notifications (already working)
-- Keep existing policies

-- Refresh schema cache
NOTIFY pgrst, 'reload schema';