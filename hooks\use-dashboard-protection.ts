"use client"

import { useState, useCallback } from "react"
import { supabaseApiClient } from "@/lib/supabase-api-client"
import { toast } from "sonner"

interface ProtectionStatus {
  isProtected: boolean
  protectionCount: number
  details?: {
    description: string
    editors: string[]
    isWarningOnly: boolean
  }
}

export function useDashboardProtection() {
  const [protectionStatus, setProtectionStatus] = useState<ProtectionStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isChecking, setIsChecking] = useState(false)

  const checkProtectionStatus = useCallback(async () => {
    setIsChecking(true)
    try {
      const response = await supabaseApiClient.checkDashboardProtection()
      setProtectionStatus({
        isProtected: response.isProtected,
        protectionCount: response.protectionCount,
        details: response.details
      })
      return response
    } catch (error) {
      console.error("Failed to check protection status:", error)
      toast.error("فشل في التحقق من حالة الحماية")
      throw error
    } finally {
      setIsChecking(false)
    }
  }, [])

  const applyProtection = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await supabaseApiClient.applyDashboardProtection()
      if (response.success) {
        toast.success("تم تطبيق الحماية على ورقة Dashboard بنجاح")
        await checkProtectionStatus()
        return response
      } else {
        toast.error("فشل في تطبيق الحماية: " + response.error)
        throw new Error(response.error)
      }
    } catch (error) {
      console.error("Failed to apply protection:", error)
      toast.error("فشل في تطبيق الحماية")
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [checkProtectionStatus])

  const removeProtection = useCallback(async () => {
    setIsLoading(true)
    try {
      const response = await supabaseApiClient.removeDashboardProtection()
      if (response.success) {
        toast.success("تم إزالة الحماية من ورقة Dashboard")
        await checkProtectionStatus()
        return response
      } else {
        toast.error("فشل في إزالة الحماية: " + response.error)
        throw new Error(response.error)
      }
    } catch (error) {
      console.error("Failed to remove protection:", error)
      toast.error("فشل في إزالة الحماية")
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [checkProtectionStatus])

  const removeProtectionWithConfirmation = useCallback(async () => {
    if (!confirm("هل أنت متأكد من إزالة الحماية؟ هذا قد يعرض البيانات للتعديل غير المرغوب فيه.")) {
      return null
    }
    return await removeProtection()
  }, [removeProtection])

  return {
    protectionStatus,
    isLoading,
    isChecking,
    checkProtectionStatus,
    applyProtection,
    removeProtection,
    removeProtectionWithConfirmation,
  }
}