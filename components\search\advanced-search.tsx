"use client"

import { useState, useEffect, useMemo } from "react"
import { Search, Filter, X, Calendar, SlidersHorizontal } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"

interface SearchFilter {
  key: string
  label: string
  type: 'text' | 'select' | 'date' | 'dateRange' | 'number' | 'numberRange' | 'checkbox'
  options?: { label: string; value: string }[]
  min?: number
  max?: number
  step?: number
}

interface FilterValue {
  key: string
  value: any
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte' | 'between'
  label?: string
}

interface AdvancedSearchProps {
  filters: SearchFilter[]
  onSearch: (query: string, filters: FilterValue[]) => void
  placeholder?: string
  className?: string
}

export function AdvancedSearch({ 
  filters, 
  onSearch, 
  placeholder = "Search...",
  className = ""
}: AdvancedSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<FilterValue[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [tempFilters, setTempFilters] = useState<{ [key: string]: any }>({})

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      onSearch(searchQuery, activeFilters)
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [searchQuery, activeFilters, onSearch])

  const addFilter = (filter: SearchFilter, value: any, operator?: FilterValue['operator']) => {
    const newFilter: FilterValue = {
      key: filter.key,
      value,
      operator: operator || 'equals',
      label: filter.label
    }

    setActiveFilters(prev => {
      const existing = prev.findIndex(f => f.key === filter.key)
      if (existing >= 0) {
        const updated = [...prev]
        updated[existing] = newFilter
        return updated
      }
      return [...prev, newFilter]
    })
  }

  const removeFilter = (key: string) => {
    setActiveFilters(prev => prev.filter(f => f.key !== key))
    setTempFilters(prev => {
      const updated = { ...prev }
      delete updated[key]
      return updated
    })
  }

  const clearAllFilters = () => {
    setActiveFilters([])
    setTempFilters({})
    setSearchQuery("")
  }

  const renderFilterControl = (filter: SearchFilter) => {
    const tempValue = tempFilters[filter.key]

    switch (filter.type) {
      case 'text':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="flex gap-2">
              <Select
                value={tempValue?.operator || 'contains'}
                onValueChange={(operator) => 
                  setTempFilters(prev => ({ 
                    ...prev, 
                    [filter.key]: { ...prev[filter.key], operator } 
                  }))
                }
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="equals">Equals</SelectItem>
                  <SelectItem value="startsWith">Starts with</SelectItem>
                  <SelectItem value="endsWith">Ends with</SelectItem>
                </SelectContent>
              </Select>
              <Input
                placeholder={`Enter ${filter.label.toLowerCase()}`}
                value={tempValue?.value || ''}
                onChange={(e) => {
                  const value = e.target.value
                  const operator = tempValue?.operator || 'contains'
                  setTempFilters(prev => ({ 
                    ...prev, 
                    [filter.key]: { value, operator } 
                  }))
                  
                  if (value) {
                    addFilter(filter, value, operator as FilterValue['operator'])
                  } else {
                    removeFilter(filter.key)
                  }
                }}
                className="flex-1"
              />
            </div>
          </div>
        )

      case 'select':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <Select
              value={tempValue?.value || ''}
              onValueChange={(value) => {
                setTempFilters(prev => ({ 
                  ...prev, 
                  [filter.key]: { value, operator: 'equals' } 
                }))
                
                if (value) {
                  addFilter(filter, value, 'equals')
                } else {
                  removeFilter(filter.key)
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Select ${filter.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All</SelectItem>
                {filter.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )

      case 'date':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal">
                  <Calendar className="mr-2 h-4 w-4" />
                  {tempValue?.value ? new Date(tempValue.value).toLocaleDateString() : 'Select date'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={tempValue?.value ? new Date(tempValue.value) : undefined}
                  onSelect={(date) => {
                    const value = date?.toISOString().split('T')[0]
                    setTempFilters(prev => ({ 
                      ...prev, 
                      [filter.key]: { value, operator: 'equals' } 
                    }))
                    
                    if (value) {
                      addFilter(filter, value, 'equals')
                    } else {
                      removeFilter(filter.key)
                    }
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        )

      case 'dateRange':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex-1 justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {tempValue?.value?.from ? new Date(tempValue.value.from).toLocaleDateString() : 'From'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={tempValue?.value?.from ? new Date(tempValue.value.from) : undefined}
                    onSelect={(date) => {
                      const from = date?.toISOString().split('T')[0]
                      const to = tempValue?.value?.to
                      const value = { from, to }
                      setTempFilters(prev => ({ 
                        ...prev, 
                        [filter.key]: { value, operator: 'between' } 
                      }))
                      
                      if (from && to) {
                        addFilter(filter, value, 'between')
                      }
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex-1 justify-start text-left font-normal">
                    <Calendar className="mr-2 h-4 w-4" />
                    {tempValue?.value?.to ? new Date(tempValue.value.to).toLocaleDateString() : 'To'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={tempValue?.value?.to ? new Date(tempValue.value.to) : undefined}
                    onSelect={(date) => {
                      const to = date?.toISOString().split('T')[0]
                      const from = tempValue?.value?.from
                      const value = { from, to }
                      setTempFilters(prev => ({ 
                        ...prev, 
                        [filter.key]: { value, operator: 'between' } 
                      }))
                      
                      if (from && to) {
                        addFilter(filter, value, 'between')
                      }
                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        )

      case 'number':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="flex gap-2">
              <Select
                value={tempValue?.operator || 'equals'}
                onValueChange={(operator) => 
                  setTempFilters(prev => ({ 
                    ...prev, 
                    [filter.key]: { ...prev[filter.key], operator } 
                  }))
                }
              >
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">=</SelectItem>
                  <SelectItem value="gt">{'>'}</SelectItem>
                  <SelectItem value="lt">{'<'}</SelectItem>
                  <SelectItem value="gte">≥</SelectItem>
                  <SelectItem value="lte">≤</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="number"
                placeholder="0"
                value={tempValue?.value || ''}
                onChange={(e) => {
                  const value = e.target.value ? parseFloat(e.target.value) : null
                  const operator = tempValue?.operator || 'equals'
                  setTempFilters(prev => ({ 
                    ...prev, 
                    [filter.key]: { value, operator } 
                  }))
                  
                  if (value !== null) {
                    addFilter(filter, value, operator as FilterValue['operator'])
                  } else {
                    removeFilter(filter.key)
                  }
                }}
                className="flex-1"
                min={filter.min}
                max={filter.max}
                step={filter.step}
              />
            </div>
          </div>
        )

      case 'numberRange':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="px-2">
              <Slider
                value={[tempValue?.value?.min || filter.min || 0, tempValue?.value?.max || filter.max || 100]}
                onValueChange={([min, max]) => {
                  const value = { min, max }
                  setTempFilters(prev => ({ 
                    ...prev, 
                    [filter.key]: { value, operator: 'between' } 
                  }))
                  addFilter(filter, value, 'between')
                }}
                min={filter.min || 0}
                max={filter.max || 100}
                step={filter.step || 1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{tempValue?.value?.min || filter.min || 0}</span>
                <span>{tempValue?.value?.max || filter.max || 100}</span>
              </div>
            </div>
          </div>
        )

      case 'checkbox':
        return (
          <div className="space-y-2">
            <Label>{filter.label}</Label>
            <div className="space-y-2">
              {filter.options?.map(option => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${filter.key}-${option.value}`}
                    checked={tempValue?.value?.includes(option.value) || false}
                    onCheckedChange={(checked) => {
                      const currentValues = tempValue?.value || []
                      let newValues
                      
                      if (checked) {
                        newValues = [...currentValues, option.value]
                      } else {
                        newValues = currentValues.filter((v: string) => v !== option.value)
                      }
                      
                      setTempFilters(prev => ({ 
                        ...prev, 
                        [filter.key]: { value: newValues, operator: 'equals' } 
                      }))
                      
                      if (newValues.length > 0) {
                        addFilter(filter, newValues, 'equals')
                      } else {
                        removeFilter(filter.key)
                      }
                    }}
                  />
                  <Label htmlFor={`${filter.key}-${option.value}`} className="text-sm">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  const formatFilterValue = (filter: FilterValue) => {
    const filterConfig = filters.find(f => f.key === filter.key)
    
    if (filterConfig?.type === 'select') {
      const option = filterConfig.options?.find(o => o.value === filter.value)
      return option?.label || filter.value
    }
    
    if (filterConfig?.type === 'dateRange' && filter.value?.from && filter.value?.to) {
      return `${new Date(filter.value.from).toLocaleDateString()} - ${new Date(filter.value.to).toLocaleDateString()}`
    }
    
    if (filterConfig?.type === 'numberRange' && filter.value?.min !== undefined && filter.value?.max !== undefined) {
      return `${filter.value.min} - ${filter.value.max}`
    }
    
    if (filterConfig?.type === 'checkbox' && Array.isArray(filter.value)) {
      return filter.value.join(', ')
    }
    
    return String(filter.value)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={placeholder}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className={`${showFilters ? 'bg-blue-50 border-blue-200' : ''}`}
        >
          <SlidersHorizontal className="h-4 w-4 mr-2" />
          Filters
          {activeFilters.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFilters.length}
            </Badge>
          )}
        </Button>
        
        {(searchQuery || activeFilters.length > 0) && (
          <Button
            variant="outline"
            onClick={clearAllFilters}
            className="text-red-600 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="flex items-center gap-1 py-1 px-2"
            >
              <span className="text-xs">
                {filter.label}: {formatFilterValue(filter)}
              </span>
              <button
                onClick={() => removeFilter(filter.key)}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}

      {/* Filter Controls */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Advanced Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filters.map((filter) => (
                <div key={filter.key}>
                  {renderFilterControl(filter)}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Hook for using advanced search with data
export function useAdvancedSearch<T>(
  data: T[],
  searchableFields: (keyof T)[],
  filters: FilterValue[]
) {
  return useMemo(() => {
    if (!data) return []
    
    return data.filter(item => {
      // Apply search filters
      const searchMatches = filters.every(filter => {
        const value = item[filter.key as keyof T]
        
        if (value === null || value === undefined) return false
        
        switch (filter.operator) {
          case 'equals':
            if (Array.isArray(filter.value)) {
              return filter.value.includes(String(value))
            }
            return String(value) === String(filter.value)
          
          case 'contains':
            return String(value).toLowerCase().includes(String(filter.value).toLowerCase())
          
          case 'startsWith':
            return String(value).toLowerCase().startsWith(String(filter.value).toLowerCase())
          
          case 'endsWith':
            return String(value).toLowerCase().endsWith(String(filter.value).toLowerCase())
          
          case 'gt':
            return Number(value) > Number(filter.value)
          
          case 'lt':
            return Number(value) < Number(filter.value)
          
          case 'gte':
            return Number(value) >= Number(filter.value)
          
          case 'lte':
            return Number(value) <= Number(filter.value)
          
          case 'between':
            if (filter.value?.min !== undefined && filter.value?.max !== undefined) {
              return Number(value) >= filter.value.min && Number(value) <= filter.value.max
            }
            if (filter.value?.from && filter.value?.to) {
              const dateValue = new Date(String(value))
              return dateValue >= new Date(filter.value.from) && dateValue <= new Date(filter.value.to)
            }
            return false
          
          default:
            return true
        }
      })
      
      return searchMatches
    })
  }, [data, filters])
}