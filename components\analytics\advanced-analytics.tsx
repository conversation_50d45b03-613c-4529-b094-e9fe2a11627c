"use client"

import { useMemo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  ComposedChart,
  Legend,
  ReferenceLine,
  <PERSON>atter<PERSON><PERSON>,
  Scatter
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Calendar,
  Clock,
  Fuel,
  Wrench,
  Car,
  Users,
  MapPin,
  Activity
} from "lucide-react"

interface AnalyticsData {
  vehicles: any[]
  drivers: any[]
  maintenance: any[]
  fuel: any[]
}

interface MetricCard {
  title: string
  value: string | number
  change?: number
  trend?: 'up' | 'down' | 'stable'
  icon: any
  color: string
  description?: string
}

interface ChartData {
  name: string
  value: number
  [key: string]: any
}

export function AdvancedAnalytics({ vehicles, drivers, maintenance, fuel }: AnalyticsData) {
  // Calculate key metrics
  const metrics = useMemo(() => {
    const totalVehicles = vehicles.length
    const activeVehicles = vehicles.filter(v => v.Status === 'Active').length
    const totalDrivers = drivers.length
    const availableDrivers = drivers.filter(d => d.Status === 'Available').length
    const totalMaintenance = maintenance.length
    const overdueMaintenance = maintenance.filter(m => new Date(m['Due Date']) < new Date()).length
    const totalFuelRecords = fuel.length
    const totalFuelCost = fuel.reduce((sum, f) => sum + (f['Cost'] || 0), 0)
    const totalFuelQuantity = fuel.reduce((sum, f) => sum + (f['Quantity (L)'] || 0), 0)
    
    // Calculate utilization rate
    const utilizationRate = totalVehicles > 0 ? (activeVehicles / totalVehicles) * 100 : 0
    
    // Calculate average fuel consumption
    const avgFuelConsumption = fuel.length > 0 
      ? fuel.reduce((sum, f) => sum + (f['Consumption (L/100km)'] || 0), 0) / fuel.length 
      : 0
    
    // Calculate maintenance efficiency
    const maintenanceEfficiency = totalMaintenance > 0 
      ? ((totalMaintenance - overdueMaintenance) / totalMaintenance) * 100 
      : 100

    return {
      totalVehicles,
      activeVehicles,
      totalDrivers,
      availableDrivers,
      totalMaintenance,
      overdueMaintenance,
      totalFuelRecords,
      totalFuelCost,
      totalFuelQuantity,
      utilizationRate,
      avgFuelConsumption,
      maintenanceEfficiency
    }
  }, [vehicles, drivers, maintenance, fuel])

  // Generate vehicle status distribution
  const vehicleStatusData = useMemo(() => {
    const statusCounts = vehicles.reduce((acc, vehicle) => {
      const status = vehicle.Status || 'Unknown'
      acc[status] = (acc[status] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return (Object.entries(statusCounts) as [string, number][]).map(([status, count]) => ({
      name: status,
      value: count,
      percentage: (count / vehicles.length) * 100
    }))
  }, [vehicles])

  // Generate vehicle type distribution
  const vehicleTypeData = useMemo(() => {
    const typeCounts = vehicles.reduce((acc, vehicle) => {
      const type = vehicle.Type || 'Unknown'
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return (Object.entries(typeCounts) as [string, number][]).map(([type, count]) => ({
      name: type,
      value: count,
      percentage: (count / vehicles.length) * 100
    }))
  }, [vehicles])

  // Generate fuel consumption trend
  const fuelTrendData = useMemo(() => {
    const monthlyData = fuel.reduce((acc, record) => {
      const date = new Date(record.Date)
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      if (!acc[monthYear]) {
        acc[monthYear] = { 
          month: monthYear, 
          consumption: 0, 
          cost: 0, 
          quantity: 0, 
          records: 0 
        }
      }
      
      acc[monthYear].consumption += record['Consumption (L/100km)'] || 0
      acc[monthYear].cost += record['Cost'] || 0
      acc[monthYear].quantity += record['Quantity (L)'] || 0
      acc[monthYear].records += 1
      
      return acc
    }, {} as Record<string, any>)

    return Object.values(monthlyData)
      .map((data: any) => ({
        ...data,
        avgConsumption: data.records > 0 ? data.consumption / data.records : 0
      }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }, [fuel])

  // Generate maintenance trend
  const maintenanceTrendData = useMemo(() => {
    const monthlyData = maintenance.reduce((acc, record) => {
      const date = new Date(record['Date'])
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      if (!acc[monthYear]) {
        acc[monthYear] = { 
          month: monthYear, 
          completed: 0, 
          cost: 0, 
          overdue: 0 
        }
      }
      
      acc[monthYear].completed += 1
      acc[monthYear].cost += record['Cost'] || 0
      
      if (new Date(record['Due Date']) < new Date(record['Date'])) {
        acc[monthYear].overdue += 1
      }
      
      return acc
    }, {} as Record<string, any>)

    return Object.values(monthlyData)
      .sort((a: any, b: any) => a.month.localeCompare(b.month))
  }, [maintenance])

  // Generate driver performance data
  const driverPerformanceData = useMemo(() => {
    const driverStats = drivers.map(driver => {
      const driverFuel = fuel.filter(f => f.vehicle_id === driver.assigned_vehicle_id)
      const driverMaintenance = maintenance.filter(m => m.vehicle_id === driver.assigned_vehicle_id)
      
      const avgConsumption = driverFuel.length > 0 
        ? driverFuel.reduce((sum, f) => sum + (f['Consumption (L/100km)'] || 0), 0) / driverFuel.length 
        : 0
      
      const maintenanceCount = driverMaintenance.length
      const totalFuelCost = driverFuel.reduce((sum, f) => sum + (f['Cost'] || 0), 0)
      
      return {
        name: driver.Name,
        avgConsumption,
        maintenanceCount,
        totalFuelCost,
        efficiency: avgConsumption > 0 ? Math.max(0, 100 - (avgConsumption - 8) * 10) : 50
      }
    })

    return driverStats.sort((a, b) => b.efficiency - a.efficiency)
  }, [drivers, fuel, maintenance])

  // Generate cost analysis
  const costAnalysisData = useMemo(() => {
    const totalFuelCost = fuel.reduce((sum, f) => sum + (f['Cost'] || 0), 0)
    const totalMaintenanceCost = maintenance.reduce((sum, m) => sum + (m['Cost'] || 0), 0)
    
    return [
      { category: 'Fuel', cost: totalFuelCost, percentage: 0 },
      { category: 'Maintenance', cost: totalMaintenanceCost, percentage: 0 },
    ].map(item => ({
      ...item,
      percentage: (item.cost / (totalFuelCost + totalMaintenanceCost)) * 100
    }))
  }, [fuel, maintenance])

  // Color scheme for charts
  const colors = ['#2563eb', '#dc2626', '#16a34a', '#ca8a04', '#9333ea', '#c2410c']

  const MetricCard = ({ title, value, change, trend, icon: Icon, color, description }: MetricCard) => (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${color}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change !== undefined && (
          <div className="flex items-center space-x-1 text-sm">
            {trend === 'up' && <TrendingUp className="h-3 w-3 text-green-500" />}
            {trend === 'down' && <TrendingDown className="h-3 w-3 text-red-500" />}
            <span className={`${trend === 'up' ? 'text-green-500' : trend === 'down' ? 'text-red-500' : 'text-gray-500'}`}>
              {change > 0 ? '+' : ''}{change}%
            </span>
          </div>
        )}
        {description && (
          <p className="text-xs text-gray-600 mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Fleet Utilization"
          value={`${metrics.utilizationRate.toFixed(1)}%`}
          change={5.2}
          trend="up"
          icon={Activity}
          color="text-blue-600"
          description={`${metrics.activeVehicles} of ${metrics.totalVehicles} vehicles active`}
        />
        
        <MetricCard
          title="Avg Fuel Consumption"
          value={`${metrics.avgFuelConsumption.toFixed(1)} L/100km`}
          change={-2.1}
          trend="down"
          icon={Fuel}
          color="text-green-600"
          description="Lower is better"
        />
        
        <MetricCard
          title="Maintenance Efficiency"
          value={`${metrics.maintenanceEfficiency.toFixed(1)}%`}
          change={3.5}
          trend="up"
          icon={Wrench}
          color="text-orange-600"
          description={`${metrics.overdueMaintenance} overdue items`}
        />
        
        <MetricCard
          title="Total Fuel Cost"
          value={`$${metrics.totalFuelCost.toLocaleString()}`}
          change={-1.8}
          trend="down"
          icon={DollarSign}
          color="text-purple-600"
          description="This month"
        />
      </div>

      {/* Charts */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="fuel">Fuel Analysis</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="costs">Cost Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Status Distribution</CardTitle>
                <CardDescription>Current status of all vehicles</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={vehicleStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} (${percentage.toFixed(1)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {vehicleStatusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Vehicle Types</CardTitle>
                <CardDescription>Distribution by vehicle type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={vehicleTypeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#2563eb" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="fuel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Fuel Consumption Trend</CardTitle>
              <CardDescription>Monthly fuel consumption and costs</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={fuelTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="quantity" fill="#2563eb" name="Quantity (L)" />
                  <Line yAxisId="right" type="monotone" dataKey="avgConsumption" stroke="#dc2626" name="Avg Consumption (L/100km)" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Total Fuel Consumed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalFuelQuantity.toLocaleString()} L</div>
                <Progress value={75} className="mt-2" />
                <p className="text-xs text-gray-600 mt-1">75% of monthly budget</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Avg Consumption</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.avgFuelConsumption.toFixed(1)} L/100km</div>
                <Badge variant={metrics.avgFuelConsumption < 10 ? "default" : "destructive"} className="mt-2">
                  {metrics.avgFuelConsumption < 10 ? "Efficient" : "High"}
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Fuel Records</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalFuelRecords}</div>
                <p className="text-xs text-gray-600 mt-1">Total fuel records</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Trend</CardTitle>
              <CardDescription>Monthly maintenance activities and costs</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={maintenanceTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="completed" fill="#16a34a" name="Completed" />
                  <Bar yAxisId="left" dataKey="overdue" fill="#dc2626" name="Overdue" />
                  <Line yAxisId="right" type="monotone" dataKey="cost" stroke="#ca8a04" name="Cost ($)" />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Efficiency</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.maintenanceEfficiency.toFixed(1)}%</div>
                <Progress value={metrics.maintenanceEfficiency} className="mt-2" />
                <p className="text-xs text-gray-600 mt-1">On-time completion rate</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Overdue Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{metrics.overdueMaintenance}</div>
                <Badge variant="destructive" className="mt-2">
                  Needs attention
                </Badge>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Total Maintenance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalMaintenance}</div>
                <p className="text-xs text-gray-600 mt-1">Total maintenance records</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Driver Performance</CardTitle>
              <CardDescription>Efficiency ranking based on fuel consumption and maintenance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={driverPerformanceData.slice(0, 10)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="efficiency" fill="#2563eb" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Performers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {driverPerformanceData.slice(0, 5).map((driver, index) => (
                    <div key={driver.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{index + 1}</Badge>
                        <span className="font-medium">{driver.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{driver.efficiency.toFixed(1)}%</div>
                        <div className="text-xs text-gray-600">{driver.avgConsumption.toFixed(1)} L/100km</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Driver Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Drivers</span>
                    <span className="font-medium">{metrics.totalDrivers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Available Drivers</span>
                    <span className="font-medium">{metrics.availableDrivers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Utilization Rate</span>
                    <span className="font-medium">
                      {((metrics.totalDrivers - metrics.availableDrivers) / metrics.totalDrivers * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="costs" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cost Analysis</CardTitle>
              <CardDescription>Breakdown of operational costs</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <PieChart>
                  <Pie
                    data={costAnalysisData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ category, percentage }) => `${category} (${percentage.toFixed(1)}%)`}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="cost"
                  >
                    {costAnalysisData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Cost Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {costAnalysisData.map((item, index) => (
                    <div key={item.category} className="space-y-2">
                      <div className="flex justify-between">
                        <span>{item.category}</span>
                        <span className="font-medium">${item.cost.toLocaleString()}</span>
                      </div>
                      <Progress value={item.percentage} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cost Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Cost per Vehicle</span>
                    <span className="font-medium">
                      ${((metrics.totalFuelCost + maintenance.reduce((sum, m) => sum + (m['Cost'] || 0), 0)) / metrics.totalVehicles).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Avg</span>
                    <span className="font-medium">
                      ${(metrics.totalFuelCost / Math.max(fuelTrendData.length, 1)).toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cost per Liter</span>
                    <span className="font-medium">
                      ${(metrics.totalFuelCost / metrics.totalFuelQuantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}