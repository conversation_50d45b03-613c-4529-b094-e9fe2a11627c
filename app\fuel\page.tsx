"use client"

import { useState, useEffect } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { FuelForm } from "@/components/forms/fuel-form"
import { useAsyncOperation } from "@/hooks/use-async-operation"
import { supabaseApiClient, type FuelRecord } from "@/lib/supabase-api-client"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, BarChart, Bar } from "recharts"
import { Plus, Search, Filter, Edit, Trash2, Fuel, TrendingUp } from "lucide-react"

export default function FuelPage() {
  const [fuelRecords, setFuelRecords] = useState<FuelRecord[]>([])
  const [filteredRecords, setFilteredRecords] = useState<FuelRecord[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingFuel, setEditingFuel] = useState<FuelRecord | null>(null)
  const [deletingFuel, setDeletingFuel] = useState<FuelRecord | null>(null)

  const { execute: loadFuel, isLoading: loadingFuel } = useAsyncOperation({
    onSuccess: (data) => setFuelRecords(data || []),
  })

  const { execute: deleteFuel, isLoading: deletingFuelLoading } = useAsyncOperation({
    successMessage: "Fuel record deleted successfully",
    onSuccess: () => {
      setDeletingFuel(null)
      loadFuelData()
    },
  })

  const loadFuelData = () => {
    loadFuel(() => supabaseApiClient.getFuel()).catch(() => {
      /* error already toasted by useAsyncOperation */
    })
  }

  useEffect(() => {
    loadFuelData()
  }, [])

  useEffect(() => {
    let filtered = fuelRecords

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (record) =>
          record.vehicle_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          record.station.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    setFilteredRecords(filtered)
  }, [fuelRecords, searchTerm])

  const handleEdit = (record: FuelRecord) => {
    setEditingFuel(record)
  }

  const handleDelete = (record: FuelRecord) => {
    setDeletingFuel(record)
  }

  const confirmDelete = () => {
    if (deletingFuel) {
      deleteFuel(() => supabaseApiClient.deleteFuel(deletingFuel.fuel_id))
    }
  }

  const handleFormSuccess = () => {
    setIsAddDialogOpen(false)
    setEditingFuel(null)
    loadFuelData()
  }

  // Calculate statistics
  const totalFuelCost = fuelRecords.reduce((sum, record) => sum + record.cost, 0)
  const avgConsumption =
    fuelRecords.length > 0
      ? fuelRecords.reduce((sum, record) => sum + record["consumption_(L/100km)"], 0) / fuelRecords.length
      : 0
  const totalDistance = fuelRecords.reduce((sum, record) => sum + record["distance_(km)"], 0)
  const totalQuantity = fuelRecords.reduce((sum, record) => sum + record["quantity_(L)"], 0)

  // Mock trend data for charts
  const fuelTrendData = [
    { month: "Sep", consumption: 11.2, cost: 1250 },
    { month: "Oct", consumption: 10.8, cost: 1180 },
    { month: "Nov", consumption: 11.5, cost: 1320 },
    { month: "Dec", consumption: 10.9, cost: 1200 },
    { month: "Jan", consumption: 11.1, cost: 1280 },
  ]

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Fuel Management</h1>
              <p className="text-gray-600 mt-2">Track fuel consumption and costs across your fleet</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Fuel Record
            </Button>
          </div>

          {/* Fuel Statistics */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Fuel Cost</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Fuel className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">${totalFuelCost.toFixed(2)}</div>
                <p className="text-xs text-gray-600 mt-1">All time</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Avg Consumption</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{avgConsumption.toFixed(2)}L</div>
                <p className="text-xs text-gray-600 mt-1">Per 100km</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Distance</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-secondary">
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{totalDistance.toLocaleString()}</div>
                <p className="text-xs text-gray-600 mt-1">Kilometers</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Fuel</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-warning">
                  <Fuel className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{totalQuantity.toFixed(1)}L</div>
                <p className="text-xs text-gray-600 mt-1">Consumed</p>
              </CardContent>
            </Card>
          </div>

          {/* Fuel Trend Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-gray-800">Fuel Consumption Trend</CardTitle>
                <CardDescription className="text-gray-600">Average consumption over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    consumption: {
                      label: "Consumption (L/100km)",
                      color: "hsl(var(--chart-1))",
                    },
                  }}
                  className="h-[200px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={fuelTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                      <XAxis dataKey="month" stroke="#6B7280" />
                      <YAxis stroke="#6B7280" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Line
                        type="monotone"
                        dataKey="consumption"
                        stroke="#3B82F6"
                        strokeWidth={3}
                        dot={{ fill: "#3B82F6", strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-gray-800">Monthly Fuel Costs</CardTitle>
                <CardDescription className="text-gray-600">Total fuel expenses by month</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    cost: {
                      label: "Cost ($)",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="h-[200px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={fuelTrendData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                      <XAxis dataKey="month" stroke="#6B7280" />
                      <YAxis stroke="#6B7280" />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="cost" fill="#8B5CF6" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>

          {/* Fuel Records Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">Fuel Records</CardTitle>
              <CardDescription className="text-gray-600">Complete history of fuel transactions</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search fuel records..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                </Button>
              </div>

              {/* Loading State */}
              {loadingFuel ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading fuel records...</span>
                </div>
              ) : (
                /* Fuel Records Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Vehicle</TableHead>
                        <TableHead className="text-gray-700">Date</TableHead>
                        <TableHead className="text-gray-700">Quantity (L)</TableHead>
                        <TableHead className="text-gray-700">Distance (km)</TableHead>
                        <TableHead className="text-gray-700">Consumption</TableHead>
                        <TableHead className="text-gray-700">Cost</TableHead>
                        <TableHead className="text-gray-700">Station</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                            {searchTerm ? "No fuel records found matching your criteria" : "No fuel records added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredRecords.map((record) => (
                          <TableRow key={record.fuel_id} className="border-gray-100 hover:bg-blue-50/30">
                            <TableCell className="font-medium text-gray-800">{record.vehicle_id}</TableCell>
                            <TableCell className="text-gray-700">{record.date}</TableCell>
                            <TableCell className="text-gray-700">{record["quantity_(L)"]}L</TableCell>
                            <TableCell className="text-gray-700">{record["distance_(km)"]}km</TableCell>
                            <TableCell className="text-gray-700">
                              {record["consumption_(L/100km)"].toFixed(2)}L/100km
                            </TableCell>
                            <TableCell className="text-gray-700">${record.cost.toFixed(2)}</TableCell>
                            <TableCell className="text-gray-700">{record.station}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(record)}
                                  className="hover:bg-blue-100 hover:text-blue-700"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(record)}
                                  className="hover:bg-red-100 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add Fuel Dialog */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Add Fuel Record</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Record a new fuel transaction for a vehicle.
                </DialogDescription>
              </DialogHeader>
              <FuelForm onSuccess={handleFormSuccess} onCancel={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          {/* Edit Fuel Dialog */}
          <Dialog open={!!editingFuel} onOpenChange={() => setEditingFuel(null)}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Edit Fuel Record</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Update the fuel record information below.
                </DialogDescription>
              </DialogHeader>
              {editingFuel && (
                <FuelForm fuel={editingFuel} onSuccess={handleFormSuccess} onCancel={() => setEditingFuel(null)} />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <ConfirmationDialog
            open={!!deletingFuel}
            onOpenChange={() => setDeletingFuel(null)}
            title="Delete Fuel Record"
            description={`Are you sure you want to delete this fuel record? This action cannot be undone.`}
            confirmText="Delete Record"
            onConfirm={confirmDelete}
            variant="destructive"
            loading={deletingFuelLoading}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
