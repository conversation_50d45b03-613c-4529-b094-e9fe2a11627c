import { createClient } from '@supabase/supabase-js'

// Types for our database
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          full_name: string
          email: string
          role: 'Super Admin' | 'Manager' | 'Employee'
          branch_id: string | null
          user_status: 'Active' | 'Inactive' | 'Suspended'
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          full_name: string
          email: string
          role: 'Super Admin' | 'Manager' | 'Employee'
          branch_id?: string | null
          user_status?: 'Active' | 'Inactive' | 'Suspended'
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          email?: string
          role?: 'Super Admin' | 'Manager' | 'Employee'
          branch_id?: string | null
          user_status?: 'Active' | 'Inactive' | 'Suspended'
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      branches: {
        Row: {
          id: string
          name: string
          location: string
          address: string | null
          phone: string | null
          email: string | null
          manager_id: string | null
          branch_status: 'Active' | 'Inactive'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          location: string
          address?: string | null
          phone?: string | null
          email?: string | null
          manager_id?: string | null
          branch_status?: 'Active' | 'Inactive'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          location?: string
          address?: string | null
          phone?: string | null
          email?: string | null
          manager_id?: string | null
          branch_status?: 'Active' | 'Inactive'
          created_at?: string
          updated_at?: string
        }
      }
      drivers: {
        Row: {
          id: string
          code: number
          full_name: string
          license_number: string
          license_expiry: string
          phone: string
          email: string | null
          status: 'Available' | 'Active' | 'Inactive' | 'On Leave'
          hire_date: string
          branch_id: string
          assigned_vehicle_id?: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          code?: number
          full_name: string
          license_number: string
          license_expiry: string
          phone: string
          email?: string | null
          status?: 'Available' | 'Active' | 'Inactive' | 'On Leave'
          hire_date: string
          branch_id: string
          assigned_vehicle_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          code?: number
          full_name?: string
          license_number?: string
          license_expiry?: string
          phone?: string
          email?: string | null
          status?: 'Available' | 'Active' | 'Inactive' | 'On Leave'
          hire_date?: string
          branch_id?: string
          assigned_vehicle_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      vehicles: {
        Row: {
          id: string
          plate_number: string
          vin_number: string | null
          model: number
          vehicle_type: string
          service_type: string
          department: string
          fuel_type: string
          tank_capacity_liters: number | null
          engine_cc: number | null
          color: string | null
          current_km: number
          branch_id: string
          assigned_driver_id: string | null
          vehicle_status: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service'
          status: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service' // Alias for vehicle_status
          next_maintenance_km?: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          plate_number: string
          vin_number?: string | null
          model: number
          vehicle_type: string
          service_type: string
          department: string
          fuel_type: string
          tank_capacity_liters?: number | null
          engine_cc?: number | null
          color?: string | null
          current_km?: number
          branch_id: string
          assigned_driver_id?: string | null
          vehicle_status?: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          plate_number?: string
          vin_number?: string | null
          model?: number
          vehicle_type?: string
          service_type?: string
          department?: string
          fuel_type?: string
          tank_capacity_liters?: number | null
          engine_cc?: number | null
          color?: string | null
          current_km?: number
          branch_id?: string
          assigned_driver_id?: string | null
          vehicle_status?: 'Active' | 'Inactive' | 'Maintenance' | 'Out of Service'
          created_at?: string
          updated_at?: string
        }
      }
      maintenance_records: {
        Row: {
          id: string
          vehicle_id: string
          maintenance_type: string
          description: string | null
          cost: number
          maintenance_date: string
          next_maintenance_date: string | null
          service_provider: string | null
          status: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          vehicle_id: string
          maintenance_type: string
          description?: string | null
          cost: number
          maintenance_date: string
          next_maintenance_date?: string | null
          service_provider?: string | null
          status?: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          vehicle_id?: string
          maintenance_type?: string
          description?: string | null
          cost?: number
          maintenance_date?: string
          next_maintenance_date?: string | null
          service_provider?: string | null
          status?: 'Scheduled' | 'In Progress' | 'Completed' | 'Cancelled'
          created_at?: string
          updated_at?: string
        }
      }
      fuel_records: {
        Row: {
          id: string
          vehicle_id: string
          date: string
          quantity_liters: number
          distance_km: number
          cost: number
          station: string
          consumption_per_100km: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          vehicle_id: string
          date: string
          quantity_liters: number
          distance_km: number
          cost: number
          station: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          vehicle_id?: string
          date?: string
          quantity_liters?: number
          distance_km?: number
          cost?: number
          station?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      dashboard_view: {
        Row: {
          vehicle_id: string
          plate_number: string
          model: number
          vehicle_type: string
          service_type: string
          vehicle_status: string
          current_km: number
          branch_name: string
          department: string
          assigned_driver: string | null
          license_expiry: string | null
          license_days_remaining: number | null
          total_fuel_liters: number
          total_fuel_cost: number
          fuel_efficiency_cost_per_km: number
          total_maintenance_cost: number
          last_maintenance_km: number | null
          next_maintenance_km: number | null
        }
      }
    }
    Functions: {
      get_dropdown_values: {
        Args: {
          column_name: string
        }
        Returns: {
          value: string
        }[]
      }
    }
  }
}

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Client-side Supabase client
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
})

// Client component helper
export const createSupabaseClient = () => createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Service role client (server-side only)
export const createSupabaseServiceClient = () => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  if (!serviceRoleKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
  }
  
  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Auth helpers
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

export const getCurrentUserProfile = async () => {
  const user = await getCurrentUser()
  if (!user) return null
  
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()
    
  if (error) throw error
  return profile
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

// Real-time subscription helpers
export const subscribeToTable = (
  table: keyof Database['public']['Tables'],
  callback: (payload: any) => void,
  filter?: string
) => {
  const channel = supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table,
        filter
      },
      callback
    )
    .subscribe()
    
  return channel
}

export const unsubscribeFromChannel = (channel: any) => {
  supabase.removeChannel(channel)
}

export default supabase
