"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { supabaseApiClient, DashboardData } from "@/lib/supabase-api-client"

export function SimpleDashboardTest() {
  const [data, setData] = useState<DashboardData[] | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)
    try {
      console.log("SimpleDashboardTest - Fetching data directly...")
      const result = await supabaseApiClient.getDashboardData()
      console.log("SimpleDashboardTest - Result:", result)
      console.log("SimpleDashboardTest - Result type:", typeof result)
      console.log("SimpleDashboardTest - Is array:", Array.isArray(result))
      setData(result)
    } catch (err) {
      console.error("SimpleDashboardTest - Error:", err)
      setError(err instanceof Error ? err.message : "Unknown error")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Simple Dashboard Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={fetchData} disabled={loading}>
              {loading ? "Loading..." : "Fetch Data"}
            </Button>
            
            <div className="text-sm">
              <strong>Status:</strong> {loading ? "Loading" : error ? "Error" : "Ready"}
            </div>
            
            {error && (
              <div className="text-red-600">
                <strong>Error:</strong> {error}
              </div>
            )}
            
            <div className="text-sm">
              <strong>Data Type:</strong> {typeof data}<br/>
              <strong>Is Array:</strong> {Array.isArray(data) ? "Yes" : "No"}<br/>
              <strong>Length:</strong> {data?.length || "N/A"}
            </div>
            
            {data && Array.isArray(data) && data.length > 0 && (
              <div>
                <strong>Sample Data (first item):</strong>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto">
                  {JSON.stringify(data[0], null, 2)}
                </pre>
              </div>
            )}
            
            {data && Array.isArray(data) && data.length > 0 && (
              <div>
                <strong>All Vehicle IDs:</strong>
                <div className="text-xs">
                  {data.map((vehicle, index) => (
                    <span key={index} className="inline-block bg-blue-100 px-2 py-1 rounded mr-1 mb-1">
                      {vehicle.vehicle_id || vehicle.plate_number || `Item ${index}`}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}