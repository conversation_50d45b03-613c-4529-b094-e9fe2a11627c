"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAddBranch, useUpdateBranch } from "@/hooks/use-branches"
import { useUsers } from "@/hooks/use-users"
// import { useManagers } from "@/hooks/use-managers"
import { type Branch } from "@/lib/supabase-api-client"
import { Loader2, Building2, MapPin, Phone, Mail, User, CheckCircle, AlertTriangle } from "lucide-react"

const branchSchema = z.object({
  name: z.string().min(1, "Branch name is required").max(100, "Branch name must be less than 100 characters"),
  location: z.string().min(1, "Location is required").max(100, "Location must be less than 100 characters"),
  address: z.string().min(1, "Address is required").max(255, "Address must be less than 255 characters"),
  phone: z.string().min(1, "Phone number is required").regex(/^[0-9+\-\s()]+$/, "Invalid phone number format"),
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  manager_id: z.string().min(1, "Manager is required"),
  status: z.enum(["Active", "Inactive"], {
    required_error: "Status is required",
  }),
})

type BranchFormData = z.infer<typeof branchSchema>

interface BranchFormProps {
  branch?: Branch | null
  onSuccess?: () => void
  onCancel?: () => void
}

export function BranchForm({ branch, onSuccess, onCancel }: BranchFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const addBranchMutation = useAddBranch()
  const updateBranchMutation = useUpdateBranch()
  const { data: users = [], isLoading: loadingUsers } = useUsers()
  // const { managers, isLoading: loadingManagers, error: managersError } = useManagers()
  const managers = users.filter(user => user.role === 'Manager')
  const loadingManagers = loadingUsers
  const managersError = null

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<BranchFormData>({
    resolver: zodResolver(branchSchema),
    defaultValues: {
      name: "",
      location: "",
      address: "",
      phone: "",
      email: "",
      manager_id: "",
      status: "Active",
    },
  })

  const watchedStatus = watch("status")

  useEffect(() => {
    if (branch) {
      console.log("Setting form data for branch:", branch)
      reset({
        name: branch.name || "",
        location: branch.location || "",
        address: branch.address || "",
        phone: branch.phone || "",
        email: branch.email || "",
        manager_id: branch.manager_id || "",
        status: (branch.branch_status as "Active" | "Inactive") || "Active",
      })
    } else {
      console.log("Resetting form for new branch")
      reset({
        name: "",
        location: "",
        address: "",
        phone: "",
        email: "",
        manager_id: "",
        status: "Active",
      })
    }
  }, [branch, reset])

  const onSubmit = async (data: BranchFormData) => {
    if (isSubmitting) return // Prevent double submission
    
    setIsSubmitting(true)
    setSubmitError(null) // Clear previous errors
    
    try {
      console.log("Form submission started:", { branch: branch?.branch_id, data })
      
      if (branch) {
        // Update existing branch
        console.log("Updating branch with ID:", branch.branch_id)
        const result = await updateBranchMutation.mutateAsync({
          id: branch.branch_id,
          data: {
            ...data,
            branch_status: data.status,
            updated_at: new Date().toISOString(),
          },
        })
        console.log("Update result:", result)
      } else {
        // Add new branch
        console.log("Adding new branch")
        const result = await addBranchMutation.mutateAsync({
          ...data,
          branch_id: `branch_${Date.now()}`, // Temporary ID, will be replaced by server
          branch_status: data.status,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        console.log("Add result:", result)
      }
      
      // Reset form and close dialog on success
      reset()
      setSubmitError(null)
      onSuccess?.()
      
    } catch (error) {
      console.error("Form submission error:", error)
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred"
      setSubmitError(errorMessage)
      // Don't reset form on error so user can retry
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get available managers from database
  const availableManagers = managers.length > 0 
    ? managers.filter(manager => manager.id && manager.id.trim() !== '' && manager.name && manager.name.trim() !== '')
    : users.filter(user => 
        user.role === 'Manager' || user.role === 'Admin' || user.role === 'Branch Manager'
      ).map(user => ({ id: user.user_id, name: user.full_name }))
      .filter(manager => manager.id && manager.id.trim() !== '' && manager.name && manager.name.trim() !== '')

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5 text-blue-600" />
          {branch ? "Edit Branch" : "Add New Branch"}
        </CardTitle>
        <CardDescription>
          {branch ? "Update branch information" : "Enter the details for the new branch"}
          {managers.length > 0 && (
            <div className="mt-2 text-sm text-green-600">
              ✓ Manager list loaded from database
            </div>
          )}
          {managersError && (
            <div className="mt-2 text-sm text-amber-600">
              ⚠ Using fallback manager list - Database unavailable
            </div>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Branch Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Branch Name *
            </Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter branch name"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Location */}
          <div className="space-y-2">
            <Label htmlFor="location" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Location *
            </Label>
            <Input
              id="location"
              {...register("location")}
              placeholder="Enter location (e.g., Cairo, Egypt)"
              className={errors.location ? "border-red-500" : ""}
            />
            {errors.location && (
              <p className="text-sm text-red-600">{errors.location.message}</p>
            )}
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label htmlFor="address" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Address *
            </Label>
            <Textarea
              id="address"
              {...register("address")}
              placeholder="Enter full address"
              className={errors.address ? "border-red-500" : ""}
              rows={3}
            />
            {errors.address && (
              <p className="text-sm text-red-600">{errors.address.message}</p>
            )}
          </div>

          {/* Phone and Email Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Phone Number *
              </Label>
              <Input
                id="phone"
                type="tel"
                autoComplete="tel"
                {...register("phone")}
                placeholder="+20-************"
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && (
                <p className="text-sm text-red-600">{errors.phone.message}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email *
              </Label>
              <Input
                id="email"
                type="email"
                autoComplete="email"
                {...register("email")}
                placeholder="<EMAIL>"
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>
          </div>

          {/* Manager and Status Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Manager */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Branch Manager *
              </Label>
              <Select
                value={watch("manager_id") || ""}
                onValueChange={(value) => setValue("manager_id", value)}
              >
                <SelectTrigger className={errors.manager_id ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select manager" />
                </SelectTrigger>
                <SelectContent>
                  {(loadingManagers || loadingUsers) ? (
                    <SelectItem value="loading" disabled>
                      Loading managers...
                    </SelectItem>
                  ) : managersError ? (
                    <SelectItem value="error" disabled>
                      Error loading managers from database
                    </SelectItem>
                  ) : availableManagers.length === 0 ? (
                    <SelectItem value="no-managers" disabled>
                      No managers available
                    </SelectItem>
                  ) : (
                    availableManagers
                      .filter(manager => manager.id && manager.id.trim() !== '')
                      .map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.name}
                          {managers.length === 0 && users.find(u => u.id === manager.id) &&
                            ` (${users.find(u => u.id === manager.id)?.role})`
                          }
                        </SelectItem>
                      ))
                  )}
                </SelectContent>
              </Select>
              {errors.manager_id && (
                <p className="text-sm text-red-600">{errors.manager_id.message}</p>
              )}
            </div>

            {/* Status */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Status *
              </Label>
              <Select
                value={watchedStatus || "Active"}
                onValueChange={(value: "Active" | "Inactive") => setValue("status", value)}
              >
                <SelectTrigger className={errors.status ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              {errors.status && (
                <p className="text-sm text-red-600">{errors.status.message}</p>
              )}
            </div>
          </div>

          {/* Error Display */}
          {submitError && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                <p className="text-sm text-red-800">{submitError}</p>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {branch ? "Updating..." : "Adding..."}
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  {branch ? "Update Branch" : "Add Branch"}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}