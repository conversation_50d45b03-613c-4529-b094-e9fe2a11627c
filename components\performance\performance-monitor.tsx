"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usePerformance, usePerformanceMonitoring } from '@/context/performance-context'
import { 
  Activity, 
  Zap, 
  Database, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react'

export function PerformanceMonitor() {
  const { 
    metrics, 
    isRealtimeEnabled, 
    toggleRealtime, 
    optimizeCache, 
    getPerformanceReport,
    refreshMetrics 
  } = usePerformance()
  
  const { isPerformant, needsOptimization } = usePerformanceMonitoring()
  const [detailedReport, setDetailedReport] = useState<any>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Format memory usage
  const formatMemoryUsage = (bytes: number) => {
    const mb = bytes / (1024 * 1024)
    return `${mb.toFixed(1)} MB`
  }

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(1)}%`
  }

  // Get performance status
  const getPerformanceStatus = () => {
    if (isPerformant) {
      return { status: 'excellent', color: 'green', icon: CheckCircle }
    } else if (needsOptimization) {
      return { status: 'needs-optimization', color: 'red', icon: AlertTriangle }
    } else {
      return { status: 'good', color: 'yellow', icon: Activity }
    }
  }

  const performanceStatus = getPerformanceStatus()
  const StatusIcon = performanceStatus.icon

  // Refresh detailed report
  const refreshReport = async () => {
    setIsRefreshing(true)
    try {
      const report = getPerformanceReport()
      setDetailedReport(report)
      refreshMetrics()
    } catch (error) {
      console.error('Failed to refresh performance report:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(refreshReport, 30000)
    refreshReport() // Initial load
    
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">حالة الأداء</CardTitle>
            <StatusIcon className={`h-4 w-4 text-${performanceStatus.color}-600`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={performanceStatus.status === 'excellent' ? 'default' : 
                             performanceStatus.status === 'good' ? 'secondary' : 'destructive'}>
                {performanceStatus.status === 'excellent' ? 'ممتاز' :
                 performanceStatus.status === 'good' ? 'جيد' : 'يحتاج تحسين'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              متوسط وقت الاستعلام: {metrics.averageQueryTime.toFixed(0)}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الاتصال الفوري</CardTitle>
            {isRealtimeEnabled ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge variant={metrics.realtimeConnected ? 'default' : 'destructive'}>
                {metrics.realtimeConnected ? 'متصل' : 'غير متصل'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.activeSubscriptions} اشتراك نشط
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">معدل الذاكرة المؤقتة</CardTitle>
            <Database className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(metrics.cacheHitRate)}
            </div>
            <Progress value={metrics.cacheHitRate * 100} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {metrics.queryCount} استعلام إجمالي
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">استخدام الذاكرة</CardTitle>
            <BarChart3 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatMemoryUsage(metrics.memoryUsage)}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.slowQueries} استعلام بطيء
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            إجراءات التحسين
          </CardTitle>
          <CardDescription>
            أدوات لتحسين أداء النظام
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={toggleRealtime} 
              variant={isRealtimeEnabled ? "destructive" : "default"}
              size="sm"
            >
              {isRealtimeEnabled ? (
                <>
                  <WifiOff className="h-4 w-4 mr-2" />
                  إيقاف التحديثات الفورية
                </>
              ) : (
                <>
                  <Wifi className="h-4 w-4 mr-2" />
                  تفعيل التحديثات الفورية
                </>
              )}
            </Button>
            
            <Button onClick={optimizeCache} variant="outline" size="sm">
              <Database className="h-4 w-4 mr-2" />
              تحسين الذاكرة المؤقتة
            </Button>
            
            <Button 
              onClick={refreshReport} 
              variant="outline" 
              size="sm"
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              تحديث التقرير
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Performance Report */}
      {detailedReport && (
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
            <TabsTrigger value="queries">الاستعلامات</TabsTrigger>
            <TabsTrigger value="cache">الذاكرة المؤقتة</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات الأداء</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {detailedReport.performance.totalQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">إجمالي الاستعلامات</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {detailedReport.performance.averageTime?.toFixed(0) || 0}ms
                    </div>
                    <div className="text-sm text-muted-foreground">متوسط الوقت</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {detailedReport.performance.slowQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">استعلامات بطيئة</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatPercentage(detailedReport.performance.cacheHitRate || 0)}
                    </div>
                    <div className="text-sm text-muted-foreground">معدل الذاكرة المؤقتة</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="queries" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>أداء الاستعلامات حسب النوع</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(detailedReport.performance.byQueryType || {}).map(([type, stats]: [string, any]) => (
                    <div key={type} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <div className="font-medium">{type}</div>
                        <div className="text-sm text-muted-foreground">
                          {stats.count} استعلام
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">
                          {stats.averageTime?.toFixed(0) || 0}ms
                        </div>
                        <div className="text-sm text-muted-foreground">
                          متوسط الوقت
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="cache" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات الذاكرة المؤقتة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {detailedReport.cache.totalQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">إجمالي الاستعلامات</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {detailedReport.cache.activeQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">استعلامات نشطة</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {detailedReport.cache.staleQueries}
                    </div>
                    <div className="text-sm text-muted-foreground">استعلامات قديمة</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatMemoryUsage(detailedReport.cache.memoryUsage)}
                    </div>
                    <div className="text-sm text-muted-foreground">استخدام الذاكرة</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Performance Recommendations */}
      {needsOptimization && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              توصيات التحسين
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm text-yellow-700">
              {metrics.slowQueries > 5 && (
                <li>• يوجد {metrics.slowQueries} استعلام بطيء - يُنصح بمراجعة الاستعلامات</li>
              )}
              {metrics.memoryUsage > 50 * 1024 * 1024 && (
                <li>• استخدام ذاكرة عالي - يُنصح بتحسين الذاكرة المؤقتة</li>
              )}
              {metrics.cacheHitRate < 0.7 && (
                <li>• معدل الذاكرة المؤقتة منخفض - يُنصح بتحسين استراتيجية التخزين المؤقت</li>
              )}
              {!metrics.realtimeConnected && isRealtimeEnabled && (
                <li>• الاتصال الفوري منقطع - تحقق من الاتصال بالإنترنت</li>
              )}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default PerformanceMonitor
