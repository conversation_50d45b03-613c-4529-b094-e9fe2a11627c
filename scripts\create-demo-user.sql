-- Create demo user for testing authentication
-- This should be run in Supabase SQL Editor

-- Insert a demo user into auth.users table
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$8K1p/a0dhrxSQxhbS2nGSuIXiLyZlJxsB3oy6Z/VwRBtXJvKDxvSm', -- password: admin123
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
);

-- Insert corresponding profile
INSERT INTO profiles (
    id,
    full_name,
    email,
    role,
    branch_id,
    user_status,
    created_at,
    updated_at
) VALUES (
    '11111111-1111-1111-1111-111111111111',
    'System Administrator',
    '<EMAIL>',
    'Super Admin',
    NULL,
    'Active',
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    branch_id = EXCLUDED.branch_id,
    user_status = EXCLUDED.user_status,
    updated_at = NOW();

-- Create a simple test user that can be used immediately
-- Email: <EMAIL>
-- Password: test123

INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '44444444-4444-4444-4444-444444444444',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: test123
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (email) DO NOTHING;

-- Insert corresponding profile
INSERT INTO profiles (
    id,
    full_name,
    email,
    role,
    branch_id,
    user_status,
    created_at,
    updated_at
) VALUES (
    '44444444-4444-4444-4444-444444444444',
    'Test User',
    '<EMAIL>',
    'Super Admin',
    NULL,
    'Active',
    NOW(),
    NOW()
) ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    branch_id = EXCLUDED.branch_id,
    user_status = EXCLUDED.user_status,
    updated_at = NOW();
