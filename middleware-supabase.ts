import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import type { Database } from '@/lib/supabase'

// Define route configurations
const publicRoutes = ['/login', '/unauthorized']
const protectedRoutes = ['/dashboard', '/vehicles', '/drivers', '/maintenance', '/fuel', '/reports', '/users', '/settings', '/admin', '/branches']

// Role-based route access
const roleBasedRoutes = {
  '/admin': ['Super Admin'],
  '/users': ['Super Admin', 'Manager'],
  '/settings': ['Super Admin'],
  '/branches': ['Super Admin']
}

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          res.cookies.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          res.cookies.set({ name, value: '', ...options })
        },
      },
    }
  )
  
  const {
    data: { session },
  } = await supabase.auth.getSession()

  const { pathname } = req.nextUrl
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route))
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))

  // Allow access to public routes
  if (isPublicRoute) {
    // If user is already logged in and trying to access login, redirect to dashboard
    if (session && pathname === '/login') {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }
    return res
  }

  // Check if user is authenticated for protected routes
  if (isProtectedRoute && !session) {
    return NextResponse.redirect(new URL('/login', req.url))
  }

  // If user is authenticated, check their profile and permissions
  if (session && isProtectedRoute) {
    try {
      // Get user profile
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()

      if (error || !profile) {
        console.error('Error fetching profile:', error)
        return NextResponse.redirect(new URL('/login', req.url))
      }

      // Check if user account is active
      if (profile.user_status !== 'Active') {
        return NextResponse.redirect(new URL('/unauthorized?reason=inactive', req.url))
      }

      // Check role-based access
      for (const [route, allowedRoles] of Object.entries(roleBasedRoutes)) {
        if (pathname.startsWith(route)) {
          if (!allowedRoles.includes(profile.role)) {
            return NextResponse.redirect(new URL('/unauthorized?reason=insufficient_role', req.url))
          }
        }
      }

      // Add user info to headers for use in components
      const requestHeaders = new Headers(req.headers)
      requestHeaders.set('x-user-id', session.user.id)
      requestHeaders.set('x-user-role', profile.role)
      requestHeaders.set('x-user-branch', profile.branch_id || '')
      requestHeaders.set('x-user-status', profile.user_status)

      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })

    } catch (error) {
      console.error('Middleware error:', error)
      return NextResponse.redirect(new URL('/login', req.url))
    }
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
