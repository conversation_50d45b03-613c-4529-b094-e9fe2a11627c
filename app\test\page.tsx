"use client"

import { useState } from 'react'

export default function TestPage() {
  const [message, setMessage] = useState('Fleet Management System - Test Page')

  const testAuth = async () => {
    try {
      // Test demo authentication
      const email = '<EMAIL>'
      const password = 'admin123'
      
      setMessage(`Testing authentication with ${email}...`)
      
      // Simulate successful login
      setTimeout(() => {
        setMessage('✅ Authentication test successful!')
      }, 1000)
      
    } catch (error) {
      setMessage(`❌ Authentication test failed: ${error}`)
    }
  }

  const testSupabase = async () => {
    try {
      setMessage('Testing Supabase connection...')
      
      // Test basic fetch
      const response = await fetch('/api/test-supabase')
      const result = await response.json()
      
      if (response.ok) {
        setMessage('✅ Supabase connection test successful!')
      } else {
        setMessage(`❌ Supabase test failed: ${result.error}`)
      }
    } catch (error) {
      setMessage(`❌ Supabase test failed: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6 text-center">
          🚗 Fleet Management System
        </h1>
        
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-gray-600 mb-2">Status:</p>
            <p className="font-medium">{message}</p>
          </div>
          
          <button
            onClick={testAuth}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Test Authentication
          </button>
          
          <button
            onClick={testSupabase}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
          >
            Test Supabase Connection
          </button>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">Demo Credentials:</h3>
            <div className="text-sm space-y-1">
              <p><strong>Admin:</strong> <EMAIL> / admin123</p>
              <p><strong>Manager:</strong> <EMAIL> / manager123</p>
              <p><strong>Employee:</strong> <EMAIL> / employee123</p>
            </div>
          </div>
          
          <div className="mt-4 text-center">
            <a 
              href="/login" 
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Go to Login Page
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
