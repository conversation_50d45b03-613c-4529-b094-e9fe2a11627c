"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { User as SupabaseUser } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'

type Profile = Database['public']['Tables']['profiles']['Row']

interface AuthUser extends Profile {
  supabaseUser: SupabaseUser
}

interface AuthContextType {
  user: AuthUser | null
  profile: Profile | null
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  loading: boolean
  refreshProfile: () => Promise<void>
  hasPermission: (permission: string) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function SupabaseAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Function to fetch and set user profile
  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()

      if (error) {
        console.error('Error fetching profile:', error)
        return null
      }

      const authUser: AuthUser = {
        ...profileData,
        supabaseUser
      }

      setUser(authUser)
      setProfile(profileData)
      
      // Update last login
      await supabase
        .from('profiles')
        .update({ last_login: new Date().toISOString() })
        .eq('id', supabaseUser.id)

      return authUser
    } catch (error) {
      console.error('Error in fetchUserProfile:', error)
      return null
    }
  }

  // Function to refresh profile data
  const refreshProfile = async () => {
    if (!user?.supabaseUser) return
    
    try {
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.supabaseUser.id)
        .single()

      if (error) {
        console.error('Error refreshing profile:', error)
        return
      }

      const updatedUser: AuthUser = {
        ...profileData,
        supabaseUser: user.supabaseUser
      }

      setUser(updatedUser)
      setProfile(profileData)
    } catch (error) {
      console.error('Error in refreshProfile:', error)
    }
  }

  // Permission system
  const hasPermission = (permission: string): boolean => {
    if (!user) return false

    const permissions = {
      "Super Admin": [
        "manage_all_data", 
        "manage_users", 
        "assign_vehicles", 
        "access_all_reports", 
        "view_all_data",
        "manage_branches",
        "delete_records",
        "export_data",
        "system_settings"
      ],
      "Manager": [
        "manage_branch_vehicles", 
        "view_branch_data", 
        "assign_drivers", 
        "access_branch_reports",
        "manage_branch_drivers",
        "schedule_maintenance",
        "add_fuel_records"
      ],
      "Employee": [
        "input_data", 
        "view_assigned_data",
        "add_fuel_records",
        "view_own_reports"
      ],
    }

    return permissions[user.role]?.includes(permission) || false
  }

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
          setLoading(false)
          return
        }

        if (session?.user) {
          await fetchUserProfile(session.user)
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error)
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        if (event === 'SIGNED_IN' && session?.user) {
          await fetchUserProfile(session.user)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          // Optionally refresh profile on token refresh
          await fetchUserProfile(session.user)
        }
        
        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Login error:', error)
        return { 
          success: false, 
          error: error.message || 'فشل في تسجيل الدخول' 
        }
      }

      if (data.user) {
        const authUser = await fetchUserProfile(data.user)
        if (!authUser) {
          return { 
            success: false, 
            error: 'لم يتم العثور على ملف المستخدم' 
          }
        }
        
        // Check if user is active
        if (authUser.user_status !== 'Active') {
          await supabase.auth.signOut()
          return { 
            success: false, 
            error: 'حساب المستخدم غير نشط' 
          }
        }

        return { success: true }
      }

      return { 
        success: false, 
        error: 'فشل في تسجيل الدخول' 
      }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: 'حدث خطأ أثناء تسجيل الدخول' 
      }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        console.error('Logout error:', error)
      }
      
      setUser(null)
      setProfile(null)
      router.push("/login")
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      profile, 
      login, 
      logout, 
      loading, 
      refreshProfile,
      hasPermission
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useSupabaseAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useSupabaseAuth must be used within a SupabaseAuthProvider")
  }
  return context
}

// Export for backward compatibility
export const useAuth = useSupabaseAuth
