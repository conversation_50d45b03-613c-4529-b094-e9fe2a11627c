"use client"

import { useState, useEffect } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { UserForm } from "@/components/forms/user-form"
import { useAsyncOperation } from "@/hooks/use-async-operation"
import { supabaseApiClient, type User } from "@/lib/supabase-api-client"
import { useAuth } from "@/context/auth-context"
import { Plus, Search, Filter, Edit, Trash2, Users, Shield, User<PERSON>he<PERSON>, Key } from "lucide-react"

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [deletingUser, setDeletingUser] = useState<User | null>(null)
  const { hasPermission } = useAuth()

  const { execute: loadUsers, isLoading: loadingUsers } = useAsyncOperation({
    onSuccess: (data) => setUsers(data || []),
  })

  const { execute: deleteUser, isLoading: deletingUserLoading } = useAsyncOperation({
    successMessage: "User deleted successfully",
    onSuccess: () => {
      setDeletingUser(null)
      loadUsersData()
    },
  })

  const { execute: toggleUserStatus } = useAsyncOperation({
    successMessage: "User status updated successfully",
    onSuccess: () => loadUsersData(),
  })

  const { execute: resetPassword } = useAsyncOperation({
    successMessage: "Password reset email sent successfully",
  })

  const loadUsersData = () => {
    loadUsers(() => supabaseApiClient.getUsers()).catch(() => {
      /* error already toasted by useAsyncOperation */
    })
  }

  useEffect(() => {
    loadUsersData()
  }, [])

  useEffect(() => {
    let filtered = users

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.role.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Filter by role
    if (roleFilter !== "all") {
      filtered = filtered.filter((user) => user.role === roleFilter)
    }

    setFilteredUsers(filtered)
  }, [users, searchTerm, roleFilter])

  const getRoleColor = (role: string) => {
    switch (role) {
      case "Super Admin":
        return "bg-red-100 text-red-800 border-red-200"
      case "Manager":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "Employee":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 border-green-200"
      case "Inactive":
        return "bg-gray-100 text-gray-800 border-gray-200"
      case "Suspended":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const handleEdit = (user: User) => {
    setEditingUser(user)
  }

  const handleDelete = (user: User) => {
    setDeletingUser(user)
  }

  const confirmDelete = () => {
    if (deletingUser) {
      deleteUser(() => supabaseApiClient.deleteUser(deletingUser.user_id))
    }
  }

  const handleStatusToggle = (userId: string, currentStatus: string) => {
    const newStatus = currentStatus === "Active" ? "Inactive" : "Active"
    toggleUserStatus(() => supabaseApiClient.updateUser(userId, { user_status: newStatus }))
  }

  const handleResetPassword = (userId: string) => {
    resetPassword(() => Promise.resolve()) // Mock implementation
  }

  const handleFormSuccess = () => {
    setIsAddDialogOpen(false)
    setEditingUser(null)
    loadUsersData()
  }

  const activeUsers = users.filter((u) => u.user_status === "Active").length
  const totalManagers = users.filter((u) => u.role === "Manager").length
  const totalEmployees = users.filter((u) => u.role === "Employee").length
  const totalAdmins = users.filter((u) => u.role === "Super Admin").length

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">User Management</h1>
              <p className="text-gray-600 mt-2">Manage system users, roles, and permissions</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>

          {/* User Statistics */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Users</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{users.length}</div>
                <p className="text-xs text-gray-600 mt-1">Registered users</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Active Users</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <UserCheck className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{activeUsers}</div>
                <p className="text-xs text-gray-600 mt-1">Currently active</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Managers</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-secondary">
                  <Shield className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{totalManagers}</div>
                <p className="text-xs text-gray-600 mt-1">Management level</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Employees</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-warning">
                  <Users className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{totalEmployees}</div>
                <p className="text-xs text-gray-600 mt-1">Employee level</p>
              </CardContent>
            </Card>
          </div>

          {/* Users Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">System Users</CardTitle>
              <CardDescription className="text-gray-600">Manage user accounts, roles, and permissions</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Roles</option>
                    <option value="Super Admin">Super Admin</option>
                    <option value="Manager">Manager</option>
                    <option value="Employee">Employee</option>
                  </select>
                  <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingUsers ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading users...</span>
                </div>
              ) : (
                /* Users Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Name</TableHead>
                        <TableHead className="text-gray-700">Email</TableHead>
                        <TableHead className="text-gray-700">Role</TableHead>
                        <TableHead className="text-gray-700">Branch</TableHead>
                        <TableHead className="text-gray-700">Status</TableHead>
                        <TableHead className="text-gray-700">Last Login</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                            {searchTerm || roleFilter !== "all"
                              ? "No users found matching your criteria"
                              : "No users added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredUsers.map((user) => (
                          <TableRow key={user.user_id} className="border-gray-100 hover:bg-blue-50/30">
                            <TableCell className="font-medium text-gray-800">{user.full_name}</TableCell>
                            <TableCell className="text-gray-700">{user.email}</TableCell>
                            <TableCell>
                              <Badge className={getRoleColor(user.role)}>{user.role}</Badge>
                            </TableCell>
                            <TableCell className="text-gray-700">{user.branch_id || "All Branches"}</TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(user.user_status)}>{user.user_status}</Badge>
                            </TableCell>
                            <TableCell className="text-sm text-gray-600">{user.last_login || "Never"}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  key={`edit-${user.user_id}`}
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(user)}
                                  className="hover:bg-blue-100 hover:text-blue-700"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  key={`status-${user.user_id}`}
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleStatusToggle(user.user_id, user.user_status)}
                                  className="hover:bg-yellow-100 hover:text-yellow-700"
                                >
                                  {user.user_status === "Active" ? "Deactivate" : "Activate"}
                                </Button>
                                <Button
                                  key={`reset-${user.user_id}`}
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleResetPassword(user.user_id)}
                                  className="hover:bg-green-100 hover:text-green-700"
                                >
                                  <Key className="h-4 w-4" />
                                </Button>
                                <Button
                                  key={`delete-${user.user_id}`}
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(user)}
                                  className="hover:bg-red-100 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add User Dialog */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Add New User</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Create a new user account with appropriate role and permissions.
                </DialogDescription>
              </DialogHeader>
              <UserForm onSuccess={handleFormSuccess} onCancel={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          {/* Edit User Dialog */}
          <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Edit User</DialogTitle>
                <DialogDescription className="text-gray-600">Update the user information below.</DialogDescription>
              </DialogHeader>
              {editingUser && (
                <UserForm user={editingUser} onSuccess={handleFormSuccess} onCancel={() => setEditingUser(null)} />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <ConfirmationDialog
            open={!!deletingUser}
            onOpenChange={() => setDeletingUser(null)}
            title="Delete User"
            description={`Are you sure you want to delete user "${deletingUser?.full_name}"? This action cannot be undone.`}
            confirmText="Delete User"
            onConfirm={confirmDelete}
            variant="destructive"
            loading={deletingUserLoading}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
