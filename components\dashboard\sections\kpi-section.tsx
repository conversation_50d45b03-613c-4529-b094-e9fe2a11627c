"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { DashboardData } from "@/lib/supabase-api-client"
import { 
  Car, 
  Users, 
  Building, 
  Fuel, 
  DollarSign, 
  Wrench, 
  Calendar,
  Shield,
  Activity,
  AlertTriangle
} from "lucide-react"

interface KPISectionProps {
  data: DashboardData[]
}

export function KPISection({ data }: KPISectionProps) {
  // Calculate KPIs
  const totalVehicles = data.length
  const activeVehicles = data.filter(vehicle => vehicle.vehicle_status === "Active").length
  const inactiveVehicles = data.filter(vehicle => 
    vehicle.vehicle_status === "Inactive" || vehicle.vehicle_status === "Maintenance"
  ).length
  
  const assignedDrivers = new Set(
    data.filter(vehicle => vehicle.assigned_driver && vehicle.assigned_driver.trim() !== "")
      .map(vehicle => vehicle.assigned_driver)
  ).size
  
  const totalBranches = new Set(
    data.filter(vehicle => vehicle.branch_name && vehicle.branch_name.trim() !== "")
      .map(vehicle => vehicle.branch_name)
  ).size
  
  const totalFuelConsumed = data.reduce((sum, vehicle) => 
    sum + (parseFloat(vehicle.total_fuel_liters?.toString() || "0") || 0), 0
  )
  
  const totalFuelCost = data.reduce((sum, vehicle) => 
    sum + (parseFloat(vehicle.total_fuel_cost_egp?.toString() || "0") || 0), 0
  )
  
  const totalMaintenanceCost = data.reduce((sum, vehicle) => 
    sum + (parseFloat(vehicle.total_maintenance_cost_egp?.toString() || "0") || 0), 0
  )
  
  const avgLicenseDaysRemaining = data.length > 0 ? 
    data.reduce((sum, vehicle) => 
      sum + (parseFloat(vehicle.license_days_remaining?.toString() || "0") || 0), 0
    ) / data.length : 0
  
  const avgInsuranceDaysRemaining = data.length > 0 ? 
    data.reduce((sum, vehicle) => 
      sum + (parseFloat(vehicle.insurance_days_remaining?.toString() || "0") || 0), 0
    ) / data.length : 0

  const kpiCards = [
    {
      title: "Total Vehicles",
      value: totalVehicles.toLocaleString(),
      description: "Total number of vehicles in the fleet",
      icon: Car,
      color: "text-blue-600"
    },
    {
      title: "Active Vehicles",
      value: activeVehicles.toLocaleString(),
      description: "Vehicles currently operational",
      icon: Activity,
      color: "text-green-600"
    },
    {
      title: "Inactive Vehicles",
      value: inactiveVehicles.toLocaleString(),
      description: "Vehicles inactive or under maintenance",
      icon: AlertTriangle,
      color: "text-red-600"
    },
    {
      title: "Assigned Drivers",
      value: assignedDrivers.toLocaleString(),
      description: "Drivers assigned to vehicles",
      icon: Users,
      color: "text-purple-600"
    },
    {
      title: "Total Branches",
      value: totalBranches.toLocaleString(),
      description: "Operational branches managing the fleet",
      icon: Building,
      color: "text-indigo-600"
    },
    {
      title: "Total Fuel Consumed",
      value: `${totalFuelConsumed.toLocaleString()} L`,
      description: "Total fuel consumed by the fleet",
      icon: Fuel,
      color: "text-orange-600"
    },
    {
      title: "Total Fuel Cost",
      value: `${totalFuelCost.toLocaleString()} EGP`,
      description: "Total amount spent on fuel",
      icon: DollarSign,
      color: "text-yellow-600"
    },
    {
      title: "Total Maintenance Cost",
      value: `${totalMaintenanceCost.toLocaleString()} EGP`,
      description: "Total maintenance cost for the fleet",
      icon: Wrench,
      color: "text-gray-600"
    },
    {
      title: "Avg License Days Remaining",
      value: Math.round(avgLicenseDaysRemaining).toLocaleString(),
      description: "Average days before license expiration",
      icon: Calendar,
      color: "text-cyan-600"
    },
    {
      title: "Avg Insurance Days Remaining",
      value: Math.round(avgInsuranceDaysRemaining).toLocaleString(),
      description: "Average days before insurance expiration",
      icon: Shield,
      color: "text-teal-600"
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Fleet KPIs</h2>
        <p className="text-gray-600">Key performance indicators for your fleet</p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        {kpiCards.map((kpi, index) => {
          const IconComponent = kpi.icon
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {kpi.title}
                </CardTitle>
                <IconComponent className={`h-4 w-4 ${kpi.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {kpi.value}
                </div>
                <CardDescription className="text-xs">
                  {kpi.description}
                </CardDescription>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}