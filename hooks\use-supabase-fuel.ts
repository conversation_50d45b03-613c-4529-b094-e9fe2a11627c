import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient } from '@/lib/supabase-api-client'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'

type FuelRecord = Database['public']['Tables']['fuel_records']['Row']
type FuelInsert = Database['public']['Tables']['fuel_records']['Insert']
type FuelUpdate = Database['public']['Tables']['fuel_records']['Update']

// Query keys
export const fuelKeys = {
  all: ['fuel'] as const,
  lists: () => [...fuelKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...fuelKeys.lists(), { filters }] as const,
  details: () => [...fuelKeys.all, 'detail'] as const,
  detail: (id: string) => [...fuelKeys.details(), id] as const,
}

// ==================== QUERIES ====================

export function useFuel(filters?: Record<string, any>) {
  return useQuery({
    queryKey: fuelKeys.list(filters || {}),
    queryFn: () => supabaseApiClient.getFuel(),
    staleTime: 3 * 60 * 1000, // 3 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useFuelRecord(id: string) {
  return useQuery({
    queryKey: fuelKeys.detail(id),
    queryFn: () => supabaseApiClient.getFuelRecord(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// ==================== MUTATIONS ====================

export function useAddFuel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (fuelData: FuelInsert) => 
      supabaseApiClient.addFuel(fuelData),
    
    onSuccess: (newRecord) => {
      // Invalidate and refetch fuel list
      queryClient.invalidateQueries({ queryKey: fuelKeys.lists() })
      
      // Add the new record to the cache
      queryClient.setQueryData(
        fuelKeys.detail(newRecord.id),
        newRecord
      )
      
      toast.success('تم إضافة سجل الوقود بنجاح')
    },
    
    onError: (error) => {
      console.error('Add fuel error:', error)
      toast.error('فشل في إضافة سجل الوقود')
    }
  })
}

export function useUpdateFuel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: FuelUpdate }) =>
      supabaseApiClient.updateFuel(id, data),
    
    onSuccess: (updatedRecord) => {
      // Update the specific record in cache
      queryClient.setQueryData(
        fuelKeys.detail(updatedRecord.id),
        updatedRecord
      )
      
      // Invalidate fuel list to reflect changes
      queryClient.invalidateQueries({ queryKey: fuelKeys.lists() })
      
      toast.success('تم تحديث سجل الوقود بنجاح')
    },
    
    onError: (error) => {
      console.error('Update fuel error:', error)
      toast.error('فشل في تحديث سجل الوقود')
    }
  })
}

export function useDeleteFuel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteFuel(id),
    
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: fuelKeys.detail(deletedId) })
      
      // Invalidate fuel list
      queryClient.invalidateQueries({ queryKey: fuelKeys.lists() })
      
      toast.success('تم حذف سجل الوقود بنجاح')
    },
    
    onError: (error) => {
      console.error('Delete fuel error:', error)
      toast.error('فشل في حذف سجل الوقود')
    }
  })
}

// ==================== COMPUTED QUERIES ====================

export function useFuelStats() {
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: [...fuelKeys.all, 'stats'],
    queryFn: () => {
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      const thisYear = new Date(now.getFullYear(), 0, 1)
      
      const thisMonthRecords = fuel.filter(f => new Date(f.date) >= thisMonth)
      const lastMonthRecords = fuel.filter(f => {
        const date = new Date(f.date)
        return date >= lastMonth && date < thisMonth
      })
      const thisYearRecords = fuel.filter(f => new Date(f.date) >= thisYear)
      
      const stats = {
        total: {
          records: fuel.length,
          liters: Math.round(fuel.reduce((sum, f) => sum + f.quantity_liters, 0) * 100) / 100,
          cost: Math.round(fuel.reduce((sum, f) => sum + f.cost, 0) * 100) / 100,
          distance: Math.round(fuel.reduce((sum, f) => sum + f.distance_km, 0) * 100) / 100
        },
        thisMonth: {
          records: thisMonthRecords.length,
          liters: Math.round(thisMonthRecords.reduce((sum, f) => sum + f.quantity_liters, 0) * 100) / 100,
          cost: Math.round(thisMonthRecords.reduce((sum, f) => sum + f.cost, 0) * 100) / 100,
          distance: Math.round(thisMonthRecords.reduce((sum, f) => sum + f.distance_km, 0) * 100) / 100
        },
        lastMonth: {
          records: lastMonthRecords.length,
          liters: Math.round(lastMonthRecords.reduce((sum, f) => sum + f.quantity_liters, 0) * 100) / 100,
          cost: Math.round(lastMonthRecords.reduce((sum, f) => sum + f.cost, 0) * 100) / 100,
          distance: Math.round(lastMonthRecords.reduce((sum, f) => sum + f.distance_km, 0) * 100) / 100
        },
        thisYear: {
          records: thisYearRecords.length,
          liters: Math.round(thisYearRecords.reduce((sum, f) => sum + f.quantity_liters, 0) * 100) / 100,
          cost: Math.round(thisYearRecords.reduce((sum, f) => sum + f.cost, 0) * 100) / 100,
          distance: Math.round(thisYearRecords.reduce((sum, f) => sum + f.distance_km, 0) * 100) / 100
        },
        averageConsumption: (() => {
          const validRecords = fuel.filter(f => f.consumption_per_100km && f.consumption_per_100km > 0)
          return validRecords.length > 0 
            ? Math.round(validRecords.reduce((sum, f) => sum + (f.consumption_per_100km || 0), 0) / validRecords.length * 100) / 100
            : 0
        })(),
        averageCostPerLiter: fuel.length > 0 
          ? Math.round((fuel.reduce((sum, f) => sum + f.cost, 0) / fuel.reduce((sum, f) => sum + f.quantity_liters, 0)) * 100) / 100
          : 0,
        byVehicle: fuel.reduce((acc, record) => {
          const plateNumber = (record as any).vehicles?.plate_number || 'Unknown'
          if (!acc[plateNumber]) {
            acc[plateNumber] = { records: 0, liters: 0, cost: 0, distance: 0 }
          }
          acc[plateNumber].records++
          acc[plateNumber].liters += record.quantity_liters
          acc[plateNumber].cost += record.cost
          acc[plateNumber].distance += record.distance_km
          return acc
        }, {} as Record<string, { records: number; liters: number; cost: number; distance: number }>),
        byStation: fuel.reduce((acc, record) => {
          if (!acc[record.station]) {
            acc[record.station] = { records: 0, liters: 0, cost: 0 }
          }
          acc[record.station].records++
          acc[record.station].liters += record.quantity_liters
          acc[record.station].cost += record.cost
          return acc
        }, {} as Record<string, { records: number; liters: number; cost: number }>)
      }
      
      return stats
    },
    enabled: fuel.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useFuelByVehicle(vehicleId?: string) {
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: [...fuelKeys.all, 'by-vehicle', vehicleId],
    queryFn: () => {
      if (!vehicleId) return fuel
      return fuel
        .filter(record => record.vehicle_id === vehicleId)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    },
    enabled: !!fuel.length,
    staleTime: 5 * 60 * 1000,
  })
}

export function useFuelByDateRange(startDate?: string, endDate?: string) {
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: [...fuelKeys.all, 'by-date-range', startDate, endDate],
    queryFn: () => {
      if (!startDate || !endDate) return fuel
      
      const start = new Date(startDate)
      const end = new Date(endDate)
      
      return fuel
        .filter(record => {
          const recordDate = new Date(record.date)
          return recordDate >= start && recordDate <= end
        })
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    },
    enabled: !!fuel.length && !!startDate && !!endDate,
    staleTime: 5 * 60 * 1000,
  })
}

export function useRecentFuel(days: number = 7) {
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: [...fuelKeys.all, 'recent', days],
    queryFn: () => {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)
      
      return fuel
        .filter(record => new Date(record.date) >= cutoffDate)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    },
    enabled: !!fuel.length,
    staleTime: 1 * 60 * 1000, // 1 minute for recent data
  })
}

export function useFuelEfficiencyAnalysis(vehicleId?: string) {
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: [...fuelKeys.all, 'efficiency-analysis', vehicleId],
    queryFn: () => {
      const relevantRecords = vehicleId 
        ? fuel.filter(f => f.vehicle_id === vehicleId)
        : fuel
      
      const validRecords = relevantRecords.filter(f => 
        f.consumption_per_100km && f.consumption_per_100km > 0
      )
      
      if (validRecords.length === 0) {
        return {
          averageConsumption: 0,
          bestConsumption: 0,
          worstConsumption: 0,
          trend: 'stable',
          monthlyTrend: []
        }
      }
      
      const consumptions = validRecords.map(f => f.consumption_per_100km!).sort((a, b) => a - b)
      const average = consumptions.reduce((sum, c) => sum + c, 0) / consumptions.length
      
      // Calculate monthly trend
      const monthlyData = validRecords.reduce((acc, record) => {
        const month = new Date(record.date).toISOString().slice(0, 7) // YYYY-MM
        if (!acc[month]) {
          acc[month] = []
        }
        acc[month].push(record.consumption_per_100km!)
        return acc
      }, {} as Record<string, number[]>)
      
      const monthlyTrend = Object.entries(monthlyData)
        .map(([month, consumptions]) => ({
          month,
          average: consumptions.reduce((sum, c) => sum + c, 0) / consumptions.length
        }))
        .sort((a, b) => a.month.localeCompare(b.month))
      
      // Determine trend
      let trend = 'stable'
      if (monthlyTrend.length >= 2) {
        const recent = monthlyTrend.slice(-3).map(m => m.average)
        const older = monthlyTrend.slice(-6, -3).map(m => m.average)
        
        if (recent.length > 0 && older.length > 0) {
          const recentAvg = recent.reduce((sum, c) => sum + c, 0) / recent.length
          const olderAvg = older.reduce((sum, c) => sum + c, 0) / older.length
          
          if (recentAvg < olderAvg * 0.95) trend = 'improving'
          else if (recentAvg > olderAvg * 1.05) trend = 'worsening'
        }
      }
      
      return {
        averageConsumption: Math.round(average * 100) / 100,
        bestConsumption: Math.round(consumptions[0] * 100) / 100,
        worstConsumption: Math.round(consumptions[consumptions.length - 1] * 100) / 100,
        trend,
        monthlyTrend
      }
    },
    enabled: !!fuel.length,
    staleTime: 5 * 60 * 1000,
  })
}

// ==================== BULK OPERATIONS ====================

export function useBulkUpdateFuel() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: FuelUpdate }>) => {
      const results = await Promise.allSettled(
        updates.map(({ id, data }) => supabaseApiClient.updateFuel(id, data))
      )
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      
      return { successful, failed, total: updates.length }
    },
    
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: fuelKeys.all })
      
      if (result.failed > 0) {
        toast.warning(`تم تحديث ${result.successful} سجل، فشل في ${result.failed}`)
      } else {
        toast.success(`تم تحديث ${result.successful} سجل وقود بنجاح`)
      }
    },
    
    onError: () => {
      toast.error('فشل في التحديث المجمع لسجلات الوقود')
    }
  })
}

// ==================== REAL-TIME SUBSCRIPTIONS ====================

export function useFuelSubscription() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: [...fuelKeys.all, 'subscription'],
    queryFn: () => {
      // Set up real-time subscription
      const subscription = supabase
        .channel('fuel_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'fuel_records'
          },
          (payload: any) => {
            console.log('Fuel change detected:', payload)
            
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: fuelKeys.all })
            
            // Show notification for real-time updates
            if (payload.eventType === 'INSERT') {
              toast.info('تم إضافة سجل وقود جديد')
            } else if (payload.eventType === 'UPDATE') {
              toast.info('تم تحديث سجل وقود')
            } else if (payload.eventType === 'DELETE') {
              toast.info('تم حذف سجل وقود')
            }
          }
        )
        .subscribe()

      return subscription
    },
    staleTime: Infinity, // Never stale
    gcTime: Infinity, // Never garbage collect
  })
}
