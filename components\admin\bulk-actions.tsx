"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { LoadingButton } from "@/components/ui/loading-button"
import { useToast } from "@/hooks/use-toast"
import { useDropdownValues } from "@/hooks/use-dropdown-values"
import { Upload, Download, RefreshCw, Trash2 } from "lucide-react"

const DROPDOWN_CATEGORIES = [
  { id: "vehicle_type", name: "Vehicle Type" },
  { id: "service_type", name: "Service Type" },
  { id: "fuel_type", name: "Fuel Type" },
  { id: "color", name: "Color" },
  { id: "department", name: "Department" },
  { id: "status", name: "Status" }
]

export function BulkActions() {
  const { categories, addValue } = useDropdownValues()
  const [selectedCategory, setSelectedCategory] = useState("")
  const [bulkValues, setBulkValues] = useState("")
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const handleBulkImport = async () => {
    if (!selectedCategory || !bulkValues.trim()) return

    setIsLoading(true)
    try {
      const values = bulkValues
        .split('\n')
        .map(v => v.trim())
        .filter(v => v.length > 0)
        .filter((value, index, array) => array.indexOf(value) === index) // Remove duplicates

      let successCount = 0
      let errorCount = 0

      for (const value of values) {
        const success = addValue(selectedCategory, value)
        if (success) {
          successCount++
        } else {
          errorCount++
        }
      }

      setBulkValues("")
      setSelectedCategory("")
      setIsImportDialogOpen(false)

      toast({
        title: "Bulk Import Complete",
        description: `Successfully imported ${successCount} values. ${errorCount > 0 ? `${errorCount} failed.` : ''}`,
      })
    } catch (error) {
      toast({
        title: "Import Error",
        description: "Failed to import values. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleExport = () => {
    try {
      const exportData = categories.map(category => ({
        category: category.name,
        values: category.values.map(v => ({
          value: v.value,
          isActive: v.isActive,
          createdAt: v.createdAt,
          updatedAt: v.updatedAt
        }))
      }))

      const dataStr = JSON.stringify(exportData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `dropdown-values-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "Export Complete",
        description: "Dropdown values have been exported successfully.",
      })
    } catch (error) {
      toast({
        title: "Export Error",
        description: "Failed to export values. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleResetToDefaults = () => {
    try {
      localStorage.removeItem("dropdown_categories")
      window.location.reload()
    } catch (error) {
      toast({
        title: "Reset Error",
        description: "Failed to reset to defaults. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="glass-effect mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5 text-blue-600" />
          Bulk Actions
        </CardTitle>
        <CardDescription>
          Perform bulk operations on dropdown values
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-3">
          {/* Bulk Import */}
          <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Upload className="h-4 w-4" />
                Bulk Import
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Bulk Import Values</DialogTitle>
                <DialogDescription>
                  Import multiple values at once. Enter one value per line.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="category-select">Category</Label>
                  <Select value={selectedCategory || "none"} onValueChange={(value) => setSelectedCategory(value === "none" ? "" : value)}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none" disabled>Select category</SelectItem>
                      {DROPDOWN_CATEGORIES.map((cat) => (
                        <SelectItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="bulk-values">Values (one per line)</Label>
                  <Textarea
                    id="bulk-values"
                    value={bulkValues}
                    onChange={(e) => setBulkValues(e.target.value)}
                    placeholder="Value 1&#10;Value 2&#10;Value 3"
                    rows={6}
                    className="mt-1"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsImportDialogOpen(false)
                    setBulkValues("")
                    setSelectedCategory("")
                  }}
                >
                  Cancel
                </Button>
                <LoadingButton 
                  onClick={handleBulkImport}
                  loading={isLoading}
                  disabled={!selectedCategory || !bulkValues.trim()}
                  className="gradient-bg-primary text-blue-700 hover:opacity-90"
                >
                  Import Values
                </LoadingButton>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Export */}
          <Button 
            variant="outline" 
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export All
          </Button>

          {/* Reset to Defaults */}
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                variant="outline" 
                className="flex items-center gap-2 hover:bg-red-50 hover:text-red-700 hover:border-red-200"
              >
                <Trash2 className="h-4 w-4" />
                Reset to Defaults
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Reset to Default Values</AlertDialogTitle>
                <AlertDialogDescription>
                  This will remove all custom dropdown values and restore the system defaults. 
                  This action cannot be undone. Are you sure you want to continue?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleResetToDefaults}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Reset to Defaults
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  )
}