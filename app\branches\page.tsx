"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { BranchForm } from "@/components/forms/branch-form"
import { useBranches, useFilteredBranches, useBranchStats, useDeleteBranch } from "@/hooks/use-branches"
import { useUsers } from "@/hooks/use-users"

import { type Branch } from "@/lib/supabase-api-client"
import { Plus, Search, Filter, Edit, Trash2, AlertTriangle, CheckCircle, Building2, MapPin, Phone, Mail, User } from "lucide-react"

export default function BranchesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [locationFilter, setLocationFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null)
  const [deletingBranch, setDeletingBranch] = useState<Branch | null>(null)

  // React Query hooks
  const { data: allBranches, isLoading: loadingAllBranches, error: allBranchesError } = useBranches()
  const { data: users = [] } = useUsers()

  const managers = users.filter(user => user.role === 'Manager')

  const { data: filteredBranches = [], isLoading: loadingFilteredBranches, error: filteredBranchesError } = useFilteredBranches(
    allBranches,
    {
      branch_status: statusFilter,
      location: locationFilter,
      searchTerm,
    }
  )

  const { data: branchStats, isLoading: loadingBranchStats, error: branchStatsError } = useBranchStats(allBranches)
  const deleteBranchMutation = useDeleteBranch()

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "inactive":
        return "bg-red-100 text-red-800 border-red-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getManagerName = (managerId: string) => {
    // Find manager in database
    const manager = managers.find(manager => manager.id === managerId)
    if (manager) {
      return manager.name
    }
    
    // Fallback to users data
    const user = users.find(user => user.id === managerId)
    return user ? user.full_name : "Unknown Manager"
  }

  const handleEdit = (branch: Branch) => {
    setEditingBranch(branch)
  }

  const handleDelete = (branch: Branch) => {
    setDeletingBranch(branch)
  }

  const confirmDelete = () => {
    if (deletingBranch) {
      deleteBranchMutation.mutate(deletingBranch.branch_id, {
        onSuccess: () => {
          setDeletingBranch(null)
        },
      })
    }
  }

  const handleFormSuccess = () => {
    console.log("Form success callback triggered")
    setIsAddDialogOpen(false)
    setEditingBranch(null)
  }

  const handleEditDialogClose = () => {
    console.log("Edit dialog close triggered")
    setEditingBranch(null)
  }

  // Get unique locations for filter
  const uniqueLocations = Array.from(new Set(allBranches?.map(branch => branch.location) || []))

  // Combine loading and error states
  const loadingBranches = loadingAllBranches || loadingFilteredBranches || loadingBranchStats
  const error = allBranchesError || filteredBranchesError || branchStatsError

  if (error) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-lg font-semibold text-gray-900 mb-2">Error loading branches</h2>
              <p className="text-gray-600 mb-4">There was a problem loading the branch data.</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Branch Management</h1>
              <p className="text-gray-600 mt-2">Manage your company branches and their information</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Branch
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Branches</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Building2 className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{branchStats?.total || 0}</div>
                <p className="text-xs text-gray-600 mt-1">Across all locations</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Active Branches</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {branchStats?.active || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Currently operational</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Inactive Branches</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-danger">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {branchStats?.inactive || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Not operational</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Locations</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-warning">
                  <MapPin className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">
                  {branchStats?.locations || 0}
                </div>
                <p className="text-xs text-gray-600 mt-1">Different cities</p>
              </CardContent>
            </Card>
          </div>

          {/* Branches Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">Company Branches</CardTitle>
              <CardDescription className="text-gray-600">Manage and monitor all branches</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search branches by name, location, address, or email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                  <select
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Locations</option>
                    {uniqueLocations.map((location) => (
                      <option key={location} value={location}>
                        {location}
                      </option>
                    ))}
                  </select>
                  <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingBranches ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading branches...</span>
                </div>
              ) : (
                /* Branches Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Branch Name</TableHead>
                        <TableHead className="text-gray-700">Location</TableHead>
                        <TableHead className="text-gray-700">Address</TableHead>
                        <TableHead className="text-gray-700">Contact</TableHead>
                        <TableHead className="text-gray-700">Manager</TableHead>
                        <TableHead className="text-gray-700">Status</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBranches.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                            {searchTerm || statusFilter !== "all" || locationFilter !== "all"
                              ? "No branches found matching your criteria"
                              : "No branches added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredBranches.map((branch) => (
                          <TableRow key={branch.branch_id} className="border-gray-100 hover:bg-blue-50/30">
                            <TableCell className="font-medium text-gray-800">
                              <div className="flex items-center gap-2">
                                <Building2 className="h-4 w-4 text-blue-600" />
                                {branch.name}
                              </div>
                            </TableCell>
                            <TableCell className="text-gray-700">
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-orange-600" />
                                {branch.location}
                              </div>
                            </TableCell>
                            <TableCell className="text-gray-700 max-w-xs truncate" title={branch.address}>
                              {branch.address}
                            </TableCell>
                            <TableCell className="text-gray-700">
                              <div className="space-y-1">
                                <div className="flex items-center gap-2 text-sm">
                                  <Phone className="h-3 w-3 text-green-600" />
                                  {branch.phone}
                                </div>
                                <div className="flex items-center gap-2 text-sm">
                                  <Mail className="h-3 w-3 text-blue-600" />
                                  {branch.email}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-gray-700">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-purple-600" />
                                {getManagerName(branch.manager_id)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(branch.branch_status)}>{branch.branch_status}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEdit(branch)}
                                  className="hover:bg-blue-100 hover:text-blue-700"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDelete(branch)}
                                  className="hover:bg-red-100 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Add Branch Dialog */}
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Branch</DialogTitle>
              <DialogDescription>
                Enter the details for the new branch. All fields marked with * are required.
              </DialogDescription>
            </DialogHeader>
            <BranchForm
              onSuccess={handleFormSuccess}
              onCancel={() => setIsAddDialogOpen(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Branch Dialog */}
        <Dialog open={!!editingBranch} onOpenChange={handleEditDialogClose}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Branch</DialogTitle>
              <DialogDescription>
                Update the branch information. All fields marked with * are required.
              </DialogDescription>
            </DialogHeader>
            <BranchForm
              branch={editingBranch}
              onSuccess={handleFormSuccess}
              onCancel={handleEditDialogClose}
            />
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <ConfirmationDialog
          open={!!deletingBranch}
          onOpenChange={() => setDeletingBranch(null)}
          onConfirm={confirmDelete}
          title="Delete Branch"
          description={`Are you sure you want to delete "${deletingBranch?.name}"? This action cannot be undone.`}
          confirmText="Delete"
          cancelText="Cancel"
          variant="destructive"
          loading={deleteBranchMutation.isPending}
        />
      </MainLayout>
    </ProtectedRoute>
  )
}