# 🚀 دليل البدء السريع - نظام إدارة الأسطول

## 📋 نظرة عامة

هذا الدليل سيساعدك في تشغيل نظام إدارة الأسطول خلال دقائق معدودة.

## ✅ المتطلبات

- Node.js 18+ 
- npm أو yarn أو pnpm
- حساب Supabase (تم إعداده مسبقاً)

## 🔧 الإعداد السريع

### 1. تثبيت Dependencies

```bash
# فحص حالة التثبيت
node scripts/check-install.js

# تثبيت المكتبات
npm install
# أو
yarn install
# أو
pnpm install
```

### 1.1 إذا واجهت مشكلة "Cannot find module"

```bash
# الحل السريع: تطبيق البيانات بدون node_modules
node scripts/apply-seed-direct.js apply

# أو إصلاح التثبيت
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 2. إعد<PERSON> قاعدة البيانات

```bash
# تطبيق Schema و RLS policies (الطريقة الآمنة)
npm run apply-rls

# أو الإعداد السريع الكامل (يشمل بيانات تجريبية)
node scripts/quick-setup.js
```

### 2.1 إضافة بيانات تجريبية

```bash
# إضافة بيانات تجريبية للاختبار
npm run seed

# فحص البيانات الموجودة
npm run seed-check
```

### 2.2 إذا واجهت خطأ "dashboard_view is not a table"

```bash
# تشخيص المشكلة
node scripts/diagnose-issue.js

# تطبيق RLS بطريقة آمنة
node scripts/apply-rls-safe.js apply
```

### 2.3 إذا واجهت خطأ في تواريخ انتهاء الرخص

```bash
# المشكلة: تواريخ انتهاء الرخص في الماضي
# الحل: تم تحديث seed.sql بتواريخ مستقبلية (2026)
npm run seed
```

### 3. تشغيل التطبيق

```bash
npm run dev
```

### 4. فتح التطبيق

افتح المتصفح على: `http://localhost:3000`

## 🔐 حسابات تجريبية

تم إنشاء الحسابات التالية للاختبار:

### مدير عام (Super Admin)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `FleetAdmin123!`
- **الصلاحيات:** إدارة كاملة للنظام

### مدير فرع (Manager)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `FleetManager123!`
- **الصلاحيات:** إدارة فرع واحد

### موظف (Employee)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `FleetEmployee123!`
- **الصلاحيات:** عرض وإدخال بيانات محدودة

## 📊 البيانات التجريبية

### الفروع (3 فروع)
- **فرع القاهرة** - المقر الرئيسي
- **فرع الإسكندرية** - فرع الساحل الشمالي
- **فرع الجونة** - فرع البحر الأحمر

### السائقين (5 سائقين)
- **Ahmed Mohamed Ali** - رخصة صالحة حتى 2026-12-31
- **Omar Hassan Ibrahim** - رخصة صالحة حتى 2026-08-30
- **Mohamed Khaled Saeed** - رخصة صالحة حتى 2026-09-15
- **Ali Ahmed Mahmoud** - رخصة صالحة حتى 2026-11-20
- **Hassan Omar Farouk** - رخصة صالحة حتى 2026-08-10

### المركبات (6 مركبات)
- **LEVC TX** - سيارة أجرة كهربائية (CAI-001, CAI-002)
- **Toyota Hiace** - ميكروباص (ALEX-001, ALEX-002)
- **Hyundai H1** - ميكروباص (GOUNA-001, GOUNA-002)

### سجلات الصيانة والوقود
- سجلات صيانة دورية وطارئة
- سجلات تزويد بالوقود مع حساب الاستهلاك
- تكاليف وتواريخ واقعية

## 📊 معلومات Supabase

### تفاصيل المشروع
- **Project URL:** `https://vjozjofhwlpskbgbgzve.supabase.co`
- **Project ID:** `vjozjofhwlpskbgbgzve`
- **Region:** US East (Virginia)

### المفاتيح
- **Anon Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (في .env.local)
- **Service Role Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (في .env.local)

## 🗄️ هيكل قاعدة البيانات

### الجداول الرئيسية
- `profiles` - ملفات المستخدمين
- `branches` - الفروع
- `vehicles` - المركبات
- `drivers` - السائقين
- `maintenance_records` - سجلات الصيانة
- `fuel_records` - سجلات الوقود

### الحماية
- **Row Level Security (RLS)** مفعل على جميع الجداول
- **Role-based access control** حسب نوع المستخدم
- **Branch-level isolation** للبيانات

## 🎯 الميزات الرئيسية

### ✅ ما يعمل الآن
- 🔐 **نظام مصادقة** كامل مع Supabase Auth
- 👥 **إدارة المستخدمين** مع أدوار مختلفة
- 🚗 **إدارة المركبات** مع تتبع الحالة
- 👨‍💼 **إدارة السائقين** مع تتبع الرخص
- 🔧 **إدارة الصيانة** مع جدولة
- ⛽ **إدارة الوقود** مع حساب الاستهلاك
- 📊 **لوحة تحكم** مع إحصائيات فورية
- 🔄 **تحديثات فورية** مع Real-time
- 📱 **واجهة متجاوبة** مع دعم RTL

### 🚧 قيد التطوير
- 📈 **تقارير متقدمة** مع تصدير
- 📱 **تطبيق موبايل** 
- 🤖 **ذكاء اصطناعي** للتنبؤات
- 🔗 **تكامل APIs** خارجية

## 📚 الوثائق

### الأدلة المتوفرة
1. **[README.md](README.md)** - نظرة عامة شاملة
2. **[DATABASE_SCHEMA.md](docs/DATABASE_SCHEMA.md)** - مخطط قاعدة البيانات
3. **[AUTHENTICATION_IMPLEMENTATION.md](docs/AUTHENTICATION_IMPLEMENTATION.md)** - نظام المصادقة
4. **[DEVELOPER_GUIDE.md](docs/DEVELOPER_GUIDE.md)** - دليل المطور
5. **[DEPLOYMENT_GUIDE.md](docs/DEPLOYMENT_GUIDE.md)** - دليل النشر

### Scripts المفيدة
```bash
# تطبيق RLS policies
node scripts/apply-rls-policies.js apply

# تطبيق البيانات التجريبية
npm run seed

# إنشاء مستخدمين تجريبيين
node scripts/quick-setup.js
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ "dashboard_view is not a table"
```bash
# تشخيص المشكلة
node scripts/diagnose-issue.js

# الحل: استخدام RLS الآمن
npm run apply-rls
# أو
node scripts/apply-rls-safe.js apply
```

#### 2. خطأ في الاتصال بـ Supabase
```bash
# تحقق من متغيرات البيئة
cat .env.local

# تحقق من الاتصال
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"

# فحص شامل للنظام
npm run check
```

#### 3. مشاكل RLS
```bash
# إعادة تطبيق RLS policies بطريقة آمنة
npm run apply-rls

# أو الطريقة القديمة
node scripts/apply-rls-policies.js apply

# التحقق من حالة RLS
node scripts/apply-rls-safe.js verify
```

#### 4. مشاكل المصادقة
```bash
# إنشاء مستخدمين جدد
node scripts/quick-setup.js

# أو إنشاء مستخدمين فقط
node scripts/apply-rls-policies.js create-users
```

#### 5. مشاكل البناء
```bash
# مسح cache وإعادة البناء
rm -rf .next
rm -rf node_modules
npm install
npm run build
```

#### 6. الجداول غير موجودة
```bash
# تطبيق schema قاعدة البيانات
node scripts/quick-setup.js

# أو تطبيق schema يدوياً من Supabase Dashboard
```

#### 7. خطأ في تواريخ انتهاء الرخص
```bash
# خطأ: "violates check constraint chk_license_expiry_future"
# السبب: تواريخ انتهاء الرخص في الماضي

# الحل: تم تحديث البيانات التجريبية
npm run seed

# أو تطبيق البيانات يدوياً
node scripts/apply-seed-data.js apply
```

#### 8. مشاكل البيانات التجريبية
```bash
# فحص البيانات الموجودة
npm run seed-check

# مسح البيانات وإعادة التطبيق
node scripts/apply-seed-data.js clear
node scripts/apply-seed-data.js apply

# التحقق من البيانات المطبقة
node scripts/apply-seed-data.js verify
```

## 🎮 تجربة النظام

### 1. تسجيل الدخول
- اذهب إلى `http://localhost:3000`
- استخدم أحد الحسابات التجريبية
- ستتم إعادة توجيهك للوحة التحكم

### 2. استكشاف الميزات
- **لوحة التحكم:** إحصائيات شاملة
- **المركبات:** إضافة وإدارة المركبات
- **السائقين:** إدارة ملفات السائقين
- **الصيانة:** جدولة وتتبع الصيانة
- **الوقود:** تسجيل عمليات التزويد

### 3. اختبار الصلاحيات
- جرب تسجيل الدخول بأدوار مختلفة
- لاحظ الاختلاف في الصلاحيات
- اختبر إضافة وتعديل البيانات

## 📞 الدعم

### إذا واجهت مشاكل:

1. **تحقق من الوثائق** في مجلد `docs/`
2. **راجع ملفات البيئة** (.env.local)
3. **تحقق من console** في المتصفح
4. **راجع logs** في terminal

### معلومات مفيدة:
- **Supabase Dashboard:** [https://supabase.com/dashboard](https://supabase.com/dashboard)
- **Project URL:** https://vjozjofhwlpskbgbgzve.supabase.co
- **Local URL:** http://localhost:3000

## 🎉 مبروك!

إذا وصلت إلى هنا، فقد تم إعداد النظام بنجاح! 

النظام جاهز الآن للاستخدام والتطوير. يمكنك البدء في:
- إضافة بيانات حقيقية
- تخصيص الواجهة
- إضافة ميزات جديدة
- نشر النظام في الإنتاج

---

**نظام إدارة الأسطول** - حل متكامل وحديث لإدارة الأساطيل بكفاءة عالية! 🚀
