// @ts-nocheck
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type Vehicle } from '@/lib/supabase-api-client'
import { queryKeys, optimisticUpdates } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useVehicles() {
  return useQuery({
    queryKey: queryKeys.vehicles(),
    queryFn: () => supabaseApiClient.getVehicles(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useVehicle(id: string) {
  const { data: vehicles } = useVehicles() // Depend on useVehicles
  return useQuery({
    queryKey: queryKeys.vehicle(id),
    queryFn: () => vehicles?.find(v => v.id === id) || null,
    enabled: !!id && !!vehicles, // Only enable if vehicles data is available
  })
}

// Mutation hooks
export function useAddVehicle() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (vehicleData: Partial<Vehicle>) => supabaseApiClient.addVehicle(vehicleData),
    onMutate: async (newVehicle) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.vehicles() })
      
      // Snapshot the previous value
      const previousVehicles = queryClient.getQueryData(queryKeys.vehicles())
      
      // Optimistically update to the new value
      optimisticUpdates.addVehicle({
        ...newVehicle,
        vehicle_id: `temp_${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      
      // Return a context object with the snapshotted value
      return { previousVehicles }
    },
    onError: (error, newVehicle, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousVehicles) {
        queryClient.setQueryData(queryKeys.vehicles(), context.previousVehicles)
      }
      toast.error('Failed to add vehicle. Please try again.')
    },
    onSuccess: (data) => {
      // Update the cache with the actual vehicle data returned from the server
      queryClient.setQueryData(queryKeys.vehicles(), (oldData: any) => {
        if (!oldData) return [data]
        // Replace the optimistic entry (temp_ID) with the actual data from the server
        return oldData.map((vehicle: any) =>
          vehicle.vehicle_id.startsWith('temp_') ? data : vehicle
        )
      })
      toast.success('Vehicle added successfully!')
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

export function useUpdateVehicle() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Vehicle> }) => 
      supabaseApiClient.updateVehicle(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.vehicles() })
      
      const previousVehicles = queryClient.getQueryData(queryKeys.vehicles())
      
      optimisticUpdates.updateVehicle({
        vehicle_id: id,
        ...data,
        updated_at: new Date().toISOString(),
      })
      
      return { previousVehicles }
    },
    onError: (error, variables, context) => {
      if (context?.previousVehicles) {
        queryClient.setQueryData(queryKeys.vehicles(), context.previousVehicles)
      }
      toast.error('Failed to update vehicle. Please try again.')
    },
    onSuccess: () => {
      toast.success('Vehicle updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

export function useDeleteVehicle() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteVehicle(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.vehicles() })
      
      const previousVehicles = queryClient.getQueryData(queryKeys.vehicles())
      
      optimisticUpdates.deleteVehicle(id)
      
      return { previousVehicles }
    },
    onError: (error, id, context) => {
      if (context?.previousVehicles) {
        queryClient.setQueryData(queryKeys.vehicles(), context.previousVehicles)
      }
      toast.error('Failed to delete vehicle. Please try again.')
    },
    onSuccess: () => {
      toast.success('Vehicle deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

// Search hook
export function useSearchVehicles(vehicles: Vehicle[] | undefined, searchTerm: string, field?: string) {
  return useQuery({
    queryKey: queryKeys.search('vehicles', searchTerm),
    queryFn: () => {
      if (!searchTerm || !vehicles) {
        return vehicles || [] // Return all vehicles if no search term, or empty array if no data
      }
      
      const searchTermLower = searchTerm.toLowerCase()
      
      return vehicles.filter(vehicle => {
        if (field) {
          return vehicle[field as keyof Vehicle]?.toString().toLowerCase().includes(searchTermLower)
        }
        
        return Object.values(vehicle).some(value =>
          value?.toString().toLowerCase().includes(searchTermLower)
        )
      })
    },
    enabled: !!vehicles, // Only enable if vehicles data is available
    staleTime: 2 * 60 * 1000, // 2 minutes for search results
  })
}

// Filtered vehicles hook
export function useFilteredVehicles(vehicles: Vehicle[] | undefined, filters: {
  vehicle_status?: string
  type?: string
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.vehicles(), 'filtered', filters],
    queryFn: () => {
      if (!vehicles) {
        return []
      }

      let filtered = vehicles
      
      if (filters.vehicle_status && filters.vehicle_status !== 'all') {
        filtered = filtered.filter(v => v.vehicle_status === filters.vehicle_status)
      }
      
      if (filters.type && filters.type !== 'all') {
        filtered = filtered.filter(v => v.vehicle_type === filters.type)
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(v =>
          v.plate_number.toLowerCase().includes(searchTermLower) ||
          v.vehicle_type.toLowerCase().includes(searchTermLower) ||
          v.vehicle_status.toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered
    },
    enabled: !!vehicles, // Only enable if vehicles data is available
    staleTime: 3 * 60 * 1000, // 3 minutes
  })
}

// Vehicle statistics hook
export function useVehicleStats(vehicles: Vehicle[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.vehicles(), 'stats'],
    queryFn: () => {
      if (!vehicles) {
        return {
          total: 0,
          active: 0,
          maintenance: 0,
          inactive: 0,
          overdueMaintenance: 0,
          dueSoon: 0,
        }
      }

      return {
        total: vehicles.length,
        active: vehicles.filter(v => v.vehicle_status === 'Active').length,
        maintenance: vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
        inactive: vehicles.filter(v => v.vehicle_status === 'Inactive').length,
        overdueMaintenance: vehicles.filter(v => {
          const currentKM = v.current_km || 0
          const nextMaintenanceKM = v.current_km + 5000 // Assuming next maintenance is 5000km from current
          return nextMaintenanceKM - currentKM <= 0
        }).length,
        dueSoon: vehicles.filter(v => {
          const currentKM = v.current_km || 0
          const nextMaintenanceKM = v.current_km + 5000 // Assuming next maintenance is 5000km from current
          const kmUntilMaintenance = nextMaintenanceKM - currentKM
          return kmUntilMaintenance > 0 && kmUntilMaintenance <= 1000
        }).length,
      }
    },
    enabled: !!vehicles,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}