// @ts-nocheck
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type User } from "@/lib/supabase-api-client"
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useUsers() {
  return useQuery({
    queryKey: queryKeys.users(),
    queryFn: async () => {
      try {
        // Try the normal API first
        return await supabaseApiClient.getUsers()
      } catch (error) {
        console.warn('Normal API failed, trying no-RLS API:', error)
        // Fallback to no-RLS API
        const response = await fetch('/api/users-no-rls')
        const result = await response.json()
        if (result.success) {
          return result.data
        }
        throw new Error(result.error || 'Failed to fetch users')
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUser(id: string) {
  const { data: users } = useUsers() // Depend on useUsers
  return useQuery({
    queryKey: queryKeys.user(id),
    queryFn: () => users?.find(u => u.id === id) || null,
    enabled: !!id && !!users, // Only enable if users data is available
  })
}

// Mutation hooks
export function useAddUser() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (userData: Partial<User>) => supabaseApiClient.addUser(userData),
    onMutate: async (newUser) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.users() })
      
      const previousUsers = queryClient.getQueryData(queryKeys.users())
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.users(), (old: any) => {
        if (!old) return [{ ...newUser, user_id: `temp_${Date.now()}` }]
        return [...old, { ...newUser, user_id: `temp_${Date.now()}` }]
      })
      
      return { previousUsers }
    },
    onError: (error, newUser, context) => {
      if (context?.previousUsers) {
        queryClient.setQueryData(queryKeys.users(), context.previousUsers)
      }
      toast.error('Failed to add user. Please try again.')
    },
    onSuccess: () => {
      toast.success('User added successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users() })
    },
  })
}

export function useUpdateUser() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<User> }) => 
      supabaseApiClient.updateUser(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.users() })
      
      const previousUsers = queryClient.getQueryData(queryKeys.users())
      
      queryClient.setQueryData(queryKeys.users(), (old: any) => {
        if (!old) return []
        return old.map((user: any) => 
          user.user_id === id ? { ...user, ...data } : user
        )
      })
      
      return { previousUsers }
    },
    onError: (error, variables, context) => {
      if (context?.previousUsers) {
        queryClient.setQueryData(queryKeys.users(), context.previousUsers)
      }
      toast.error('Failed to update user. Please try again.')
    },
    onSuccess: () => {
      toast.success('User updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users() })
    },
  })
}

export function useDeleteUser() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteUser(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.users() })
      
      const previousUsers = queryClient.getQueryData(queryKeys.users())
      
      queryClient.setQueryData(queryKeys.users(), (old: any) => {
        if (!old) return []
        return old.filter((user: any) => user.user_id !== id)
      })
      
      return { previousUsers }
    },
    onError: (error, id, context) => {
      if (context?.previousUsers) {
        queryClient.setQueryData(queryKeys.users(), context.previousUsers)
      }
      toast.error('Failed to delete user. Please try again.')
    },
    onSuccess: () => {
      toast.success('User deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users() })
    },
  })
}

// Search and filter hooks
export function useFilteredUsers(users: User[] | undefined, filters: {
  role?: string
  user_status?: string
  branch_id?: string
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.users(), 'filtered', filters],
    queryFn: () => {
      if (!users) {
        return []
      }

      let filtered = users
      
      if (filters.role && filters.role !== 'all') {
        filtered = filtered.filter(u => u.role === filters.role)
      }
      
      if (filters.user_status && filters.user_status !== 'all') {
        filtered = filtered.filter(u => u.user_status === filters.user_status)
      }
      
      if (filters.branch_id && filters.branch_id !== 'all') {
        filtered = filtered.filter(u => u.branch_id === filters.branch_id)
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(u =>
          u.full_name.toLowerCase().includes(searchTermLower) ||
          u.email.toLowerCase().includes(searchTermLower) ||
          u.role.toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
    },
    enabled: !!users,
    staleTime: 3 * 60 * 1000,
  })
}

// User statistics hook
export function useUserStats(users: User[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.users(), 'stats'],
    queryFn: () => {
      if (!users) {
        return {
          total: 0,
          active: 0,
          inactive: 0,
          superAdmins: 0,
          managers: 0,
          employees: 0,
          newThisMonth: 0,
          newLastMonth: 0,
          recentlyLoggedIn: 0,
          branches: [],
        }
      }

      const today = new Date()
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      
      return {
        total: users.length,
        active: users.filter(u => u.status === 'Active').length,
        inactive: users.filter(u => u.status === 'Inactive').length,
        superAdmins: users.filter(u => u.role === 'Super Admin').length,
        managers: users.filter(u => u.role === 'Manager').length,
        employees: users.filter(u => u.role === 'Employee').length,
        newThisMonth: users.filter(u => {
          const createdDate = new Date(u.createdDate)
          return createdDate >= thisMonth
        }).length,
        newLastMonth: users.filter(u => {
          const createdDate = new Date(u.createdDate)
          return createdDate >= lastMonth && createdDate < thisMonth
        }).length,
        recentlyLoggedIn: users.filter(u => {
          if (!u.lastLogin) return false
          const lastLoginDate = new Date(u.lastLogin)
          const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000)
          return lastLoginDate >= threeDaysAgo
        }).length,
        branches: (() => {
          const branchCounts = users.reduce((acc, user) => {
            if (user.branch_id) {
              acc[user.branch_id] = (acc[user.branch_id] || 0) + 1
            }
            return acc
          }, {} as Record<string, number>)
          return Object.entries(branchCounts).map(([branch_id, count]) => ({
            branch_id,
            count,
          }))
        })(),
      }
    },
    enabled: !!users,
    staleTime: 2 * 60 * 1000,
  })
}

// User roles hook
export function useUserRoles() {
  return useQuery({
    queryKey: [...queryKeys.users(), 'roles'],
    queryFn: async () => {
      // This would typically come from a configuration or API
      // For now, return the predefined roles
      return [
        {
          value: 'Super Admin',
          label: 'Super Admin',
          description: 'Full system access and user management',
          permissions: ['manage_all_data', 'manage_users', 'assign_vehicles', 'access_all_reports', 'view_all_data'],
        },
        {
          value: 'Manager',
          label: 'Manager',
          description: 'Manage vehicles and employees within branch',
          permissions: ['manage_own_vehicles', 'view_own_data', 'assign_employees', 'access_reports'],
        },
        {
          value: 'Employee',
          label: 'Employee',
          description: 'Input data and view assigned information',
          permissions: ['input_data', 'view_assigned_data'],
        },
      ]
    },
    staleTime: 60 * 60 * 1000, // 1 hour - roles don't change often
  })
}

// Active users hook (for assignments, etc.)
export function useActiveUsers(users: User[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.users(), 'active'],
    queryFn: () => {
      if (!users) {
        return []
      }
      return users.filter(u => u.status === 'Active')
    },
    enabled: !!users,
    staleTime: 5 * 60 * 1000,
  })
}

// User permissions hook
export function useUserPermissions(users: User[] | undefined, userId: string) {
  return useQuery({
    queryKey: [...queryKeys.user(userId), 'permissions'],
    queryFn: () => {
      if (!users) {
        return []
      }
      const user = users.find(u => u.id === userId)
      
      if (!user) return []
      
      const permissions = {
        'Super Admin': ['manage_all_data', 'manage_users', 'assign_vehicles', 'access_all_reports', 'view_all_data'],
        'Manager': ['manage_own_vehicles', 'view_own_data', 'assign_employees', 'access_reports'],
        'Employee': ['input_data', 'view_assigned_data'],
      }
      
      return permissions[user.role as keyof typeof permissions] || []
    },
    enabled: !!userId && !!users,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// User activity hook
export function useUserActivity(users: User[] | undefined, userId: string) {
  return useQuery({
    queryKey: [...queryKeys.user(userId), 'activity'],
    queryFn: () => {
      if (!users) {
        return null
      }
      const user = users.find(u => u.id === userId)
      
      if (!user) return null
      
      return {
        lastLogin: user.lastLogin,
        createdDate: user.createdDate,
        updatedDate: user.updatedDate,
        // Add more activity data as needed
      }
    },
    enabled: !!userId && !!users,
    staleTime: 5 * 60 * 1000,
  })
}