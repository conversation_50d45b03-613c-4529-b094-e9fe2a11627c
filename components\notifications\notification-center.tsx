"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON>, <PERSON><PERSON>he<PERSON>, X, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { notificationService, type Notification } from "@/lib/notifications"
import { cn } from "@/lib/utils"

const NotificationIcon = ({ type }: { type: Notification['type'] }) => {
  const iconClasses = "h-2 w-2 rounded-full"
  
  switch (type) {
    case 'success':
      return <div className={cn(iconClasses, "bg-green-500")} />
    case 'error':
      return <div className={cn(iconClasses, "bg-red-500")} />
    case 'warning':
      return <div className={cn(iconClasses, "bg-yellow-500")} />
    case 'info':
    default:
      return <div className={cn(iconClasses, "bg-blue-500")} />
  }
}

const NotificationItem = ({ 
  notification, 
  onMarkAsRead, 
  onDelete,
  compact = false 
}: { 
  notification: Notification
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
  compact?: boolean
}) => {
  const handleAction = () => {
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl
      onMarkAsRead(notification.id)
    }
  }

  return (
    <div className={cn(
      "flex items-start space-x-3 p-3 border-l-4 hover:bg-gray-50 transition-colors",
      notification.read ? "bg-white border-gray-200" : "bg-blue-50 border-blue-500",
      notification.priority === 'high' ? "border-red-500" : ""
    )}>
      <div className="flex-shrink-0 mt-1">
        <NotificationIcon type={notification.type} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <p className={cn(
            "text-sm font-medium",
            notification.read ? "text-gray-600" : "text-gray-900"
          )}>
            {notification.title}
          </p>
          <div className="flex items-center space-x-1">
            {notification.priority === 'high' && (
              <Badge variant="destructive" className="text-xs">
                High Priority
              </Badge>
            )}
            <span className="text-xs text-gray-500">
              {notification.timestamp.toLocaleTimeString()}
            </span>
          </div>
        </div>
        
        <p className={cn(
          "text-sm mt-1",
          notification.read ? "text-gray-500" : "text-gray-700"
        )}>
          {notification.message}
        </p>
        
        {!compact && (
          <div className="flex items-center space-x-2 mt-2">
            {notification.actionUrl && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleAction}
                className="text-xs"
              >
                {notification.actionLabel || "View"}
                <ExternalLink className="ml-1 h-3 w-3" />
              </Button>
            )}
            
            {!notification.read && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onMarkAsRead(notification.id)}
                className="text-xs"
              >
                <Check className="h-3 w-3" />
                Mark as read
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete(notification.id)}
              className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-3 w-3" />
              Delete
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

export function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [activeTab, setActiveTab] = useState('all')

  useEffect(() => {
    // Load initial notifications
    setNotifications(notificationService.getAll())
    
    // Subscribe to updates
    const unsubscribe = notificationService.subscribe(setNotifications)
    
    return unsubscribe
  }, [])

  const unreadCount = notifications.filter(n => !n.read).length
  
  const filteredNotifications = activeTab === 'all' 
    ? notifications 
    : notifications.filter(n => n.category === activeTab)

  const handleMarkAsRead = (id: string) => {
    notificationService.markAsRead(id)
  }

  const handleDelete = (id: string) => {
    notificationService.delete(id)
  }

  const handleMarkAllAsRead = () => {
    notificationService.markAllAsRead()
  }

  const handleClearAll = () => {
    notificationService.clear()
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-96">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleMarkAllAsRead}
                className="text-xs"
              >
                <CheckCheck className="h-3 w-3 mr-1" />
                Mark all read
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="text-xs text-red-600 hover:text-red-700"
            >
              <X className="h-3 w-3" />
              Clear all
            </Button>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="text-xs">
              All
              {notifications.length > 0 && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {notifications.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="maintenance" className="text-xs">Maintenance</TabsTrigger>
            <TabsTrigger value="driver" className="text-xs">Drivers</TabsTrigger>
            <TabsTrigger value="system" className="text-xs">System</TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="mt-2">
            <ScrollArea className="h-96">
              {filteredNotifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No notifications</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={handleMarkAsRead}
                      onDelete={handleDelete}
                      compact
                    />
                  ))}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Full-page notification center
export function NotificationPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [activeTab, setActiveTab] = useState('all')

  useEffect(() => {
    setNotifications(notificationService.getAll())
    const unsubscribe = notificationService.subscribe(setNotifications)
    return unsubscribe
  }, [])

  const filteredNotifications = activeTab === 'all' 
    ? notifications 
    : notifications.filter(n => n.category === activeTab)

  const unreadCount = notifications.filter(n => !n.read).length
  const categoryStats = {
    maintenance: notifications.filter(n => n.category === 'maintenance').length,
    driver: notifications.filter(n => n.category === 'driver').length,
    vehicle: notifications.filter(n => n.category === 'vehicle').length,
    fuel: notifications.filter(n => n.category === 'fuel').length,
    system: notifications.filter(n => n.category === 'system').length,
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Notifications</h1>
          <p className="text-gray-600">
            {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All notifications read'}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => notificationService.markAllAsRead()}
            disabled={unreadCount === 0}
          >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark all as read
          </Button>
          <Button
            variant="outline"
            onClick={() => notificationService.clear()}
            disabled={notifications.length === 0}
            className="text-red-600 hover:text-red-700"
          >
            <X className="h-4 w-4 mr-2" />
            Clear all
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">All</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{notifications.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Maintenance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categoryStats.maintenance}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Drivers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categoryStats.driver}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Vehicles</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categoryStats.vehicle}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">System</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categoryStats.system}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              <TabsTrigger value="driver">Drivers</TabsTrigger>
              <TabsTrigger value="vehicle">Vehicles</TabsTrigger>
              <TabsTrigger value="fuel">Fuel</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
            </TabsList>
          </Tabs>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-2">
            {filteredNotifications.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No notifications in this category</p>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={(id) => notificationService.markAsRead(id)}
                  onDelete={(id) => notificationService.delete(id)}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}