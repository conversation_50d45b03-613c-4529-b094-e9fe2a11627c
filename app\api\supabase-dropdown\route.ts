import { NextRequest, NextResponse } from 'next/server'
import { supabaseApiClient } from '@/lib/supabase-api-client'

export async function POST(request: NextRequest) {
  try {
    const { action, ...data } = await request.json()

    switch (action) {
      case 'getCategoryValues':
        return await getCategoryValues(data)
      
      case 'getUniqueValues':
        return await getUniqueValues(data)
      
      case 'searchValues':
        return await searchValues(data)
      
      default:
        return NextResponse.json(
          { success: false, error: 'Unknown action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Supabase dropdown API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getCategoryValues(data: any) {
  try {
    const { category, filters = {} } = data
    
    let values: any[] = []
    
    switch (category) {
      case 'vehicles':
        const vehicles = await supabaseApiClient.getVehicles()
        values = vehicles.map(v => ({
          value: v.id,
          label: `${v.make} ${v.model} (${v.vin_number})`,
          metadata: {
            make: v.make,
            model: v.model,
            year: v.year,
            status: v.vehicle_status
          }
        }))
        break
        
      case 'drivers':
        const drivers = await supabaseApiClient.getDrivers()
        values = drivers.map(d => ({
          value: d.id,
          label: d.full_name,
          metadata: {
            license_number: d.license_number,
            status: d.status,
            phone: d.phone
          }
        }))
        break
        
      case 'branches':
        const branches = await supabaseApiClient.getBranches()
        values = branches.map(b => ({
          value: b.id,
          label: b.name,
          metadata: {
            location: b.location,
            manager_id: b.manager_id
          }
        }))
        break
        
      case 'vehicle_makes':
        const vehiclesForMakes = await supabaseApiClient.getVehicles()
        const uniqueMakes = [...new Set(vehiclesForMakes.map(v => v.make))]
        values = uniqueMakes.map(make => ({
          value: make,
          label: make
        }))
        break
        
      case 'vehicle_models':
        const vehiclesForModels = await supabaseApiClient.getVehicles()
        let filteredVehicles = vehiclesForModels
        
        if (filters.make) {
          filteredVehicles = vehiclesForModels.filter(v => v.make === filters.make)
        }
        
        const uniqueModels = [...new Set(filteredVehicles.map(v => v.model))]
        values = uniqueModels.map(model => ({
          value: model,
          label: model
        }))
        break
        
      case 'fuel_stations':
        const fuelRecords = await supabaseApiClient.getFuelRecords()
        const uniqueStations = [...new Set(fuelRecords.map(f => f.station).filter(Boolean))]
        values = uniqueStations.map(station => ({
          value: station,
          label: station
        }))
        break
        
      default:
        values = []
    }
    
    return NextResponse.json({
      success: true,
      data: values,
      count: values.length
    })
    
  } catch (error) {
    console.error('Error getting category values:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get category values' },
      { status: 500 }
    )
  }
}

async function getUniqueValues(data: any) {
  try {
    const { table, column, filters = {} } = data
    
    // This would need to be implemented based on specific requirements
    // For now, return empty array
    return NextResponse.json({
      success: true,
      data: [],
      count: 0
    })
    
  } catch (error) {
    console.error('Error getting unique values:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get unique values' },
      { status: 500 }
    )
  }
}

async function searchValues(data: any) {
  try {
    const { category, query, limit = 50 } = data
    
    // This would need to be implemented based on specific requirements
    // For now, return empty array
    return NextResponse.json({
      success: true,
      data: [],
      count: 0
    })
    
  } catch (error) {
    console.error('Error searching values:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to search values' },
      { status: 500 }
    )
  }
}
