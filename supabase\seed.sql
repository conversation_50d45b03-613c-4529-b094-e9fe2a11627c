-- Fleet Management System - Seed Data
-- This file contains initial data for testing and development

-- Insert sample branches
INSERT INTO branches (id, name, location, address, phone, email, branch_status) VALUES
('550e8400-e29b-41d4-a716-************', 'Cairo Branch', 'Cairo', '123 Tahrir Square, Cairo, Egypt', '+20-2-1234567', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-************', 'Alexandria Branch', 'Alexandria', '456 Corniche Road, Alexandria, Egypt', '+20-3-7654321', '<EMAIL>', 'Active'),
('550e8400-e29b-41d4-a716-************', 'Gouna Branch', 'El Gouna', '789 Marina Boulevard, El Gouna, Egypt', '+20-65-3456789', '<EMAIL>', 'Active');

-- Note: Users will be created through Supabase Auth, but we can prepare some sample profiles
-- These will be linked when users actually sign up

-- Insert sample drivers
INSERT INTO drivers (id, full_name, license_number, license_expiry, phone, email, status, hire_date, branch_id) VALUES
('660e8400-e29b-41d4-a716-************', '<PERSON> <PERSON> Ali', 'DL123456789', '2026-12-31', '+20-10-1234567', '<EMAIL>', 'Available', '2023-01-15', '550e8400-e29b-41d4-a716-************'),
('660e8400-e29b-41d4-a716-************', 'Omar Hassan Ibrahim', 'DL987654321', '2026-08-30', '+20-11-2345678', '<EMAIL>', 'Available', '2023-02-20', '550e8400-e29b-41d4-a716-************'),
('660e8400-e29b-41d4-a716-************', 'Mohamed Khaled Saeed', 'DL456789123', '2026-09-15', '+20-12-3456789', '<EMAIL>', 'Available', '2023-03-10', '550e8400-e29b-41d4-a716-************'),
('660e8400-e29b-41d4-a716-************', 'Ali Ahmed Mahmoud', 'DL789123456', '2026-11-20', '+20-10-4567890', '<EMAIL>', 'Available', '2023-04-05', '550e8400-e29b-41d4-a716-************'),
('660e8400-e29b-41d4-a716-************', 'Hassan Omar Farouk', 'DL321654987', '2026-08-10', '+20-11-5678901', '<EMAIL>', 'Available', '2023-05-12', '550e8400-e29b-41d4-a716-************');

-- Insert sample vehicles
INSERT INTO vehicles (id, plate_number, vin_number, model, vehicle_type, service_type, department, fuel_type, tank_capacity_liters, engine_cc, color, current_km, branch_id, assigned_driver_id, vehicle_status) VALUES
('770e8400-e29b-41d4-a716-************', 'ABC-123', 'VIN123456789ABC123', 2023, 'LEVC', 'E Cab', 'London Cab', 'Gasoline 95', 45.00, 1500, 'Black', 15000, '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Active'),
('770e8400-e29b-41d4-a716-************', 'DEF-456', 'VIN456789123DEF456', 2022, 'Toyota', 'Taxi', 'Transport', 'Gasoline 91', 50.00, 1600, 'White', 25000, '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Active'),
('770e8400-e29b-41d4-a716-************', 'GHI-789', 'VIN789123456GHI789', 2023, 'Hyundai', 'Private', 'Executive', 'Diesel', 55.00, 2000, 'Blue', 8000, '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Active'),
('770e8400-e29b-41d4-a716-************', 'JKL-012', 'VIN012345678JKL012', 2022, 'Toyota', 'Taxi', 'Transport', 'Gasoline 95', 48.00, 1800, 'Red', 32000, '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Active'),
('770e8400-e29b-41d4-a716-************', 'MNO-345', 'VIN345678901MNO345', 2023, 'LEVC', 'E Cab', 'London Cab', 'Gasoline 95', 45.00, 1500, 'Green', 5000, '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'Active'),
('770e8400-e29b-41d4-a716-************', 'PQR-678', 'VIN678901234PQR678', 2022, 'Hyundai', 'Private', 'Executive', 'Diesel', 60.00, 2200, 'Silver', 18000, '550e8400-e29b-41d4-a716-************', NULL, 'Available');

-- Insert sample maintenance records
INSERT INTO maintenance_records (id, vehicle_id, maintenance_type, maintenance_status, scheduled_date, completed_date, cost, description, technician, next_maintenance_km) VALUES
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Oil Change', 'Completed', '2024-01-15', '2024-01-15', 150.00, 'Regular oil change and filter replacement', 'Ahmed Mechanic', 20000),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Tire Rotation', 'Completed', '2024-02-20', '2024-02-20', 80.00, 'Tire rotation and pressure check', 'Mohamed Technician', 25000),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Brake Service', 'Completed', '2024-01-10', '2024-01-12', 300.00, 'Brake pad replacement and fluid change', 'Ali Specialist', 30000),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Oil Change', 'Scheduled', '2024-12-30', NULL, 0.00, 'Scheduled oil change', 'Hassan Mechanic', 15000),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'General Inspection', 'In Progress', '2024-12-25', NULL, 0.00, 'Annual vehicle inspection', 'Omar Inspector', 35000),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Oil Change', 'Scheduled', '2025-01-15', NULL, 0.00, 'First oil change for new vehicle', 'Ahmed Mechanic', 10000);

-- Insert sample fuel records
INSERT INTO fuel_records (id, vehicle_id, date, quantity_liters, distance_km, cost, station) VALUES
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-01', 35.50, 450.00, 850.00, 'Shell Station - Tahrir'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-10', 40.00, 520.00, 960.00, 'Mobil Station - Zamalek'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-05', 45.00, 600.00, 1080.00, 'Total Station - Maadi'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-15', 38.75, 485.00, 930.00, 'ADNOC Station - Heliopolis'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-08', 42.00, 580.00, 1260.00, 'Shell Station - Alexandria'),
('990e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '2024-12-18', 39.25, 510.00, 1177.50, 'Mobil Station - Alexandria'),
('990e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-************', '2024-12-12', 44.50, 620.00, 1067.00, 'Total Station - Alexandria'),
('990e8400-e29b-41d4-a716-446655440008', '770e8400-e29b-41d4-a716-************', '2024-12-20', 36.00, 480.00, 864.00, 'Shell Station - Gouna'),
('990e8400-e29b-41d4-a716-446655440009', '770e8400-e29b-41d4-a716-************', '2024-12-22', 48.00, 650.00, 1440.00, 'ADNOC Station - Gouna');

-- Create some dropdown admin data for compatibility
CREATE TABLE IF NOT EXISTS dropdown_admin (
    id SERIAL PRIMARY KEY,
    vehicle_type TEXT,
    service_type TEXT,
    fuel_type TEXT,
    color TEXT,
    department TEXT,
    vehicle_status TEXT,
    branches TEXT,
    branch_status TEXT,
    driver_status TEXT,
    maintenance_status TEXT,
    user_status TEXT,
    manager TEXT,
    drivers TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert dropdown values
INSERT INTO dropdown_admin (vehicle_type, service_type, fuel_type, color, department, vehicle_status, branches, branch_status, driver_status, maintenance_status, user_status) VALUES
('LEVC', 'E Cab', 'Gasoline 95', 'Black', 'London Cab', 'Active', 'Cairo Branch', 'Active', 'Available', 'Scheduled', 'Active'),
('Toyota', 'Taxi', 'Gasoline 91', 'White', 'Transport', 'Inactive', 'Alexandria Branch', 'Inactive', 'Active', 'In Progress', 'Inactive'),
('Hyundai', 'Private', 'Diesel', 'Blue', 'Executive', 'Maintenance', 'Gouna Branch', NULL, 'Inactive', 'Completed', 'Suspended'),
('Ford', 'Commercial', 'Gasoline 95', 'Red', 'Delivery', 'Out of Service', NULL, NULL, 'On Leave', 'Cancelled', NULL),
('Nissan', 'Rental', 'Gasoline 91', 'Green', 'Tourism', NULL, NULL, NULL, NULL, NULL, NULL),
('Mercedes', 'VIP', 'Diesel', 'Silver', 'Executive', NULL, NULL, NULL, NULL, NULL, NULL),
('BMW', 'Executive', 'Gasoline 95', 'Gold', 'Management', NULL, NULL, NULL, NULL, NULL, NULL),
('Volkswagen', 'Standard', 'Diesel', 'Gray', 'General', NULL, NULL, NULL, NULL, NULL, NULL);

-- Create a function to get dropdown values (for compatibility with existing frontend)
CREATE OR REPLACE FUNCTION get_dropdown_values(column_name TEXT)
RETURNS TABLE(value TEXT) AS $$
BEGIN
    CASE column_name
        WHEN 'Vehicle_Type' THEN
            RETURN QUERY SELECT DISTINCT vehicle_type FROM dropdown_admin WHERE vehicle_type IS NOT NULL;
        WHEN 'Service_Type' THEN
            RETURN QUERY SELECT DISTINCT service_type FROM dropdown_admin WHERE service_type IS NOT NULL;
        WHEN 'Fuel_Type' THEN
            RETURN QUERY SELECT DISTINCT fuel_type FROM dropdown_admin WHERE fuel_type IS NOT NULL;
        WHEN 'Color' THEN
            RETURN QUERY SELECT DISTINCT color FROM dropdown_admin WHERE color IS NOT NULL;
        WHEN 'Department' THEN
            RETURN QUERY SELECT DISTINCT department FROM dropdown_admin WHERE department IS NOT NULL;
        WHEN 'vehicle_status' THEN
            RETURN QUERY SELECT DISTINCT vehicle_status FROM dropdown_admin WHERE vehicle_status IS NOT NULL;
        WHEN 'Branches' THEN
            RETURN QUERY SELECT name FROM branches WHERE branch_status = 'Active';
        WHEN 'branch_status' THEN
            RETURN QUERY SELECT DISTINCT branch_status FROM dropdown_admin WHERE branch_status IS NOT NULL;
        WHEN 'driver_status' THEN
            RETURN QUERY SELECT DISTINCT driver_status FROM dropdown_admin WHERE driver_status IS NOT NULL;
        WHEN 'maintenance_status' THEN
            RETURN QUERY SELECT DISTINCT maintenance_status FROM dropdown_admin WHERE maintenance_status IS NOT NULL;
        WHEN 'user_status' THEN
            RETURN QUERY SELECT DISTINCT user_status FROM dropdown_admin WHERE user_status IS NOT NULL;
        WHEN 'Manager' THEN
            RETURN QUERY SELECT full_name FROM profiles WHERE role = 'Manager';
        WHEN 'Drivers' THEN
            RETURN QUERY SELECT full_name FROM drivers WHERE status = 'Available';
        ELSE
            RETURN;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT SELECT ON dropdown_admin TO authenticated;
GRANT EXECUTE ON FUNCTION get_dropdown_values(TEXT) TO authenticated;

-- Create indexes for better performance on dropdown queries
CREATE INDEX idx_dropdown_admin_vehicle_type ON dropdown_admin(vehicle_type);
CREATE INDEX idx_dropdown_admin_service_type ON dropdown_admin(service_type);
CREATE INDEX idx_dropdown_admin_fuel_type ON dropdown_admin(fuel_type);

-- Add some helpful views for reporting
CREATE VIEW vehicle_summary AS
SELECT 
    b.name as branch_name,
    COUNT(*) as total_vehicles,
    COUNT(CASE WHEN v.vehicle_status = 'Active' THEN 1 END) as active_vehicles,
    COUNT(CASE WHEN v.vehicle_status = 'Maintenance' THEN 1 END) as maintenance_vehicles,
    COUNT(CASE WHEN v.assigned_driver_id IS NOT NULL THEN 1 END) as assigned_vehicles,
    AVG(v.current_km) as avg_kilometers
FROM vehicles v
JOIN branches b ON v.branch_id = b.id
GROUP BY b.id, b.name;

CREATE VIEW maintenance_summary AS
SELECT 
    b.name as branch_name,
    COUNT(*) as total_maintenance,
    COUNT(CASE WHEN m.maintenance_status = 'Completed' THEN 1 END) as completed_maintenance,
    COUNT(CASE WHEN m.maintenance_status = 'Scheduled' THEN 1 END) as scheduled_maintenance,
    COUNT(CASE WHEN m.maintenance_status = 'In Progress' THEN 1 END) as in_progress_maintenance,
    SUM(m.cost) as total_maintenance_cost
FROM maintenance_records m
JOIN vehicles v ON m.vehicle_id = v.id
JOIN branches b ON v.branch_id = b.id
GROUP BY b.id, b.name;

CREATE VIEW fuel_summary AS
SELECT 
    b.name as branch_name,
    COUNT(*) as total_fuel_records,
    SUM(f.quantity_liters) as total_fuel_liters,
    SUM(f.cost) as total_fuel_cost,
    AVG(f.consumption_per_100km) as avg_consumption_per_100km
FROM fuel_records f
JOIN vehicles v ON f.vehicle_id = v.id
JOIN branches b ON v.branch_id = b.id
GROUP BY b.id, b.name;

-- Grant access to summary views
GRANT SELECT ON vehicle_summary TO authenticated;
GRANT SELECT ON maintenance_summary TO authenticated;
GRANT SELECT ON fuel_summary TO authenticated;
