import { useEffect, useState, useCallback, useRef } from 'react'

// Performance monitoring hook
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    connectionSpeed: 'unknown' as 'slow-2g' | '2g' | '3g' | '4g' | 'unknown',
    isOnline: true
  })

  const startTimeRef = useRef<number>(Date.now())

  useEffect(() => {
    // Monitor page load time
    const handleLoad = () => {
      const loadTime = Date.now() - startTimeRef.current
      setMetrics(prev => ({ ...prev, loadTime }))
    }

    // Monitor connection speed
    const updateConnectionSpeed = () => {
      const connection = (navigator as any).connection
      if (connection) {
        setMetrics(prev => ({ 
          ...prev, 
          connectionSpeed: connection.effectiveType || 'unknown'
        }))
      }
    }

    // Monitor memory usage
    const updateMemoryUsage = () => {
      const memory = (performance as any).memory
      if (memory) {
        setMetrics(prev => ({ 
          ...prev, 
          memoryUsage: memory.usedJSHeapSize / (1024 * 1024) // MB
        }))
      }
    }

    // Monitor online/offline status
    const handleOnline = () => setMetrics(prev => ({ ...prev, isOnline: true }))
    const handleOffline = () => setMetrics(prev => ({ ...prev, isOnline: false }))

    // Initial setup
    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
    }

    updateConnectionSpeed()
    updateMemoryUsage()
    setMetrics(prev => ({ ...prev, isOnline: navigator.onLine }))

    // Set up listeners
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Set up periodic updates
    const interval = setInterval(() => {
      updateConnectionSpeed()
      updateMemoryUsage()
    }, 30000) // Update every 30 seconds

    return () => {
      window.removeEventListener('load', handleLoad)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(interval)
    }
  }, [])

  return metrics
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(options: IntersectionObserverInit = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)
  const targetRef = useRef<Element | null>(null)

  useEffect(() => {
    const element = targetRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isCurrentlyIntersecting = entry.isIntersecting
        setIsIntersecting(isCurrentlyIntersecting)
        
        if (isCurrentlyIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [hasIntersected, options])

  return {
    ref: targetRef,
    isIntersecting,
    hasIntersected
  }
}

// Debounced value hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttled callback hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const throttledCallback = useRef<T>()
  const lastExecTime = useRef<number>(0)

  return useCallback(
    ((...args: Parameters<T>) => {
      const currentTime = Date.now()
      
      if (currentTime - lastExecTime.current > delay) {
        lastExecTime.current = currentTime
        return callback(...args)
      }
      
      if (!throttledCallback.current) {
        throttledCallback.current = callback
      }
      
      return undefined
    }) as T,
    [callback, delay]
  )
}

// Virtual scrolling hook
export function useVirtualScroll<T>({
  items,
  containerHeight,
  itemHeight,
  buffer = 5
}: {
  items: T[]
  containerHeight: number
  itemHeight: number
  buffer?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)
  const [containerRef, setContainerRef] = useState<HTMLElement | null>(null)

  const visibleCount = Math.ceil(containerHeight / itemHeight)
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer)
  const endIndex = Math.min(items.length, startIndex + visibleCount + buffer * 2)

  const visibleItems = items.slice(startIndex, endIndex)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = useCallback((e: Event) => {
    const target = e.target as HTMLElement
    setScrollTop(target.scrollTop)
  }, [])

  useEffect(() => {
    if (!containerRef) return

    containerRef.addEventListener('scroll', handleScroll, { passive: true })
    return () => containerRef.removeEventListener('scroll', handleScroll)
  }, [containerRef, handleScroll])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    setContainerRef,
    startIndex,
    endIndex
  }
}

// Image lazy loading hook
export function useLazyImage(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState(placeholder || '')
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const { ref, isIntersecting } = useIntersectionObserver()

  useEffect(() => {
    if (isIntersecting) {
      setIsLoading(true)
      setHasError(false)
      
      const img = new Image()
      img.onload = () => {
        setImageSrc(src)
        setIsLoading(false)
      }
      img.onerror = () => {
        setHasError(true)
        setIsLoading(false)
      }
      img.src = src
    }
  }, [isIntersecting, src])

  return {
    ref,
    src: imageSrc,
    isLoading,
    hasError
  }
}

// Prefetch hook for API calls
export function usePrefetch() {
  const prefetchedRef = useRef<Set<string>>(new Set())

  const prefetch = useCallback(async (url: string, options?: RequestInit) => {
    if (prefetchedRef.current.has(url)) return

    prefetchedRef.current.add(url)
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...options?.headers,
          'X-Prefetch': 'true'
        }
      })
      
      if (response.ok) {
        // Cache the response
        if ('caches' in window) {
          const cache = await caches.open('prefetch-cache')
          await cache.put(url, response.clone())
        }
      }
    } catch (error) {
      console.warn('Prefetch failed:', error)
      prefetchedRef.current.delete(url)
    }
  }, [])

  return { prefetch }
}

// Bundle size monitoring
export function useBundleSize() {
  const [bundleSize, setBundleSize] = useState(0)
  const [chunkSizes, setChunkSizes] = useState<Record<string, number>>({})

  useEffect(() => {
    if (typeof window === 'undefined') return

    // Monitor performance entries for resource sizes
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      let totalSize = 0
      const chunks: Record<string, number> = {}

      entries.forEach((entry) => {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          const size = resourceEntry.transferSize || 0
          totalSize += size

          if (resourceEntry.name.includes('_next/static')) {
            const filename = resourceEntry.name.split('/').pop() || 'unknown'
            chunks[filename] = size
          }
        }
      })

      if (totalSize > 0) {
        setBundleSize(totalSize)
        setChunkSizes(chunks)
      }
    })

    observer.observe({ entryTypes: ['resource'] })

    return () => observer.disconnect()
  }, [])

  return {
    bundleSize: bundleSize / 1024, // KB
    chunkSizes: Object.fromEntries(
      Object.entries(chunkSizes).map(([key, value]) => [key, value / 1024])
    )
  }
}

// Network quality monitoring
export function useNetworkQuality() {
  const [networkQuality, setNetworkQuality] = useState<'good' | 'fair' | 'poor'>('good')
  const [rtt, setRtt] = useState(0)
  const [downlink, setDownlink] = useState(0)

  useEffect(() => {
    const updateNetworkInfo = () => {
      const connection = (navigator as any).connection
      if (connection) {
        setRtt(connection.rtt || 0)
        setDownlink(connection.downlink || 0)
        
        // Determine network quality
        if (connection.effectiveType === '4g' && connection.rtt < 100) {
          setNetworkQuality('good')
        } else if (connection.effectiveType === '3g' || connection.rtt < 300) {
          setNetworkQuality('fair')
        } else {
          setNetworkQuality('poor')
        }
      }
    }

    updateNetworkInfo()
    
    const connection = (navigator as any).connection
    if (connection) {
      connection.addEventListener('change', updateNetworkInfo)
      return () => connection.removeEventListener('change', updateNetworkInfo)
    }
  }, [])

  return {
    networkQuality,
    rtt,
    downlink
  }
}

// Cache management hook
export function useCacheManager() {
  const clearCache = useCallback(async (cacheName?: string) => {
    if ('caches' in window) {
      if (cacheName) {
        await caches.delete(cacheName)
      } else {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
      }
    }
  }, [])

  const getCacheSize = useCallback(async (cacheName?: string) => {
    if ('caches' in window) {
      let totalSize = 0
      
      if (cacheName) {
        const cache = await caches.open(cacheName)
        const requests = await cache.keys()
        
        for (const request of requests) {
          const response = await cache.match(request)
          if (response) {
            const blob = await response.blob()
            totalSize += blob.size
          }
        }
      } else {
        const cacheNames = await caches.keys()
        for (const name of cacheNames) {
          const size = await getCacheSize(name)
          totalSize += size
        }
      }
      
      return totalSize
    }
    
    return 0
  }, [])

  return {
    clearCache,
    getCacheSize
  }
}

// Component render tracking
export function useRenderTracker(componentName: string) {
  const renderCount = useRef(0)
  const lastRenderTime = useRef(Date.now())

  useEffect(() => {
    renderCount.current += 1
    const currentTime = Date.now()
    const timeSinceLastRender = currentTime - lastRenderTime.current
    lastRenderTime.current = currentTime

    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} rendered ${renderCount.current} times (${timeSinceLastRender}ms since last render)`)
    }
  })

  return {
    renderCount: renderCount.current,
    resetRenderCount: () => {
      renderCount.current = 0
    }
  }
}

// Error boundary hook
export function useErrorBoundary() {
  const [error, setError] = useState<Error | null>(null)

  const resetError = useCallback(() => {
    setError(null)
  }, [])

  const captureError = useCallback((error: Error) => {
    setError(error)
  }, [])

  if (error) {
    throw error
  }

  return {
    captureError,
    resetError
  }
}