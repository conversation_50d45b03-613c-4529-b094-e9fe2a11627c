"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useAuth } from "@/context/auth-context"
import { NotificationCenter } from "@/components/notifications/notification-center"
import { LayoutDashboard, Car, Wrench, Fuel, Users, FileText, Settings, LogOut, Menu, X, Building2 } from "lucide-react"

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard, permission: null, color: "text-blue-600" },
  { name: "Vehicles", href: "/vehicles", icon: Car, permission: null, color: "text-purple-600" },
  { name: "Branches", href: "/branches", icon: Building2, permission: null, color: "text-cyan-600" },
  { name: "Maintenance", href: "/maintenance", icon: Wrench, permission: null, color: "text-orange-600" },
  { name: "Fuel", href: "/fuel", icon: Fuel, permission: null, color: "text-green-600" },
  { name: "Drivers", href: "/drivers", icon: Users, permission: null, color: "text-pink-600" },
  { name: "Reports", href: "/reports", icon: FileText, permission: "access_reports", color: "text-indigo-600" },
  { name: "User Management", href: "/users", icon: Settings, permission: null, color: "text-red-600" },
  { name: "System Settings", href: "/settings", icon: Settings, permission: "manage_all_data", color: "text-gray-600" },
]

export function Sidebar() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()
  const { user, logout, hasPermission } = useAuth()

  const filteredNavigation = navigation.filter((item) => !item.permission || hasPermission(item.permission))

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden glass-effect"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-64 glass-effect transform transition-transform duration-300 ease-in-out md:translate-x-0",
          isOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 border-b border-white/20">
            <div className="gradient-bg-primary p-3 rounded-xl">
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Fleet Manager
              </h1>
            </div>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1 px-3 py-4">
            <nav className="space-y-2">
              {filteredNavigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 card-hover",
                      isActive
                        ? "bg-white/40 text-gray-800 shadow-lg"
                        : "text-gray-600 hover:bg-white/20 hover:text-gray-800",
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <item.icon className={cn("mr-3 h-5 w-5", item.color)} />
                    {item.name}
                  </Link>
                )
              })}
            </nav>
          </ScrollArea>

          {/* User info and logout */}
          <div className="p-4 border-t border-white/20">
            <div className="gradient-bg-secondary p-3 rounded-xl mb-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full gradient-bg-accent flex items-center justify-center mr-3">
                  <span className="text-sm font-bold text-purple-700">
                    {user?.fullName
                      ?.split(" ")
                      .map((n) => n[0])
                      .join("") || "U"}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-800 truncate">{user?.fullName}</p>
                  <p className="text-xs text-gray-600 truncate">{user?.role}</p>
                </div>
                <NotificationCenter />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start hover:bg-red-100 hover:text-red-700 transition-colors"
              onClick={logout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div className="fixed inset-0 z-30 bg-black/20 backdrop-blur-sm md:hidden" onClick={() => setIsOpen(false)} />
      )}
    </>
  )
}
