import { QueryClient } from '@tanstack/react-query'

// Cache configuration
export const CACHE_CONFIG = {
  // Default cache times
  staleTime: {
    short: 1 * 60 * 1000,      // 1 minute
    medium: 5 * 60 * 1000,     // 5 minutes
    long: 15 * 60 * 1000,      // 15 minutes
    veryLong: 60 * 60 * 1000,  // 1 hour
  },
  gcTime: {
    short: 5 * 60 * 1000,      // 5 minutes
    medium: 10 * 60 * 1000,    // 10 minutes
    long: 30 * 60 * 1000,      // 30 minutes
    veryLong: 2 * 60 * 60 * 1000, // 2 hours
  },
  // Retry configuration
  retry: {
    attempts: 3,
    delay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
  // Network mode
  networkMode: 'online' as const,
}

// Cache key generators
export const cacheKeys = {
  // Vehicles
  vehicles: {
    all: ['vehicles'] as const,
    lists: () => [...cacheKeys.vehicles.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.vehicles.lists(), { filters }] as const,
    details: () => [...cacheKeys.vehicles.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.vehicles.details(), id] as const,
    stats: () => [...cacheKeys.vehicles.all, 'stats'] as const,
    byBranch: (branchId: string) => [...cacheKeys.vehicles.all, 'by-branch', branchId] as const,
    available: () => [...cacheKeys.vehicles.all, 'available'] as const,
  },
  
  // Drivers
  drivers: {
    all: ['drivers'] as const,
    lists: () => [...cacheKeys.drivers.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.drivers.lists(), { filters }] as const,
    details: () => [...cacheKeys.drivers.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.drivers.details(), id] as const,
    stats: () => [...cacheKeys.drivers.all, 'stats'] as const,
    byBranch: (branchId: string) => [...cacheKeys.drivers.all, 'by-branch', branchId] as const,
    available: () => [...cacheKeys.drivers.all, 'available'] as const,
    expiring: (days: number) => [...cacheKeys.drivers.all, 'expiring', days] as const,
  },
  
  // Maintenance
  maintenance: {
    all: ['maintenance'] as const,
    lists: () => [...cacheKeys.maintenance.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.maintenance.lists(), { filters }] as const,
    details: () => [...cacheKeys.maintenance.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.maintenance.details(), id] as const,
    stats: () => [...cacheKeys.maintenance.all, 'stats'] as const,
    byVehicle: (vehicleId: string) => [...cacheKeys.maintenance.all, 'by-vehicle', vehicleId] as const,
    upcoming: (days: number) => [...cacheKeys.maintenance.all, 'upcoming', days] as const,
    overdue: () => [...cacheKeys.maintenance.all, 'overdue'] as const,
  },
  
  // Fuel
  fuel: {
    all: ['fuel'] as const,
    lists: () => [...cacheKeys.fuel.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.fuel.lists(), { filters }] as const,
    details: () => [...cacheKeys.fuel.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.fuel.details(), id] as const,
    stats: () => [...cacheKeys.fuel.all, 'stats'] as const,
    byVehicle: (vehicleId: string) => [...cacheKeys.fuel.all, 'by-vehicle', vehicleId] as const,
    byDateRange: (start: string, end: string) => [...cacheKeys.fuel.all, 'by-date-range', start, end] as const,
    recent: (days: number) => [...cacheKeys.fuel.all, 'recent', days] as const,
    efficiency: (vehicleId?: string) => [...cacheKeys.fuel.all, 'efficiency', vehicleId] as const,
  },
  
  // Dashboard
  dashboard: {
    all: ['dashboard'] as const,
    overview: () => [...cacheKeys.dashboard.all, 'overview'] as const,
    stats: () => [...cacheKeys.dashboard.all, 'stats'] as const,
    alerts: () => [...cacheKeys.dashboard.all, 'alerts'] as const,
    charts: () => [...cacheKeys.dashboard.all, 'charts'] as const,
  },
  
  // Branches
  branches: {
    all: ['branches'] as const,
    lists: () => [...cacheKeys.branches.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.branches.lists(), { filters }] as const,
    details: () => [...cacheKeys.branches.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.branches.details(), id] as const,
  },
  
  // Users
  users: {
    all: ['users'] as const,
    lists: () => [...cacheKeys.users.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.users.lists(), { filters }] as const,
    details: () => [...cacheKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...cacheKeys.users.details(), id] as const,
  },
  
  // Dropdown values
  dropdowns: {
    all: ['dropdowns'] as const,
    value: (column: string) => [...cacheKeys.dropdowns.all, column] as const,
  },
}

// Cache invalidation strategies
export class CacheInvalidationManager {
  constructor(private queryClient: QueryClient) {}

  // Invalidate related caches when a vehicle is updated
  invalidateVehicleRelated(vehicleId?: string) {
    // Invalidate vehicle caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.vehicles.all })
    
    // Invalidate dashboard caches (vehicles affect dashboard)
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
    
    // If specific vehicle, invalidate its maintenance and fuel records
    if (vehicleId) {
      this.queryClient.invalidateQueries({ 
        queryKey: cacheKeys.maintenance.byVehicle(vehicleId) 
      })
      this.queryClient.invalidateQueries({ 
        queryKey: cacheKeys.fuel.byVehicle(vehicleId) 
      })
    }
  }

  // Invalidate related caches when a driver is updated
  invalidateDriverRelated(driverId?: string) {
    // Invalidate driver caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.drivers.all })
    
    // Invalidate vehicle caches (driver assignments affect vehicles)
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.vehicles.all })
    
    // Invalidate dashboard caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
  }

  // Invalidate related caches when maintenance is updated
  invalidateMaintenanceRelated(vehicleId?: string) {
    // Invalidate maintenance caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.maintenance.all })
    
    // Invalidate dashboard caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
    
    // If specific vehicle, invalidate vehicle stats
    if (vehicleId) {
      this.queryClient.invalidateQueries({ 
        queryKey: cacheKeys.vehicles.detail(vehicleId) 
      })
    }
  }

  // Invalidate related caches when fuel is updated
  invalidateFuelRelated(vehicleId?: string) {
    // Invalidate fuel caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.fuel.all })
    
    // Invalidate dashboard caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
    
    // If specific vehicle, invalidate vehicle stats
    if (vehicleId) {
      this.queryClient.invalidateQueries({ 
        queryKey: cacheKeys.vehicles.detail(vehicleId) 
      })
    }
  }

  // Invalidate branch-related caches
  invalidateBranchRelated(branchId?: string) {
    // Invalidate branch caches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.branches.all })
    
    // Invalidate all data that depends on branches
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.vehicles.all })
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.drivers.all })
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.users.all })
    this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
  }

  // Smart invalidation based on entity type and operation
  smartInvalidate(entity: string, operation: 'create' | 'update' | 'delete', entityId?: string) {
    switch (entity) {
      case 'vehicles':
        this.invalidateVehicleRelated(entityId)
        break
      case 'drivers':
        this.invalidateDriverRelated(entityId)
        break
      case 'maintenance_records':
        // Get vehicle ID from the maintenance record if needed
        this.invalidateMaintenanceRelated()
        break
      case 'fuel_records':
        // Get vehicle ID from the fuel record if needed
        this.invalidateFuelRelated()
        break
      case 'branches':
        this.invalidateBranchRelated(entityId)
        break
      default:
        // Fallback: invalidate dashboard
        this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
    }
  }

  // Prefetch related data
  async prefetchRelated(entity: string, entityId: string) {
    // Implementation for prefetching related data
    // This can be used to improve perceived performance
  }
}

// Cache optimization utilities
export class CacheOptimizer {
  constructor(private queryClient: QueryClient) {}

  // Optimize cache by removing stale data
  optimizeCache() {
    // Get cache stats
    const cache = this.queryClient.getQueryCache()
    const queries = cache.getAll()
    
    console.log(`Cache optimization: ${queries.length} queries in cache`)
    
    // Remove queries that haven't been used recently
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    let removedCount = 0
    
    queries.forEach(query => {
      if (query.state.dataUpdatedAt < oneHourAgo && !query.getObserversCount()) {
        cache.remove(query)
        removedCount++
      }
    })
    
    console.log(`Cache optimization: removed ${removedCount} stale queries`)
  }

  // Get cache statistics
  getCacheStats() {
    const cache = this.queryClient.getQueryCache()
    const queries = cache.getAll()
    
    const stats = {
      totalQueries: queries.length,
      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(q => q.isStale()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      loadingQueries: queries.filter(q => q.state.status === 'pending').length,
      memoryUsage: this.estimateMemoryUsage(queries),
      byQueryKey: queries.reduce((acc, query) => {
        const key = query.queryKey[0] as string
        acc[key] = (acc[key] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    }
    
    return stats
  }

  // Estimate memory usage (rough calculation)
  private estimateMemoryUsage(queries: any[]): number {
    return queries.reduce((total, query) => {
      try {
        const dataSize = JSON.stringify(query.state.data || {}).length
        return total + dataSize
      } catch {
        return total
      }
    }, 0)
  }

  // Preload critical data
  async preloadCriticalData() {
    // Preload dashboard data
    await this.queryClient.prefetchQuery({
      queryKey: cacheKeys.dashboard.overview(),
      staleTime: CACHE_CONFIG.staleTime.medium
    })
    
    // Preload vehicle stats
    await this.queryClient.prefetchQuery({
      queryKey: cacheKeys.vehicles.stats(),
      staleTime: CACHE_CONFIG.staleTime.medium
    })
  }
}

// Export configured query client
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: CACHE_CONFIG.staleTime.medium,
        gcTime: CACHE_CONFIG.gcTime.medium,
        retry: CACHE_CONFIG.retry.attempts,
        retryDelay: CACHE_CONFIG.retry.delay,
        networkMode: CACHE_CONFIG.networkMode,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
      },
      mutations: {
        networkMode: CACHE_CONFIG.networkMode,
        retry: CACHE_CONFIG.retry.attempts,
        retryDelay: CACHE_CONFIG.retry.delay,
      },
    },
  })
}

// Performance monitoring
export class CachePerformanceMonitor {
  private metrics: Map<string, number[]> = new Map()

  recordQueryTime(queryKey: string, duration: number) {
    if (!this.metrics.has(queryKey)) {
      this.metrics.set(queryKey, [])
    }
    
    const times = this.metrics.get(queryKey)!
    times.push(duration)
    
    // Keep only last 100 measurements
    if (times.length > 100) {
      times.shift()
    }
  }

  getQueryStats(queryKey: string) {
    const times = this.metrics.get(queryKey) || []
    
    if (times.length === 0) {
      return null
    }
    
    const sorted = [...times].sort((a, b) => a - b)
    
    return {
      count: times.length,
      average: times.reduce((sum, time) => sum + time, 0) / times.length,
      median: sorted[Math.floor(sorted.length / 2)],
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)]
    }
  }

  getAllStats() {
    const allStats: Record<string, any> = {}
    
    this.metrics.forEach((times, queryKey) => {
      allStats[queryKey] = this.getQueryStats(queryKey)
    })
    
    return allStats
  }
}

export const cachePerformanceMonitor = new CachePerformanceMonitor()
