"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { supabase } from "@/lib/supabase"

export interface User {
  id: string
  fullName: string
  email: string
  role: "Super Admin" | "Manager" | "Employee"
  branchId?: string
}

interface AuthContextType {
  user: User | null
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  hasPermission: (permission: string) => boolean
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const login = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)

    try {
      // Demo authentication for testing
      if (email === '<EMAIL>' && password === 'admin123') {
        const userData = {
          id: '11111111-1111-1111-1111-111111111111',
          fullName: 'System Administrator',
          email: '<EMAIL>',
          role: 'Super Admin' as const,
          branchId: undefined,
        }

        setUser(userData)
        localStorage.setItem("user", JSON.stringify(userData))
        setIsLoading(false)
        return true
      }

      if (email === '<EMAIL>' && password === 'manager123') {
        const userData = {
          id: '22222222-2222-2222-2222-222222222222',
          fullName: 'Branch Manager',
          email: '<EMAIL>',
          role: 'Manager' as const,
          branchId: '550e8400-e29b-41d4-a716-446655440001',
        }

        setUser(userData)
        localStorage.setItem("user", JSON.stringify(userData))
        setIsLoading(false)
        return true
      }

      if (email === '<EMAIL>' && password === 'employee123') {
        const userData = {
          id: '33333333-3333-3333-3333-333333333333',
          fullName: 'Fleet Employee',
          email: '<EMAIL>',
          role: 'Employee' as const,
          branchId: '550e8400-e29b-41d4-a716-446655440001',
        }

        setUser(userData)
        localStorage.setItem("user", JSON.stringify(userData))
        setIsLoading(false)
        return true
      }

      // Try Supabase Auth for other users
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error("Authentication failed:", error.message)
        setIsLoading(false)
        return false
      }

      if (data.user) {
        // Get user profile from database
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (profileError) {
          console.error("Failed to fetch user profile:", profileError.message)
          setIsLoading(false)
          return false
        }

        const userData = {
          id: profile.id,
          fullName: profile.full_name,
          email: profile.email,
          role: profile.role as "Super Admin" | "Manager" | "Employee",
          branchId: profile.branch_id,
        }

        setUser(userData)
        localStorage.setItem("user", JSON.stringify(userData))
        setIsLoading(false)
        return true
      }

      setIsLoading(false)
      return false


    } catch (error) {
      console.error("API authentication failed:", error)
      setIsLoading(false)
      return false
    }
  }

  const logout = async () => {
    await supabase.auth.signOut()
    setUser(null)
    localStorage.removeItem("user")
  }

  const hasPermission = (permission: string): boolean => {
    if (!user) return false

    const permissions = {
      "Super Admin": ["manage_all_data", "manage_users", "assign_vehicles", "access_all_reports", "view_all_data"],
      Manager: ["manage_own_vehicles", "view_own_data", "assign_employees", "access_reports"],
      Employee: ["input_data", "view_assigned_data"],
    }

    return permissions[user.role]?.includes(permission) || false
  }

  useEffect(() => {
    // Check for existing session
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()

      if (session?.user) {
        // Get user profile from database
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single()

        if (!error && profile) {
          const userData = {
            id: profile.id,
            fullName: profile.full_name,
            email: profile.email,
            role: profile.role as "Super Admin" | "Manager" | "Employee",
            branchId: profile.branch_id,
          }
          setUser(userData)
          localStorage.setItem("user", JSON.stringify(userData))
        }
      } else {
        // Check localStorage as fallback
        const savedUser = localStorage.getItem("user")
        if (savedUser) {
          try {
            const parsedUser = JSON.parse(savedUser)
            setUser(parsedUser)
          } catch (error) {
            console.error("Failed to parse saved user:", error)
            localStorage.removeItem("user")
          }
        }
      }
      setIsLoading(false)
    }

    checkSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_OUT' || !session) {
          setUser(null)
          localStorage.removeItem("user")
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return (
    <AuthContext.Provider value={{ user, login, logout, hasPermission, isLoading }}>{children}</AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
