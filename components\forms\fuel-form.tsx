"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingButton } from "@/components/ui/loading-button"


import { supabaseApiClient, type FuelRecord, type Vehicle } from "@/lib/supabase-api-client"

const fuelSchema = z.object({
  vehicleId: z.string().min(1, "Vehicle selection is required"),
  date: z.string().min(1, "Date is required"),
  quantity: z.number().min(0.1, "Quantity must be greater than 0"),
  distance: z.number().min(1, "Distance must be greater than 0"),
  cost: z.number().min(0.01, "Cost must be greater than 0"),
  station: z.string().min(1, "Station name is required"),
})

type FuelFormData = z.infer<typeof fuelSchema>

interface FuelFormProps {
  fuel?: FuelRecord
  onSuccess: () => void
  onCancel: () => void
}

export function FuelForm({ fuel, onSuccess, onCancel }: FuelFormProps) {
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [loadingVehicles, setLoadingVehicles] = useState(true)
  const [calculatedConsumption, setCalculatedConsumption] = useState<number | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<FuelFormData>({
    resolver: zodResolver(fuelSchema),
    defaultValues: {
      vehicleId: fuel?.vehicle_id || "",
      date: fuel?.date || new Date().toISOString().split("T")[0],
      quantity: fuel?.["quantity_(L)"] || 0,
      distance: fuel?.["distance_(km)"] || 0,
      cost: fuel?.cost || 0,
      station: fuel?.station || "",
    },
  })

  const watchedQuantity = watch("quantity")
  const watchedDistance = watch("distance")

  // const { execute, isLoading } = useAsyncOperation({
  //   successMessage: fuel ? "Fuel record updated successfully" : "Fuel record added successfully",
  //   onSuccess: () => {
  //     reset()
  //     onSuccess()
  //   },
  // })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        const vehiclesData = await supabaseApiClient.getVehicles()
        setVehicles(vehiclesData.filter((v) => v.vehicle_status === "Active" || v.vehicle_id === fuel?.vehicle_id))
      } catch (error) {
        console.error("Failed to load vehicles:", error)
      } finally {
        setLoadingVehicles(false)
      }
    }

    loadVehicles()
  }, [fuel])

  useEffect(() => {
    if (watchedQuantity > 0 && watchedDistance > 0) {
      const consumption = (watchedQuantity / watchedDistance) * 100
      setCalculatedConsumption(Math.round(consumption * 100) / 100) // Round to 2 decimal places
    } else {
      setCalculatedConsumption(null)
    }
  }, [watchedQuantity, watchedDistance])

  const onSubmit = async (data: FuelFormData) => {
    setIsLoading(true)
    try {
      const consumption = (data.quantity / data.distance) * 100

      const fuelData = {
        vehicle_id: data.vehicleId,
        date: data.date,
        quantity_liters: data.quantity,
        distance_km: data.distance,
        cost: data.cost,
        station: data.station,
        consumption_per_100km: Number.parseFloat(consumption.toFixed(2)),
      }

      if (fuel) {
        await supabaseApiClient.updateFuel(fuel.id, fuelData)
      } else {
        await supabaseApiClient.addFuel(fuelData)
      }

      reset()
      onSuccess()
    } catch (error) {
      console.error('Error saving fuel record:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="vehicleId" className="text-gray-700">
            Vehicle *
          </Label>
          <Select
            value={watch("vehicleId") || ""}
            onValueChange={(value) => setValue("vehicleId", value)}
            disabled={loadingVehicles}
          >
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder={loadingVehicles ? "Loading vehicles..." : "Select vehicle"} />
            </SelectTrigger>
            <SelectContent>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.vehicle_id} value={vehicle.vehicle_id}>
                  {vehicle.vin_number} - {vehicle.vehicle_type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.vehicleId && <p className="text-red-500 text-sm mt-1">{errors.vehicleId.message}</p>}
        </div>

        <div>
          <Label htmlFor="date" className="text-gray-700">
            Date *
          </Label>
          <Input
            id="date"
            type="date"
            max={new Date().toISOString().split('T')[0]}
            {...register("date")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.date ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.date && <p className="text-red-500 text-sm mt-1">{errors.date.message}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="quantity" className="text-gray-700">
              Quantity (Liters) *
            </Label>
            <Input
              id="quantity"
              type="number"
              step="0.1"
              min="0"
              {...register("quantity", { valueAsNumber: true })}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.quantity ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="0.0"
            />
            {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity.message}</p>}
          </div>

          <div>
            <Label htmlFor="distance" className="text-gray-700">
              Distance (km) *
            </Label>
            <Input
              id="distance"
              type="number"
              min="0"
              {...register("distance", { valueAsNumber: true })}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.distance ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="0"
            />
            {errors.distance && <p className="text-red-500 text-sm mt-1">{errors.distance.message}</p>}
          </div>
        </div>

        {calculatedConsumption && (
          <div className="p-3 gradient-bg-secondary rounded-lg">
            <div className="text-sm text-gray-700">
              <strong>Calculated Consumption:</strong> {calculatedConsumption.toFixed(2)} L/100km
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="cost" className="text-gray-700">
              Total Cost (EGP) *
            </Label>
            <Input
              id="cost"
              type="number"
              step="0.01"
              min="0"
              {...register("cost", { valueAsNumber: true })}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.cost ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="0.00"
            />
            {errors.cost && <p className="text-red-500 text-sm mt-1">{errors.cost.message}</p>}
          </div>

          <div>
            <Label htmlFor="station" className="text-gray-700">
              Gas Station *
            </Label>
            <Input
              id="station"
              placeholder="Enter gas station name"
              {...register("station")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.station ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
            />
            {errors.station && <p className="text-red-500 text-sm mt-1">{errors.station.message}</p>}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <LoadingButton type="submit" loading={isLoading} className="gradient-bg-primary text-blue-700 hover:opacity-90">
          {fuel ? "Update Record" : "Add Record"}
        </LoadingButton>
      </div>
    </form>
  )
}
