"use client"

import { memo, useMemo, useState } from "react"
import { FixedSizeList as List } from "react-window"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react"

interface Column<T> {
  key: keyof T
  title: string
  render?: (value: any, item: T) => React.ReactNode
  sortable?: boolean
  width?: number
  align?: 'left' | 'center' | 'right'
}

interface OptimizedTableProps<T> {
  data: T[]
  columns: Column<T>[]
  keyExtractor: (item: T) => string
  loading?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  pageSize?: number
  virtualScrolling?: boolean
  height?: number
  onRowClick?: (item: T) => void
  actions?: (item: T) => React.ReactNode
}

type SortDirection = 'asc' | 'desc' | null

// Memoized row component for performance
const TableRowMemo = memo(function TableRowMemo({
  item,
  columns,
  onRowClick,
  actions,
  style
}: {
  item: any
  columns: Column<any>[]
  onRowClick?: (item: any) => void
  actions?: (item: any) => React.ReactNode
  style?: React.CSSProperties
}) {
  return (
    <TableRow
      className={`hover:bg-gray-50 ${onRowClick ? 'cursor-pointer' : ''}`}
      onClick={() => onRowClick?.(item)}
      style={style}
    >
      {columns.map((column) => (
        <TableCell
          key={String(column.key)}
          className={`py-2 px-3 text-${column.align || 'left'}`}
          style={{ width: column.width }}
        >
          {column.render ? column.render(item[column.key], item) : String(item[column.key] || '')}
        </TableCell>
      ))}
      {actions && (
        <TableCell className="py-2 px-3 w-32">
          {actions(item)}
        </TableCell>
      )}
    </TableRow>
  )
})

// Virtual list row component
const VirtualRow = memo(function VirtualRow({
  index,
  style,
  data
}: {
  index: number
  style: React.CSSProperties
  data: {
    items: any[]
    columns: Column<any>[]
    onRowClick?: (item: any) => void
    actions?: (item: any) => React.ReactNode
  }
}) {
  const { items, columns, onRowClick, actions } = data
  const item = items[index]
  
  return (
    <div style={style}>
      <TableRowMemo
        item={item}
        columns={columns}
        onRowClick={onRowClick}
        actions={actions}
      />
    </div>
  )
})

export function OptimizedTable<T extends unknown>({
  data,
  columns,
  keyExtractor,
  loading = false,
  searchable = true,
  searchPlaceholder = "Search...",
  pageSize = 50,
  virtualScrolling = false,
  height = 400,
  onRowClick,
  actions
}: OptimizedTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState("")
  const [sortKey, setSortKey] = useState<keyof T | null>(null)
  const [sortDirection, setSortDirection] = useState<SortDirection>(null)
  const [currentPage, setCurrentPage] = useState(0)

  // Memoized filtered and sorted data
  const processedData = useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(item =>
        columns.some(column => {
          const value = item[column.key]
          return value && String(value).toLowerCase().includes(searchLower)
        })
      )
    }

    // Apply sorting
    if (sortKey && sortDirection) {
      filtered.sort((a, b) => {
        const aValue = a[sortKey]
        const bValue = b[sortKey]
        
        if (aValue === bValue) return 0
        
        let comparison = 0
        if (aValue > bValue) comparison = 1
        else if (aValue < bValue) comparison = -1
        
        return sortDirection === 'asc' ? comparison : -comparison
      })
    }

    return filtered
  }, [data, searchTerm, sortKey, sortDirection, columns])

  // Memoized paginated data
  const paginatedData = useMemo(() => {
    if (virtualScrolling) return processedData
    
    const start = currentPage * pageSize
    const end = start + pageSize
    return processedData.slice(start, end)
  }, [processedData, currentPage, pageSize, virtualScrolling])

  const totalPages = Math.ceil(processedData.length / pageSize)

  const handleSort = (key: keyof T) => {
    if (sortKey === key) {
      setSortDirection(current => {
        if (current === 'asc') return 'desc'
        if (current === 'desc') return null
        return 'asc'
      })
    } else {
      setSortKey(key)
      setSortDirection('asc')
    }
    setCurrentPage(0)
  }

  const getSortIcon = (key: keyof T) => {
    if (sortKey !== key) return <ArrowUpDown className="ml-1 h-4 w-4" />
    if (sortDirection === 'asc') return <ArrowUp className="ml-1 h-4 w-4" />
    if (sortDirection === 'desc') return <ArrowDown className="ml-1 h-4 w-4" />
    return <ArrowUpDown className="ml-1 h-4 w-4" />
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {/* Search skeleton */}
        {searchable && (
          <div className="h-10 bg-gray-200 rounded animate-pulse" />
        )}
        
        {/* Table skeleton */}
        <div className="space-y-2">
          <div className="h-12 bg-gray-200 rounded animate-pulse" />
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and controls */}
      {searchable && (
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value)
                setCurrentPage(0)
              }}
              className="pl-10"
            />
          </div>
          
          <Badge variant="outline" className="text-sm">
            {processedData.length} {processedData.length === 1 ? 'item' : 'items'}
          </Badge>
        </div>
      )}

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              {columns.map((column) => (
                <TableHead
                  key={String(column.key)}
                  className={`py-3 px-3 text-${column.align || 'left'} ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center">
                    {column.title}
                    {column.sortable && getSortIcon(column.key)}
                  </div>
                </TableHead>
              ))}
              {actions && (
                <TableHead className="py-3 px-3 w-32">Actions</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {virtualScrolling ? (
              <tr>
                <td colSpan={columns.length + (actions ? 1 : 0)} className="p-0">
                  <List
                    height={height}
                    itemCount={processedData.length}
                    itemSize={64}
                    itemData={{
                      items: processedData as any[],
                      columns: columns as Column<any>[],
                      onRowClick: onRowClick as ((item: any) => void) | undefined,
                      actions: actions as ((item: any) => React.ReactNode) | undefined
                    }}
                    width="100%"
                  >
                    {VirtualRow as any}
                  </List>
                </td>
              </tr>
            ) : (
              paginatedData.map((item) => (
                <TableRowMemo
                  key={keyExtractor(item)}
                  item={item}
                  columns={columns}
                  onRowClick={onRowClick}
                  actions={actions}
                />
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {!virtualScrolling && totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {currentPage * pageSize + 1} to{' '}
            {Math.min((currentPage + 1) * pageSize, processedData.length)} of{' '}
            {processedData.length} items
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(0)}
              disabled={currentPage === 0}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(current => Math.max(0, current - 1))}
              disabled={currentPage === 0}
            >
              Previous
            </Button>
            
            <div className="flex items-center gap-1">
              <span className="text-sm">Page</span>
              <Select
                value={String(currentPage + 1)}
                onValueChange={(value) => setCurrentPage(parseInt(value) - 1)}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: totalPages }).map((_, i) => (
                    <SelectItem key={i} value={String(i + 1)}>
                      {i + 1}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <span className="text-sm">of {totalPages}</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(current => Math.min(totalPages - 1, current + 1))}
              disabled={currentPage >= totalPages - 1}
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(totalPages - 1)}
              disabled={currentPage >= totalPages - 1}
            >
              Last
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}