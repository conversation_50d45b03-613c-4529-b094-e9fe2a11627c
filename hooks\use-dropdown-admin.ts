"use client"

import { useState, useEffect, useCallback } from "react"
import { supabaseApiClient, type DropdownAdmin } from "@/lib/supabase-api-client"
import { useToast } from "@/hooks/use-toast"

export interface DropdownCategory {
  id: keyof DropdownAdmin
  name: string
  icon: string
  description: string
  color: string
}

export const DROPDOWN_CATEGORIES: DropdownCategory[] = [
  {
    id: "Vehicle_Type",
    name: "Vehicle Type",
    icon: "Car",
    description: "Types of vehicles in the fleet",
    color: "text-blue-600"
  },
  {
    id: "Service_Type", 
    name: "Service Type",
    icon: "Wrench",
    description: "Service categories for vehicles",
    color: "text-green-600"
  },
  {
    id: "Fuel_Type",
    name: "Fuel Type", 
    icon: "Fuel",
    description: "Available fuel types",
    color: "text-orange-600"
  },
  {
    id: "Color",
    name: "Color",
    icon: "Palette",
    description: "Available vehicle colors",
    color: "text-purple-600"
  },
  {
    id: "Department",
    name: "Department",
    icon: "Building",
    description: "Organizational departments",
    color: "text-indigo-600"
  },
  {
    id: "vehicle_status",
    name: "Vehicle Status",
    icon: "Activity",
    description: "Vehicle status options",
    color: "text-red-600"
  },
  {
    id: "driver_status",
    name: "Driver Status",
    icon: "UserCheck",
    description: "Driver status options",
    color: "text-green-600"
  },
  {
    id: "maintenance_status",
    name: "Maintenance Status",
    icon: "Wrench",
    description: "Maintenance status options",
    color: "text-yellow-600"
  },
  {
    id: "user_status",
    name: "User Status",
    icon: "Users",
    description: "User status options",
    color: "text-blue-600"
  },
  {
    id: "branch_status",
    name: "Branch Status",
    icon: "Building",
    description: "Branch status options",
    color: "text-purple-600"
  },
  {
    id: "Branches",
    name: "Branches",
    icon: "MapPin",
    description: "Available branches",
    color: "text-teal-600"
  },
  {
    id: "Manager",
    name: "Manager",
    icon: "UserCheck",
    description: "Available managers",
    color: "text-amber-600"
  },
  {
    id: "Drivers",
    name: "Drivers",
    icon: "Users",
    description: "Available drivers",
    color: "text-cyan-600"
  }
]

export function useDropdownAdmin() {
  const [data, setData] = useState<DropdownAdmin[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Fetch dropdown data from Supabase
  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log("Fetching dropdown data from Supabase...")
      const response = await supabaseApiClient.getDropdownAdmin()

      console.log("Supabase response:", response)

      if (response && Array.isArray(response) && response.length > 0) {
        setData(response)
        console.log("Successfully loaded data from Supabase:", response.length, "records")

        toast({
          title: "Success",
          description: `Loaded ${response.length} records from database`,
        })
      } else {
        console.warn("No dropdown data found in database")
        setError("No dropdown data available")

        toast({
          title: "Notice",
          description: "No dropdown data found in database",
          variant: "default",
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch dropdown data"
      console.error("Failed to fetch dropdown data:", errorMessage)

      setError(errorMessage)

      toast({
        title: "Error",
        description: "Failed to load dropdown data from database",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [toast])

  // Get unique values for a specific category
  const getCategoryValues = useCallback((category: keyof DropdownAdmin): string[] => {
    const values = data
      .map(item => item[category])
      .filter(value => value && value.trim().length > 0)
      .filter((value, index, array) => array.indexOf(value) === index) // Remove duplicates
      .sort()
    
    return values
  }, [data])

  // Add a new value to a category
  const addValue = useCallback(async (category: keyof DropdownAdmin, value: string): Promise<boolean> => {
    try {
      const trimmedValue = value.trim()
      
      if (trimmedValue.length === 0) {
        toast({
          title: "Error",
          description: "Value cannot be empty",
          variant: "destructive",
        })
        return false
      }

      // Check if value already exists
      const existingValues = getCategoryValues(category)
      if (existingValues.some(v => v.toLowerCase() === trimmedValue.toLowerCase())) {
        toast({
          title: "Error", 
          description: "This value already exists",
          variant: "destructive",
        })
        return false
      }

      await supabaseApiClient.addDropdownValue(category, trimmedValue)
      
      toast({
        title: "Success",
        description: `Added "${trimmedValue}" to ${DROPDOWN_CATEGORIES.find(c => c.id === category)?.name}`,
      })
      
      // Refresh data
      await fetchData()
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to add value"
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
      return false
    }
  }, [getCategoryValues, fetchData, toast])

  // Update an existing value
  const updateValue = useCallback(async (
    category: keyof DropdownAdmin, 
    oldValue: string, 
    newValue: string
  ): Promise<boolean> => {
    try {
      const trimmedNewValue = newValue.trim()
      
      if (trimmedNewValue.length === 0) {
        toast({
          title: "Error",
          description: "Value cannot be empty",
          variant: "destructive",
        })
        return false
      }

      if (oldValue === trimmedNewValue) {
        return true // No change needed
      }

      // Check if new value already exists
      const existingValues = getCategoryValues(category)
      if (existingValues.some(v => v.toLowerCase() === trimmedNewValue.toLowerCase() && v !== oldValue)) {
        toast({
          title: "Error",
          description: "This value already exists",
          variant: "destructive",
        })
        return false
      }

      await supabaseApiClient.updateDropdownValue(category, oldValue, trimmedNewValue)
      
      toast({
        title: "Success",
        description: `Updated "${oldValue}" to "${trimmedNewValue}"`,
      })
      
      // Refresh data
      await fetchData()
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update value"
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
      return false
    }
  }, [getCategoryValues, fetchData, toast])

  // Delete a value
  const deleteValue = useCallback(async (category: keyof DropdownAdmin, value: string): Promise<boolean> => {
    try {
      await supabaseApiClient.deleteDropdownValue(category, value)
      
      toast({
        title: "Success",
        description: `Deleted "${value}" from ${DROPDOWN_CATEGORIES.find(c => c.id === category)?.name}`,
      })
      
      // Refresh data
      await fetchData()
      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete value"
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
      return false
    }
  }, [fetchData, toast])

  // Get statistics
  const getStats = useCallback(() => {
    const totalCategories = DROPDOWN_CATEGORIES.length
    const totalValues = DROPDOWN_CATEGORIES.reduce((sum, category) => {
      return sum + getCategoryValues(category.id).length
    }, 0)
    
    const categoryStats = DROPDOWN_CATEGORIES.map(category => ({
      ...category,
      valueCount: getCategoryValues(category.id).length,
      values: getCategoryValues(category.id)
    }))

    return {
      totalCategories,
      totalValues,
      categoryStats
    }
  }, [getCategoryValues])

  // Bulk operations
  const bulkAddValues = useCallback(async (
    category: keyof DropdownAdmin, 
    values: string[]
  ): Promise<{ success: number; failed: number }> => {
    let success = 0
    let failed = 0

    for (const value of values) {
      const result = await addValue(category, value)
      if (result) {
        success++
      } else {
        failed++
      }
    }

    return { success, failed }
  }, [addValue])

  // Export data
  const exportData = useCallback(() => {
    const exportData = DROPDOWN_CATEGORIES.map(category => ({
      category: category.name,
      values: getCategoryValues(category.id)
    }))

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `dropdown-admin-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    toast({
      title: "Success",
      description: "Data exported successfully",
    })
  }, [getCategoryValues, toast])

  // Initial data fetch
  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    isLoading,
    error,
    categories: DROPDOWN_CATEGORIES,
    getCategoryValues,
    addValue,
    updateValue,
    deleteValue,
    bulkAddValues,
    exportData,
    getStats,
    refetch: fetchData
  }
}