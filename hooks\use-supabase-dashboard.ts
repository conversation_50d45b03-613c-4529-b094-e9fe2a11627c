import { useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient } from '@/lib/supabase-api-client'
import { supabase } from '@/lib/supabase'
import { useVehicles } from './use-supabase-vehicles'
import { useDrivers } from './use-supabase-drivers'
import { useMaintenance } from './use-supabase-maintenance'
import { useFuel } from './use-supabase-fuel'

// Query keys
export const dashboardKeys = {
  all: ['dashboard'] as const,
  overview: () => [...dashboardKeys.all, 'overview'] as const,
  stats: () => [...dashboardKeys.all, 'stats'] as const,
  alerts: () => [...dashboardKeys.all, 'alerts'] as const,
  charts: () => [...dashboardKeys.all, 'charts'] as const,
}

// ==================== DASHBOARD DATA ====================

export function useDashboardData() {
  return useQuery({
    queryKey: dashboardKeys.overview(),
    queryFn: () => supabaseApiClient.getDashboardData(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

// ==================== DASHBOARD STATS ====================

export function useDashboardStats() {
  const { data: vehicles = [] } = useVehicles()
  const { data: drivers = [] } = useDrivers()
  const { data: maintenance = [] } = useMaintenance()
  const { data: fuel = [] } = useFuel()

  return useQuery({
    queryKey: dashboardKeys.stats(),
    queryFn: () => {
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
      
      // Vehicle stats
      const vehicleStats = {
        total: vehicles.length,
        active: vehicles.filter(v => v.vehicle_status === 'Active').length,
        maintenance: vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
        assigned: vehicles.filter(v => v.assigned_driver_id).length,
      }

      // Driver stats
      const driverStats = {
        total: drivers.length,
        active: drivers.filter(d => d.status === 'Active').length,
        available: drivers.filter(d => d.status === 'Available').length,
        expiringSoon: drivers.filter(d => {
          const expiryDate = new Date(d.license_expiry)
          const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
          return expiryDate <= oneMonthFromNow && expiryDate > now
        }).length,
      }

      // Maintenance stats
      const maintenanceStats = {
        total: maintenance.length,
        scheduled: maintenance.filter(m => m.maintenance_status === 'Scheduled').length,
        inProgress: maintenance.filter(m => m.maintenance_status === 'In Progress').length,
        overdue: maintenance.filter(m => {
          const scheduledDate = new Date(m.scheduled_date)
          return scheduledDate < now && m.maintenance_status === 'Scheduled'
        }).length,
        thisMonthCost: maintenance
          .filter(m => 
            m.maintenance_status === 'Completed' && 
            new Date(m.completed_date || m.scheduled_date) >= thisMonth
          )
          .reduce((sum, m) => sum + m.cost, 0),
      }

      // Fuel stats
      const thisMonthFuel = fuel.filter(f => new Date(f.date) >= thisMonth)
      const lastMonthFuel = fuel.filter(f => {
        const date = new Date(f.date)
        return date >= lastMonth && date < thisMonth
      })

      const fuelStats = {
        total: fuel.length,
        thisMonthRecords: thisMonthFuel.length,
        thisMonthCost: thisMonthFuel.reduce((sum, f) => sum + f.cost, 0),
        thisMonthLiters: thisMonthFuel.reduce((sum, f) => sum + f.quantity_liters, 0),
        lastMonthCost: lastMonthFuel.reduce((sum, f) => sum + f.cost, 0),
        averageConsumption: (() => {
          const validRecords = fuel.filter(f => f.consumption_per_100km && f.consumption_per_100km > 0)
          return validRecords.length > 0 
            ? validRecords.reduce((sum, f) => sum + (f.consumption_per_100km || 0), 0) / validRecords.length
            : 0
        })(),
      }

      // Calculate trends
      const fuelCostTrend = fuelStats.lastMonthCost > 0 
        ? ((fuelStats.thisMonthCost - fuelStats.lastMonthCost) / fuelStats.lastMonthCost) * 100
        : 0

      return {
        vehicles: vehicleStats,
        drivers: driverStats,
        maintenance: maintenanceStats,
        fuel: fuelStats,
        trends: {
          fuelCost: Math.round(fuelCostTrend * 100) / 100,
        },
        utilization: {
          vehicleUtilization: vehicleStats.total > 0 
            ? Math.round((vehicleStats.assigned / vehicleStats.total) * 100)
            : 0,
          driverUtilization: driverStats.total > 0 
            ? Math.round((driverStats.active / driverStats.total) * 100)
            : 0,
        }
      }
    },
    enabled: vehicles.length > 0 || drivers.length > 0 || maintenance.length > 0 || fuel.length > 0,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

// ==================== DASHBOARD ALERTS ====================

export function useDashboardAlerts() {
  const { data: vehicles = [] } = useVehicles()
  const { data: drivers = [] } = useDrivers()
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: dashboardKeys.alerts(),
    queryFn: () => {
      const now = new Date()
      const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

      const alerts = []

      // Overdue maintenance
      const overdueMaintenance = maintenance.filter(m => {
        const scheduledDate = new Date(m.scheduled_date)
        return scheduledDate < now && m.maintenance_status === 'Scheduled'
      })

      if (overdueMaintenance.length > 0) {
        alerts.push({
          id: 'overdue-maintenance',
          type: 'error' as const,
          title: 'صيانة متأخرة',
          message: `${overdueMaintenance.length} مركبة تحتاج صيانة متأخرة`,
          count: overdueMaintenance.length,
          action: 'view-maintenance',
          priority: 'high' as const
        })
      }

      // Upcoming maintenance (this week)
      const upcomingMaintenance = maintenance.filter(m => {
        const scheduledDate = new Date(m.scheduled_date)
        return scheduledDate >= now && scheduledDate <= oneWeekFromNow && 
               m.maintenance_status === 'Scheduled'
      })

      if (upcomingMaintenance.length > 0) {
        alerts.push({
          id: 'upcoming-maintenance',
          type: 'warning' as const,
          title: 'صيانة قادمة',
          message: `${upcomingMaintenance.length} مركبة تحتاج صيانة هذا الأسبوع`,
          count: upcomingMaintenance.length,
          action: 'view-maintenance',
          priority: 'medium' as const
        })
      }

      // Expired driver licenses
      const expiredLicenses = drivers.filter(d => {
        const expiryDate = new Date(d.license_expiry)
        return expiryDate <= now
      })

      if (expiredLicenses.length > 0) {
        alerts.push({
          id: 'expired-licenses',
          type: 'error' as const,
          title: 'رخص منتهية الصلاحية',
          message: `${expiredLicenses.length} سائق لديه رخصة منتهية الصلاحية`,
          count: expiredLicenses.length,
          action: 'view-drivers',
          priority: 'high' as const
        })
      }

      // Expiring driver licenses (this month)
      const expiringLicenses = drivers.filter(d => {
        const expiryDate = new Date(d.license_expiry)
        return expiryDate > now && expiryDate <= oneMonthFromNow
      })

      if (expiringLicenses.length > 0) {
        alerts.push({
          id: 'expiring-licenses',
          type: 'warning' as const,
          title: 'رخص تنتهي قريباً',
          message: `${expiringLicenses.length} سائق رخصته تنتهي خلال شهر`,
          count: expiringLicenses.length,
          action: 'view-drivers',
          priority: 'medium' as const
        })
      }

      // Vehicles in maintenance
      const vehiclesInMaintenance = vehicles.filter(v => v.vehicle_status === 'Maintenance')

      if (vehiclesInMaintenance.length > 0) {
        alerts.push({
          id: 'vehicles-in-maintenance',
          type: 'info' as const,
          title: 'مركبات في الصيانة',
          message: `${vehiclesInMaintenance.length} مركبة حالياً في الصيانة`,
          count: vehiclesInMaintenance.length,
          action: 'view-vehicles',
          priority: 'low' as const
        })
      }

      // Unassigned vehicles
      const unassignedVehicles = vehicles.filter(v => 
        v.vehicle_status === 'Active' && !v.assigned_driver_id
      )

      if (unassignedVehicles.length > 0) {
        alerts.push({
          id: 'unassigned-vehicles',
          type: 'info' as const,
          title: 'مركبات غير مخصصة',
          message: `${unassignedVehicles.length} مركبة نشطة غير مخصصة لسائق`,
          count: unassignedVehicles.length,
          action: 'assign-vehicles',
          priority: 'low' as const
        })
      }

      // Sort by priority
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return alerts.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority])
    },
    enabled: vehicles.length > 0 || drivers.length > 0 || maintenance.length > 0,
    staleTime: 30 * 1000, // 30 seconds for alerts
  })
}

// ==================== DASHBOARD CHARTS ====================

export function useDashboardCharts() {
  const { data: vehicles = [] } = useVehicles()
  const { data: fuel = [] } = useFuel()
  const { data: maintenance = [] } = useMaintenance()

  return useQuery({
    queryKey: dashboardKeys.charts(),
    queryFn: () => {
      // Vehicle status distribution
      const vehicleStatusChart = {
        labels: ['نشطة', 'في الصيانة', 'غير نشطة', 'خارج الخدمة'],
        data: [
          vehicles.filter(v => v.vehicle_status === 'Active').length,
          vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
          vehicles.filter(v => v.vehicle_status === 'Inactive').length,
          vehicles.filter(v => v.vehicle_status === 'Out of Service').length,
        ],
        colors: ['#10b981', '#f59e0b', '#6b7280', '#ef4444']
      }

      // Vehicle types distribution
      const vehicleTypeChart = {
        labels: [...new Set(vehicles.map(v => v.vehicle_type))],
        data: [...new Set(vehicles.map(v => v.vehicle_type))].map(type =>
          vehicles.filter(v => v.vehicle_type === type).length
        ),
        colors: ['#3b82f6', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316']
      }

      // Monthly fuel cost trend (last 6 months)
      const sixMonthsAgo = new Date()
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
      
      const monthlyFuelData = fuel
        .filter(f => new Date(f.date) >= sixMonthsAgo)
        .reduce((acc, record) => {
          const month = new Date(record.date).toISOString().slice(0, 7) // YYYY-MM
          acc[month] = (acc[month] || 0) + record.cost
          return acc
        }, {} as Record<string, number>)

      const fuelTrendChart = {
        labels: Object.keys(monthlyFuelData).sort(),
        data: Object.keys(monthlyFuelData).sort().map(month => 
          Math.round(monthlyFuelData[month] * 100) / 100
        )
      }

      // Maintenance cost by type
      const completedMaintenance = maintenance.filter(m => m.maintenance_status === 'Completed')
      const maintenanceCostByType = completedMaintenance.reduce((acc, record) => {
        acc[record.maintenance_type] = (acc[record.maintenance_type] || 0) + record.cost
        return acc
      }, {} as Record<string, number>)

      const maintenanceCostChart = {
        labels: Object.keys(maintenanceCostByType),
        data: Object.values(maintenanceCostByType).map(cost => 
          Math.round(cost * 100) / 100
        ),
        colors: ['#ef4444', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6']
      }

      // Fleet utilization over time (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      
      const dailyUtilization = []
      for (let i = 0; i < 30; i++) {
        const date = new Date(thirtyDaysAgo)
        date.setDate(date.getDate() + i)
        
        const totalVehicles = vehicles.length
        const activeVehicles = vehicles.filter(v => v.vehicle_status === 'Active').length
        const utilization = totalVehicles > 0 ? (activeVehicles / totalVehicles) * 100 : 0
        
        dailyUtilization.push({
          date: date.toISOString().slice(0, 10),
          utilization: Math.round(utilization * 100) / 100
        })
      }

      const utilizationChart = {
        labels: dailyUtilization.map(d => d.date),
        data: dailyUtilization.map(d => d.utilization)
      }

      return {
        vehicleStatus: vehicleStatusChart,
        vehicleTypes: vehicleTypeChart,
        fuelTrend: fuelTrendChart,
        maintenanceCost: maintenanceCostChart,
        utilization: utilizationChart
      }
    },
    enabled: vehicles.length > 0 || fuel.length > 0 || maintenance.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// ==================== REAL-TIME DASHBOARD ====================

export function useDashboardSubscription() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: [...dashboardKeys.all, 'subscription'],
    queryFn: () => {
      // Set up real-time subscriptions for all relevant tables
      const channels = [
        supabase
          .channel('dashboard_vehicles')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'vehicles' }, () => {
            queryClient.invalidateQueries({ queryKey: dashboardKeys.all })
          }),

        supabase
          .channel('dashboard_drivers')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'drivers' }, () => {
            queryClient.invalidateQueries({ queryKey: dashboardKeys.all })
          }),
        
        supabase
          .channel('dashboard_maintenance')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'maintenance_records' }, () => {
            queryClient.invalidateQueries({ queryKey: dashboardKeys.all })
          }),

        supabase
          .channel('dashboard_fuel')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'fuel_records' }, () => {
            queryClient.invalidateQueries({ queryKey: dashboardKeys.all })
          })
      ]

      channels.forEach(channel => channel.subscribe())

      return channels
    },
    staleTime: Infinity,
    gcTime: Infinity,
  })
}
