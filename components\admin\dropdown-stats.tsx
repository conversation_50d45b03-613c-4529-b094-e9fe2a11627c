"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useDropdownValues } from "@/hooks/use-dropdown-values"
import { BarChart3, Database, CheckCircle, XCircle } from "lucide-react"

export function DropdownStats() {
  const { categories, isLoading } = useDropdownValues()

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const totalCategories = categories.length
  const totalValues = categories.reduce((sum, cat) => sum + cat.values.length, 0)
  const activeValues = categories.reduce((sum, cat) => sum + cat.values.filter(v => v.isActive).length, 0)
  const inactiveValues = totalValues - activeValues

  const stats = [
    {
      title: "Total Categories",
      value: totalCategories,
      icon: Database,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "Total Values",
      value: totalValues,
      icon: BarChart3,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    },
    {
      title: "Active Values",
      value: activeValues,
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      title: "Inactive Values",
      value: inactiveValues,
      icon: XCircle,
      color: "text-red-600",
      bgColor: "bg-red-100"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
      {stats.map((stat) => {
        const IconComponent = stat.icon
        return (
          <Card key={stat.title} className="glass-effect hover:shadow-lg transition-all duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <IconComponent className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-800">{stat.value}</div>
              <Badge variant="secondary" className="mt-1 text-xs">
                {stat.title === "Active Values" && totalValues > 0 
                  ? `${Math.round((activeValues / totalValues) * 100)}% active`
                  : stat.title === "Inactive Values" && totalValues > 0
                  ? `${Math.round((inactiveValues / totalValues) * 100)}% inactive`
                  : "System data"
                }
              </Badge>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}