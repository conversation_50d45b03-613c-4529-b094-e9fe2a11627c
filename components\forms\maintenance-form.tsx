"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { LoadingButton } from "@/components/ui/loading-button"


import { supabaseApiClient, type MaintenanceRecord, type Vehicle } from "@/lib/supabase-api-client"

const maintenanceSchema = z.object({
  vehicleId: z.string().min(1, "Vehicle selection is required"),
  type: z.string().min(1, "Maintenance type is required"),
  scheduledDate: z.string().min(1, "Scheduled date is required"),
  cost: z.number().min(0, "Cost must be non-negative"),
  description: z.string().min(1, "Description is required"),
  technician: z.string().min(1, "Technician name is required"),
  status: z.string().optional(),
  nextMaintenanceKm: z.number().optional(),
})

type MaintenanceFormData = z.infer<typeof maintenanceSchema>

interface MaintenanceFormProps {
  maintenance?: MaintenanceRecord
  onSuccess: () => void
  onCancel: () => void
}

export function MaintenanceForm({ maintenance, onSuccess, onCancel }: MaintenanceFormProps) {
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [loadingVehicles, setLoadingVehicles] = useState(true)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<MaintenanceFormData>({
    resolver: zodResolver(maintenanceSchema),
    defaultValues: {
      vehicleId: maintenance?.vehicle_id || "",
      type: maintenance?.maintenance_type || "",
      scheduledDate: maintenance?.scheduled_date || "",
      cost: maintenance?.cost || 0,
      description: maintenance?.description || "",
      technician: maintenance?.technician || "",
      status: maintenance?.maintenance_status || "Scheduled",
      nextMaintenanceKm: maintenance?.next_maintenance_km || 0,
    },
  })

  // const { execute, isLoading } = useAsyncOperation({
  //   successMessage: maintenance ? "Maintenance updated successfully" : "Maintenance scheduled successfully",
  //   onSuccess: () => {
  //     reset()
  //     onSuccess()
  //   },
  // })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        const vehiclesData = await supabaseApiClient.getVehicles()
        setVehicles(vehiclesData.filter((v) => v.vehicle_status === "Active" || v.vehicle_id === maintenance?.vehicle_id))
      } catch (error) {
        console.error("Failed to load vehicles:", error)
      } finally {
        setLoadingVehicles(false)
      }
    }

    loadVehicles()
  }, [maintenance])

  const onSubmit = async (data: MaintenanceFormData) => {
    setIsLoading(true)
    try {
      const maintenanceData = {
        vehicle_id: data.vehicleId,
        maintenance_type: data.type,
        scheduled_date: data.scheduledDate,
        cost: data.cost,
        description: data.description,
        technician: data.technician,
        maintenance_status: data.status || "Scheduled",
        completed_date: maintenance?.completed_date || null,
        next_maintenance_km: maintenance?.next_maintenance_km || 0,
      }

      if (maintenance) {
        await supabaseApiClient.updateMaintenance(maintenance.id, maintenanceData)
      } else {
        await supabaseApiClient.addMaintenance(maintenanceData)
      }

      reset()
      onSuccess()
    } catch (error) {
      console.error('Error saving maintenance record:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="vehicleId" className="text-gray-700">
            Vehicle *
          </Label>
          <Select
            value={watch("vehicleId") || ""}
            onValueChange={(value) => setValue("vehicleId", value)}
            disabled={loadingVehicles}
          >
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder={loadingVehicles ? "Loading vehicles..." : "Select vehicle"} />
            </SelectTrigger>
            <SelectContent>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.vehicle_id} value={vehicle.vehicle_id}>
                  {vehicle.plate_number} - {vehicle.vehicle_type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.vehicleId && <p className="text-red-500 text-sm mt-1">{errors.vehicleId.message}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="type" className="text-gray-700">
              Maintenance Type *
            </Label>
            <Select value={watch("type") || ""} onValueChange={(value) => setValue("type", value)}>
              <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder="Select maintenance type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Oil Change">Oil Change</SelectItem>
                <SelectItem value="Tire Replacement">Tire Replacement</SelectItem>
                <SelectItem value="Brake Service">Brake Service</SelectItem>
                <SelectItem value="Engine Service">Engine Service</SelectItem>
                <SelectItem value="General Inspection">General Inspection</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.type && <p className="text-red-500 text-sm mt-1">{errors.type.message}</p>}
          </div>

          <div>
            <Label htmlFor="status" className="text-gray-700">
              Status
            </Label>
            <Select value={watch("status") || "Scheduled"} onValueChange={(value) => setValue("status", value)}>
              <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Scheduled">Scheduled</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="scheduledDate" className="text-gray-700">
              Scheduled Date *
            </Label>
            <Input
              id="scheduledDate"
              type="date"
              min={new Date().toISOString().split('T')[0]}
              {...register("scheduledDate")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.scheduledDate ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
            />
            {errors.scheduledDate && <p className="text-red-500 text-sm mt-1">{errors.scheduledDate.message}</p>}
          </div>

          <div>
            <Label htmlFor="cost" className="text-gray-700">
              Estimated Cost (EGP) *
            </Label>
            <Input
              id="cost"
              type="number"
              step="0.01"
              min="0"
              {...register("cost", { valueAsNumber: true })}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.cost ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="0.00"
            />
            {errors.cost && <p className="text-red-500 text-sm mt-1">{errors.cost.message}</p>}
          </div>
        </div>

        <div>
          <Label htmlFor="technician" className="text-gray-700">
            Technician *
          </Label>
          <Input
            id="technician"
            placeholder="Enter technician name"
            {...register("technician")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.technician ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
          />
          {errors.technician && <p className="text-red-500 text-sm mt-1">{errors.technician.message}</p>}
        </div>

        <div>
          <Label htmlFor="nextMaintenanceKm" className="text-gray-700">
            Next Maintenance KM
          </Label>
          <Input
            id="nextMaintenanceKm"
            type="number"
            min="0"
            {...register("nextMaintenanceKm", { valueAsNumber: true })}
            className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
            placeholder="e.g., 210000"
          />
        </div>

        <div>
          <Label htmlFor="description" className="text-gray-700">
            Description *
          </Label>
          <Textarea
            id="description"
            {...register("description")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.description ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
            placeholder="Describe the maintenance work to be performed..."
            rows={3}
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>}
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <LoadingButton type="submit" loading={isLoading} className="gradient-bg-primary text-blue-700 hover:opacity-90">
          {maintenance ? "Update Maintenance" : "Schedule Maintenance"}
        </LoadingButton>
      </div>
    </form>
  )
}
