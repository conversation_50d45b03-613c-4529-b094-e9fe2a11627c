import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient } from '@/lib/supabase-api-client'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'

type Vehicle = Database['public']['Tables']['vehicles']['Row']
type VehicleInsert = Database['public']['Tables']['vehicles']['Insert']
type VehicleUpdate = Database['public']['Tables']['vehicles']['Update']

// Query keys
export const vehicleKeys = {
  all: ['vehicles'] as const,
  lists: () => [...vehicleKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...vehicleKeys.lists(), { filters }] as const,
  details: () => [...vehicleKeys.all, 'detail'] as const,
  detail: (id: string) => [...vehicleKeys.details(), id] as const,
}

// ==================== QUERIES ====================

export function useVehicles(filters?: Record<string, any>) {
  return useQuery({
    queryKey: vehicleKeys.list(filters || {}),
    queryFn: () => supabaseApiClient.getVehicles(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useVehicle(id: string) {
  return useQuery({
    queryKey: vehicleKeys.detail(id),
    queryFn: () => supabaseApiClient.getVehicle(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// ==================== MUTATIONS ====================

export function useAddVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (vehicleData: VehicleInsert) => 
      supabaseApiClient.addVehicle(vehicleData),
    
    onSuccess: (newVehicle) => {
      // Invalidate and refetch vehicles list
      queryClient.invalidateQueries({ queryKey: vehicleKeys.lists() })
      
      // Add the new vehicle to the cache
      queryClient.setQueryData(
        vehicleKeys.detail(newVehicle.id),
        newVehicle
      )
      
      toast.success('تم إضافة المركبة بنجاح')
    },
    
    onError: (error) => {
      console.error('Add vehicle error:', error)
      toast.error('فشل في إضافة المركبة')
    }
  })
}

export function useUpdateVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: VehicleUpdate }) =>
      supabaseApiClient.updateVehicle(id, data),
    
    onSuccess: (updatedVehicle) => {
      // Update the specific vehicle in cache
      queryClient.setQueryData(
        vehicleKeys.detail(updatedVehicle.id),
        updatedVehicle
      )
      
      // Invalidate vehicles list to reflect changes
      queryClient.invalidateQueries({ queryKey: vehicleKeys.lists() })
      
      toast.success('تم تحديث المركبة بنجاح')
    },
    
    onError: (error) => {
      console.error('Update vehicle error:', error)
      toast.error('فشل في تحديث المركبة')
    }
  })
}

export function useDeleteVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteVehicle(id),
    
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: vehicleKeys.detail(deletedId) })
      
      // Invalidate vehicles list
      queryClient.invalidateQueries({ queryKey: vehicleKeys.lists() })
      
      toast.success('تم حذف المركبة بنجاح')
    },
    
    onError: (error) => {
      console.error('Delete vehicle error:', error)
      toast.error('فشل في حذف المركبة')
    }
  })
}

// ==================== COMPUTED QUERIES ====================

export function useVehicleStats() {
  const { data: vehicles = [] } = useVehicles()

  return useQuery({
    queryKey: [...vehicleKeys.all, 'stats'],
    queryFn: () => {
      const stats = {
        total: vehicles.length,
        active: vehicles.filter(v => v.vehicle_status === 'Active').length,
        maintenance: vehicles.filter(v => v.vehicle_status === 'Maintenance').length,
        inactive: vehicles.filter(v => v.vehicle_status === 'Inactive').length,
        assigned: vehicles.filter(v => v.assigned_driver_id).length,
        unassigned: vehicles.filter(v => !v.assigned_driver_id).length,
        byType: vehicles.reduce((acc, vehicle) => {
          acc[vehicle.vehicle_type] = (acc[vehicle.vehicle_type] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        byBranch: vehicles.reduce((acc, vehicle) => {
          const branchName = (vehicle as any).branches?.name || 'Unknown'
          acc[branchName] = (acc[branchName] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        averageKm: vehicles.length > 0 
          ? Math.round(vehicles.reduce((sum, v) => sum + v.current_km, 0) / vehicles.length)
          : 0
      }
      
      return stats
    },
    enabled: vehicles.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useVehiclesByBranch(branchId?: string) {
  const { data: vehicles = [] } = useVehicles()

  return useQuery({
    queryKey: [...vehicleKeys.all, 'by-branch', branchId],
    queryFn: () => {
      if (!branchId) return vehicles
      return vehicles.filter(vehicle => vehicle.branch_id === branchId)
    },
    enabled: !!vehicles.length,
    staleTime: 5 * 60 * 1000,
  })
}

export function useAvailableVehicles() {
  const { data: vehicles = [] } = useVehicles()

  return useQuery({
    queryKey: [...vehicleKeys.all, 'available'],
    queryFn: () => {
      return vehicles.filter(vehicle => 
        vehicle.vehicle_status === 'Active' && !vehicle.assigned_driver_id
      )
    },
    enabled: !!vehicles.length,
    staleTime: 2 * 60 * 1000,
  })
}

// ==================== OPTIMISTIC UPDATES ====================

export function useOptimisticVehicleUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: VehicleUpdate }) =>
      supabaseApiClient.updateVehicle(id, data),
    
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: vehicleKeys.detail(id) })
      
      // Snapshot previous value
      const previousVehicle = queryClient.getQueryData(vehicleKeys.detail(id))
      
      // Optimistically update
      if (previousVehicle) {
        queryClient.setQueryData(vehicleKeys.detail(id), {
          ...previousVehicle,
          ...data,
          updated_at: new Date().toISOString()
        })
      }
      
      return { previousVehicle }
    },
    
    onError: (error, { id }, context) => {
      // Rollback on error
      if (context?.previousVehicle) {
        queryClient.setQueryData(vehicleKeys.detail(id), context.previousVehicle)
      }
      toast.error('فشل في تحديث المركبة')
    },
    
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: vehicleKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: vehicleKeys.lists() })
    }
  })
}

// ==================== BULK OPERATIONS ====================

export function useBulkUpdateVehicles() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: VehicleUpdate }>) => {
      const results = await Promise.allSettled(
        updates.map(({ id, data }) => supabaseApiClient.updateVehicle(id, data))
      )
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      
      return { successful, failed, total: updates.length }
    },
    
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: vehicleKeys.all })
      
      if (result.failed > 0) {
        toast.warning(`تم تحديث ${result.successful} مركبة، فشل في ${result.failed}`)
      } else {
        toast.success(`تم تحديث ${result.successful} مركبة بنجاح`)
      }
    },
    
    onError: () => {
      toast.error('فشل في التحديث المجمع للمركبات')
    }
  })
}

// ==================== REAL-TIME SUBSCRIPTIONS ====================

export function useVehicleSubscription() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: [...vehicleKeys.all, 'subscription'],
    queryFn: () => {
      // Set up real-time subscription
      const subscription = supabase
        .channel('vehicles_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'vehicles'
          },
          (payload: any) => {
            console.log('Vehicle change detected:', payload)
            
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: vehicleKeys.all })
            
            // Show notification for real-time updates
            if (payload.eventType === 'INSERT') {
              toast.info('تم إضافة مركبة جديدة')
            } else if (payload.eventType === 'UPDATE') {
              toast.info('تم تحديث مركبة')
            } else if (payload.eventType === 'DELETE') {
              toast.info('تم حذف مركبة')
            }
          }
        )
        .subscribe()

      return subscription
    },
    staleTime: Infinity, // Never stale
    gcTime: Infinity, // Never garbage collect
  })
}
