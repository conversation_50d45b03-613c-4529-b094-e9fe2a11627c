"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingButton } from "@/components/ui/loading-button"
// import { useAsyncOperation } from "@/hooks/use-async-operation"
import { supabaseApiClient, type Driver, type Vehicle } from "@/lib/supabase-api-client"

const driverSchema = z.object({
  name: z.string().min(1, "Name is required"),
  licenseNumber: z.coerce.number().min(1, "License number is required"),
  licenseExpiry: z.string().min(1, "License expiry date is required"),
  phone: z.string().min(1, "Phone number is required"),
  email: z.string().email("Invalid email address"),
  assignedVehicleId: z.string().optional(),
})

type DriverFormData = {
  name: string;
  licenseNumber: number;
  licenseExpiry: string;
  phone: string;
  email: string;
  assignedVehicleId?: string;
}

interface DriverFormProps {
  driver?: Driver
  onSuccess: () => void
  onCancel: () => void
}

export function DriverForm({ driver, onSuccess, onCancel }: DriverFormProps) {
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [loadingVehicles, setLoadingVehicles] = useState(true)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<DriverFormData>({
    resolver: zodResolver(driverSchema),
    defaultValues: {
      name: driver?.full_name || "",
      licenseNumber: driver?.license_number || 0,
      licenseExpiry: driver?.license_expiry || "",
      phone: driver?.phone || "",
      email: driver?.email || "",
      assignedVehicleId: driver?.assigned_vehicle_id || "",
    },
  })

  // const { execute, isLoading } = useAsyncOperation({
  //   successMessage: driver ? "Driver updated successfully" : "Driver added successfully",
  //   onSuccess: () => {
  //     reset()
  //     onSuccess()
  //   },
  // })
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const loadVehicles = async () => {
      try {
        const vehiclesData = await supabaseApiClient.getVehicles()
        setVehicles(vehiclesData.filter((v) => v.vehicle_status === "Active" && !v.assigned_driver_code))
      } catch (error) {
        console.error("Failed to load vehicles:", error)
      } finally {
        setLoadingVehicles(false)
      }
    }

    loadVehicles()
  }, [])

  const onSubmit = async (data: DriverFormData) => {
    setIsLoading(true)
    try {
      const driverData = {
        full_name: data.name,
        license_number: data.licenseNumber,
        license_expiry: data.licenseExpiry,
        phone: data.phone,
        email: data.email,
        assigned_vehicle_id: data.assignedVehicleId || "",
        status: data.assignedVehicleId ? "Active" : "Available",
        hire_date: driver?.hire_date || new Date().toISOString().split("T")[0],
      }

      if (driver) {
        await supabaseApiClient.updateDriver(driver.id, driverData)
      } else {
        await supabaseApiClient.addDriver(driverData)
      }

      reset()
      onSuccess()
    } catch (error) {
      console.error('Error saving driver:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid gap-4">
        <div>
          <Label htmlFor="name" className="text-gray-700">
            Full Name *
          </Label>
          <Input
            id="name"
            autoComplete="name"
            {...register("name")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.name ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
            }`}
            placeholder="Enter full name"
          />
          {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="licenseNumber" className="text-gray-700">
              License Number *
            </Label>
            <Input
              id="licenseNumber"
              autoComplete="off"
              {...register("licenseNumber")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.licenseNumber ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="License number"
            />
            {errors.licenseNumber && <p className="text-red-500 text-sm mt-1">{errors.licenseNumber.message}</p>}
          </div>

          <div>
            <Label htmlFor="licenseExpiry" className="text-gray-700">
              License Expiry *
            </Label>
            <Input
              id="licenseExpiry"
              type="date"
              min={new Date().toISOString().split('T')[0]}
              {...register("licenseExpiry")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.licenseExpiry ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
            />
            {errors.licenseExpiry && <p className="text-red-500 text-sm mt-1">{errors.licenseExpiry.message}</p>}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="phone" className="text-gray-700">
              Phone Number *
            </Label>
            <Input
              id="phone"
              type="tel"
              autoComplete="tel"
              {...register("phone")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.phone ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="+20-************"
            />
            {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>}
          </div>

          <div>
            <Label htmlFor="email" className="text-gray-700">
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              autoComplete="email"
              {...register("email")}
              className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
                errors.email ? "border-red-300 focus:border-red-400 focus:ring-red-400" : ""
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
          </div>
        </div>

        <div>
          <Label htmlFor="assignedVehicleId" className="text-gray-700">
            Assign Vehicle (Optional)
          </Label>
          <Select
            value={watch("assignedVehicleId") || "none"}
            onValueChange={(value) => setValue("assignedVehicleId", value)}
            disabled={loadingVehicles}
          >
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder={loadingVehicles ? "Loading vehicles..." : "Select a vehicle"} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No assignment</SelectItem>
              {vehicles.map((vehicle) => (
                <SelectItem key={vehicle.vehicle_id} value={vehicle.vehicle_id}>
                  {vehicle.plate_number} - {vehicle.vehicle_type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
        <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <LoadingButton type="submit" loading={isLoading} className="gradient-bg-primary text-blue-700 hover:opacity-90">
          {driver ? "Update Driver" : "Add Driver"}
        </LoadingButton>
      </div>
    </form>
  )
}
