import { supabase } from './supabase'
import { cachePerformanceMonitor } from './cache-manager'

// Query optimization strategies
export interface QueryOptimization {
  useIndexes: boolean
  limitResults: boolean
  selectSpecificFields: boolean
  useJoins: boolean
  enableExplain: boolean
}

// Performance monitoring
export interface QueryPerformance {
  queryKey: string
  duration: number
  resultCount: number
  cacheHit: boolean
  timestamp: number
}

// Query builder with optimization
export class OptimizedQueryBuilder {
  private tableName: string
  private selectFields: string[] = ['*']
  private joinClauses: string[] = []
  private whereConditions: string[] = []
  private orderByClause: string = ''
  private limitValue: number | null = null
  private offsetValue: number | null = null
  private optimization: QueryOptimization

  constructor(table: string, optimization: QueryOptimization = {
    useIndexes: true,
    limitResults: true,
    selectSpecificFields: true,
    useJoins: true,
    enableExplain: false
  }) {
    this.tableName = table
    this.optimization = optimization
  }

  // Select specific fields for better performance
  select(fields: string | string[]): this {
    if (this.optimization.selectSpecificFields) {
      this.selectFields = Array.isArray(fields) ? fields : [fields]
    }
    return this
  }

  // Add joins with optimization
  join(table: string, condition: string, type: 'INNER' | 'LEFT' | 'RIGHT' = 'LEFT'): this {
    if (this.optimization.useJoins) {
      this.joinClauses.push(`${type} JOIN ${table} ON ${condition}`)
    }
    return this
  }

  // Add where conditions with index hints
  where(condition: string): this {
    this.whereConditions.push(condition)
    return this
  }

  // Add order by with index consideration
  orderBy(field: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    this.orderByClause = `ORDER BY ${field} ${direction}`
    return this
  }

  // Limit results for performance
  limit(count: number): this {
    if (this.optimization.limitResults) {
      this.limitValue = count
    }
    return this
  }

  // Add offset for pagination
  offset(count: number): this {
    this.offsetValue = count
    return this
  }

  // Build and execute optimized query
  async execute<T = any>(): Promise<T[]> {
    const startTime = performance.now()
    
    try {
      let query: any = supabase.from(this.tableName)

      // Apply select fields
      if (this.selectFields.length > 0 && this.selectFields[0] !== '*') {
        query = query.select(this.selectFields.join(', '))
      } else {
        query = query.select('*')
      }

      // Apply where conditions
      this.whereConditions.forEach(condition => {
        // Parse condition and apply appropriate filter
        const [field, operator, value] = this.parseCondition(condition)
        query = this.applyFilter(query, field, operator, value)
      })

      // Apply ordering
      if (this.orderByClause) {
        const [field, direction] = this.parseOrderBy(this.orderByClause)
        query = query.order(field, { ascending: direction === 'ASC' })
      }

      // Apply pagination
      if (this.limitValue !== null) {
        query = query.limit(this.limitValue)
      }
      
      if (this.offsetValue !== null) {
        query = query.range(this.offsetValue, this.offsetValue + (this.limitValue || 1000) - 1)
      }

      const { data, error } = await query

      if (error) throw error

      const endTime = performance.now()
      const duration = endTime - startTime

      // Record performance metrics
      cachePerformanceMonitor.recordQueryTime(
        `${this.tableName}_optimized`,
        duration
      )

      console.log(`Optimized query for ${this.tableName}: ${duration.toFixed(2)}ms, ${data?.length || 0} results`)

      return data || []
    } catch (error) {
      console.error(`Optimized query failed for ${this.tableName}:`, error)
      throw error
    }
  }

  // Parse condition string
  private parseCondition(condition: string): [string, string, any] {
    // Simple parser for conditions like "field = value" or "field > value"
    const operators = ['>=', '<=', '!=', '=', '>', '<', 'LIKE', 'IN']
    
    for (const op of operators) {
      if (condition.includes(op)) {
        const [field, value] = condition.split(op).map(s => s.trim())
        return [field, op, value]
      }
    }
    
    throw new Error(`Invalid condition: ${condition}`)
  }

  // Apply filter based on operator
  private applyFilter(query: any, field: string, operator: string, value: any) {
    switch (operator) {
      case '=':
        return query.eq(field, value)
      case '!=':
        return query.neq(field, value)
      case '>':
        return query.gt(field, value)
      case '>=':
        return query.gte(field, value)
      case '<':
        return query.lt(field, value)
      case '<=':
        return query.lte(field, value)
      case 'LIKE':
        return query.like(field, value)
      case 'IN':
        return query.in(field, JSON.parse(value))
      default:
        return query
    }
  }

  // Parse order by clause
  private parseOrderBy(orderBy: string): [string, string] {
    const parts = orderBy.replace('ORDER BY ', '').split(' ')
    return [parts[0], parts[1] || 'ASC']
  }
}

// Optimized query functions for common operations
export class FleetQueryOptimizer {
  // Get vehicles with optimized joins
  static async getVehiclesOptimized(filters?: {
    branchId?: string
    status?: string
    limit?: number
  }) {
    const query = new OptimizedQueryBuilder('vehicles')
      .select([
        'id',
        'plate_number', 
        'model',
        'vehicle_type',
        'service_type',
        'vehicle_status',
        'current_km',
        'branch_id',
        'assigned_driver_id',
        'branches(id, name)',
        'drivers(id, full_name)'
      ])

    if (filters?.branchId) {
      query.where(`branch_id = ${filters.branchId}`)
    }

    if (filters?.status) {
      query.where(`vehicle_status = ${filters.status}`)
    }

    if (filters?.limit) {
      query.limit(filters.limit)
    }

    return query.orderBy('created_at', 'DESC').execute()
  }

  // Get drivers with license expiry optimization
  static async getDriversWithExpiryOptimized(days: number = 30) {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + days)

    return new OptimizedQueryBuilder('drivers')
      .select([
        'id',
        'full_name',
        'license_number',
        'license_expiry',
        'phone',
        'status',
        'branch_id',
        'branches(id, name)'
      ])
      .where(`license_expiry <= ${futureDate.toISOString().split('T')[0]}`)
      .where(`license_expiry > ${new Date().toISOString().split('T')[0]}`)
      .orderBy('license_expiry', 'ASC')
      .execute()
  }

  // Get maintenance records with vehicle info
  static async getMaintenanceOptimized(filters?: {
    vehicleId?: string
    status?: string
    upcoming?: boolean
    limit?: number
  }) {
    const query = new OptimizedQueryBuilder('maintenance_records')
      .select([
        'id',
        'vehicle_id',
        'maintenance_type',
        'maintenance_status',
        'scheduled_date',
        'completed_date',
        'cost',
        'description',
        'vehicles(id, plate_number, vehicle_type, branches(name))'
      ])

    if (filters?.vehicleId) {
      query.where(`vehicle_id = ${filters.vehicleId}`)
    }

    if (filters?.status) {
      query.where(`maintenance_status = ${filters.status}`)
    }

    if (filters?.upcoming) {
      const oneWeekFromNow = new Date()
      oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7)
      query.where(`scheduled_date <= ${oneWeekFromNow.toISOString().split('T')[0]}`)
      query.where(`scheduled_date >= ${new Date().toISOString().split('T')[0]}`)
      query.where(`maintenance_status = Scheduled`)
    }

    if (filters?.limit) {
      query.limit(filters.limit)
    }

    return query.orderBy('scheduled_date', 'DESC').execute()
  }

  // Get fuel records with efficiency calculation
  static async getFuelOptimized(filters?: {
    vehicleId?: string
    dateRange?: { start: string; end: string }
    limit?: number
  }) {
    const query = new OptimizedQueryBuilder('fuel_records')
      .select([
        'id',
        'vehicle_id',
        'date',
        'quantity_liters',
        'distance_km',
        'cost',
        'station',
        'consumption_per_100km',
        'vehicles(id, plate_number, vehicle_type, branches(name))'
      ])

    if (filters?.vehicleId) {
      query.where(`vehicle_id = ${filters.vehicleId}`)
    }

    if (filters?.dateRange) {
      query.where(`date >= ${filters.dateRange.start}`)
      query.where(`date <= ${filters.dateRange.end}`)
    }

    if (filters?.limit) {
      query.limit(filters.limit)
    }

    return query.orderBy('date', 'DESC').execute()
  }

  // Get dashboard data optimized
  static async getDashboardDataOptimized() {
    // Use the optimized dashboard view
    return new OptimizedQueryBuilder('dashboard_view')
      .select([
        'vehicle_id',
        'plate_number',
        'model',
        'vehicle_type',
        'vehicle_status',
        'branch_name',
        'assigned_driver',
        'license_days_remaining',
        'total_fuel_cost',
        'total_maintenance_cost'
      ])
      .limit(100) // Limit for dashboard performance
      .execute()
  }
}

// Query performance analyzer
export class QueryPerformanceAnalyzer {
  private static instance: QueryPerformanceAnalyzer
  private performanceLog: QueryPerformance[] = []

  static getInstance(): QueryPerformanceAnalyzer {
    if (!QueryPerformanceAnalyzer.instance) {
      QueryPerformanceAnalyzer.instance = new QueryPerformanceAnalyzer()
    }
    return QueryPerformanceAnalyzer.instance
  }

  logQuery(performance: QueryPerformance) {
    this.performanceLog.push(performance)
    
    // Keep only last 1000 entries
    if (this.performanceLog.length > 1000) {
      this.performanceLog.shift()
    }

    // Log slow queries
    if (performance.duration > 1000) {
      console.warn(`Slow query detected: ${performance.queryKey} took ${performance.duration}ms`)
    }
  }

  getSlowQueries(threshold: number = 1000): QueryPerformance[] {
    return this.performanceLog.filter(p => p.duration > threshold)
  }

  getAverageQueryTime(queryKey: string): number {
    const queries = this.performanceLog.filter(p => p.queryKey === queryKey)
    if (queries.length === 0) return 0
    
    return queries.reduce((sum, q) => sum + q.duration, 0) / queries.length
  }

  getPerformanceReport() {
    const report = {
      totalQueries: this.performanceLog.length,
      averageTime: this.performanceLog.reduce((sum, q) => sum + q.duration, 0) / this.performanceLog.length,
      slowQueries: this.getSlowQueries().length,
      cacheHitRate: this.performanceLog.filter(p => p.cacheHit).length / this.performanceLog.length,
      byQueryType: {} as Record<string, { count: number; averageTime: number }>
    }

    // Group by query type
    const queryTypes = [...new Set(this.performanceLog.map(p => p.queryKey))]
    queryTypes.forEach(type => {
      const queries = this.performanceLog.filter(p => p.queryKey === type)
      report.byQueryType[type] = {
        count: queries.length,
        averageTime: queries.reduce((sum, q) => sum + q.duration, 0) / queries.length
      }
    })

    return report
  }
}

// Export singleton
export const queryPerformanceAnalyzer = QueryPerformanceAnalyzer.getInstance()

// Database index recommendations
export const INDEX_RECOMMENDATIONS = {
  vehicles: [
    'CREATE INDEX IF NOT EXISTS idx_vehicles_branch_id ON vehicles(branch_id);',
    'CREATE INDEX IF NOT EXISTS idx_vehicles_status ON vehicles(vehicle_status);',
    'CREATE INDEX IF NOT EXISTS idx_vehicles_assigned_driver ON vehicles(assigned_driver_id);',
    'CREATE INDEX IF NOT EXISTS idx_vehicles_plate_number ON vehicles(plate_number);'
  ],
  drivers: [
    'CREATE INDEX IF NOT EXISTS idx_drivers_branch_id ON drivers(branch_id);',
    'CREATE INDEX IF NOT EXISTS idx_drivers_status ON drivers(status);',
    'CREATE INDEX IF NOT EXISTS idx_drivers_license_expiry ON drivers(license_expiry);',
    'CREATE INDEX IF NOT EXISTS idx_drivers_license_number ON drivers(license_number);'
  ],
  maintenance_records: [
    'CREATE INDEX IF NOT EXISTS idx_maintenance_vehicle_id ON maintenance_records(vehicle_id);',
    'CREATE INDEX IF NOT EXISTS idx_maintenance_scheduled_date ON maintenance_records(scheduled_date);',
    'CREATE INDEX IF NOT EXISTS idx_maintenance_status ON maintenance_records(maintenance_status);',
    'CREATE INDEX IF NOT EXISTS idx_maintenance_type ON maintenance_records(maintenance_type);'
  ],
  fuel_records: [
    'CREATE INDEX IF NOT EXISTS idx_fuel_vehicle_id ON fuel_records(vehicle_id);',
    'CREATE INDEX IF NOT EXISTS idx_fuel_date ON fuel_records(date);',
    'CREATE INDEX IF NOT EXISTS idx_fuel_station ON fuel_records(station);'
  ]
}
