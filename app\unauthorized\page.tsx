"use client"

import { Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, ArrowLeft, Home, Shield, User, UserX } from 'lucide-react'
import { useSupabaseAuth } from '@/context/supabase-auth-context'

function UnauthorizedContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { user, logout } = useSupabaseAuth()

  const reason = searchParams.get('reason') || 'unknown'

  const getReasonDetails = (reason: string) => {
    switch (reason) {
      case 'inactive':
        return {
          title: 'حساب غير نشط',
          description: 'تم إلغاء تفعيل حسابك. يرجى التواصل مع المدير لإعادة تفعيل الحساب.',
          icon: UserX,
          color: 'text-red-600',
          bgColor: 'bg-red-50'
        }
      case 'insufficient_role':
        return {
          title: 'صلاحيات غير كافية',
          description: 'دورك الحالي لا يسمح بالوصول إلى هذه الصفحة. يرجى التواصل مع المدير لترقية صلاحياتك.',
          icon: Shield,
          color: 'text-orange-600',
          bgColor: 'bg-orange-50'
        }
      case 'insufficient_permission':
        return {
          title: 'ليس لديك إذن للوصول',
          description: 'لا تملك الصلاحية المطلوبة للوصول إلى هذه الميزة.',
          icon: User,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50'
        }
      default:
        return {
          title: 'وصول غير مصرح',
          description: 'ليس لديك إذن للوصول إلى هذه الصفحة.',
          icon: AlertTriangle,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50'
        }
    }
  }

  const reasonDetails = getReasonDetails(reason)
  const IconComponent = reasonDetails.icon

  const handleGoBack = () => {
    router.back()
  }

  const handleGoHome = () => {
    router.push('/dashboard')
  }

  const handleLogout = async () => {
    await logout()
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className={`mx-auto w-16 h-16 ${reasonDetails.bgColor} rounded-full flex items-center justify-center mb-4`}>
            <IconComponent className={`w-8 h-8 ${reasonDetails.color}`} />
          </div>
          <CardTitle className="text-xl font-bold text-gray-900">
            {reasonDetails.title}
          </CardTitle>
          <CardDescription className="text-gray-600 text-center">
            {reasonDetails.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {user && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-medium text-blue-900 mb-2">معلومات المستخدم الحالي:</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>الاسم:</strong> {user.full_name}</p>
                <p><strong>البريد الإلكتروني:</strong> {user.email}</p>
                <p><strong>الدور:</strong> {user.role}</p>
                <p><strong>الحالة:</strong> {user.user_status}</p>
              </div>
            </div>
          )}
          
          <div className="space-y-3">
            <Button
              onClick={handleGoBack}
              variant="outline"
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              العودة للصفحة السابقة
            </Button>
            
            <Button
              onClick={handleGoHome}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              <Home className="mr-2 h-4 w-4" />
              الذهاب للصفحة الرئيسية
            </Button>
            
            {reason === 'inactive' && (
              <Button
                onClick={handleLogout}
                variant="destructive"
                className="w-full"
              >
                <UserX className="mr-2 h-4 w-4" />
                تسجيل الخروج
              </Button>
            )}
          </div>
          
          <div className="text-center text-sm text-gray-500 mt-6">
            إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function UnauthorizedPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <UnauthorizedContent />
    </Suspense>
  )
}
