import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient } from '@/lib/supabase-api-client'
import { supabase } from '@/lib/supabase'
import { Database } from '@/lib/supabase'
import { toast } from 'sonner'

type Driver = Database['public']['Tables']['drivers']['Row']
type DriverInsert = Database['public']['Tables']['drivers']['Insert']
type DriverUpdate = Database['public']['Tables']['drivers']['Update']

// Query keys
export const driverKeys = {
  all: ['drivers'] as const,
  lists: () => [...driverKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...driverKeys.lists(), { filters }] as const,
  details: () => [...driverKeys.all, 'detail'] as const,
  detail: (id: string) => [...driverKeys.details(), id] as const,
}

// ==================== QUERIES ====================

export function useDrivers(filters?: Record<string, any>) {
  return useQuery({
    queryKey: driverKeys.list(filters || {}),
    queryFn: () => supabaseApiClient.getDrivers(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useDriver(id: string) {
  return useQuery({
    queryKey: driverKeys.detail(id),
    queryFn: () => supabaseApiClient.getDriver(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  })
}

// ==================== MUTATIONS ====================

export function useAddDriver() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (driverData: DriverInsert) => 
      supabaseApiClient.addDriver(driverData),
    
    onSuccess: (newDriver) => {
      // Invalidate and refetch drivers list
      queryClient.invalidateQueries({ queryKey: driverKeys.lists() })
      
      // Add the new driver to the cache
      queryClient.setQueryData(
        driverKeys.detail(newDriver.id),
        newDriver
      )
      
      toast.success('تم إضافة السائق بنجاح')
    },
    
    onError: (error) => {
      console.error('Add driver error:', error)
      toast.error('فشل في إضافة السائق')
    }
  })
}

export function useUpdateDriver() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DriverUpdate }) =>
      supabaseApiClient.updateDriver(id, data),
    
    onSuccess: (updatedDriver) => {
      // Update the specific driver in cache
      queryClient.setQueryData(
        driverKeys.detail(updatedDriver.id),
        updatedDriver
      )
      
      // Invalidate drivers list to reflect changes
      queryClient.invalidateQueries({ queryKey: driverKeys.lists() })
      
      toast.success('تم تحديث السائق بنجاح')
    },
    
    onError: (error) => {
      console.error('Update driver error:', error)
      toast.error('فشل في تحديث السائق')
    }
  })
}

export function useDeleteDriver() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteDriver(id),
    
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: driverKeys.detail(deletedId) })
      
      // Invalidate drivers list
      queryClient.invalidateQueries({ queryKey: driverKeys.lists() })
      
      toast.success('تم حذف السائق بنجاح')
    },
    
    onError: (error) => {
      console.error('Delete driver error:', error)
      toast.error('فشل في حذف السائق')
    }
  })
}

// ==================== COMPUTED QUERIES ====================

export function useDriverStats() {
  const { data: drivers = [] } = useDrivers()

  return useQuery({
    queryKey: [...driverKeys.all, 'stats'],
    queryFn: () => {
      const now = new Date()
      const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      
      const stats = {
        total: drivers.length,
        active: drivers.filter(d => d.status === 'Active').length,
        available: drivers.filter(d => d.status === 'Available').length,
        inactive: drivers.filter(d => d.status === 'Inactive').length,
        onLeave: drivers.filter(d => d.status === 'On Leave').length,
        expiringSoon: drivers.filter(d => {
          const expiryDate = new Date(d.license_expiry)
          return expiryDate <= oneMonthFromNow && expiryDate > now
        }).length,
        expired: drivers.filter(d => {
          const expiryDate = new Date(d.license_expiry)
          return expiryDate <= now
        }).length,
        byBranch: drivers.reduce((acc, driver) => {
          const branchName = (driver as any).branches?.name || 'Unknown'
          acc[branchName] = (acc[branchName] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        averageExperience: drivers.length > 0 
          ? Math.round(drivers.reduce((sum, d) => {
              const hireDate = new Date(d.hire_date)
              const years = (now.getTime() - hireDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000)
              return sum + years
            }, 0) / drivers.length * 10) / 10
          : 0
      }
      
      return stats
    },
    enabled: drivers.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useDriversByBranch(branchId?: string) {
  const { data: drivers = [] } = useDrivers()

  return useQuery({
    queryKey: [...driverKeys.all, 'by-branch', branchId],
    queryFn: () => {
      if (!branchId) return drivers
      return drivers.filter(driver => driver.branch_id === branchId)
    },
    enabled: !!drivers.length,
    staleTime: 5 * 60 * 1000,
  })
}

export function useAvailableDrivers() {
  const { data: drivers = [] } = useDrivers()

  return useQuery({
    queryKey: [...driverKeys.all, 'available'],
    queryFn: () => {
      return drivers.filter(driver => 
        driver.status === 'Available' && 
        new Date(driver.license_expiry) > new Date()
      )
    },
    enabled: !!drivers.length,
    staleTime: 2 * 60 * 1000,
  })
}

export function useDriversWithExpiringLicenses(days: number = 30) {
  const { data: drivers = [] } = useDrivers()

  return useQuery({
    queryKey: [...driverKeys.all, 'expiring-licenses', days],
    queryFn: () => {
      const now = new Date()
      const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
      
      return drivers.filter(driver => {
        const expiryDate = new Date(driver.license_expiry)
        return expiryDate <= futureDate && expiryDate > now
      }).sort((a, b) => 
        new Date(a.license_expiry).getTime() - new Date(b.license_expiry).getTime()
      )
    },
    enabled: !!drivers.length,
    staleTime: 1 * 60 * 1000, // 1 minute for critical data
  })
}

// ==================== OPTIMISTIC UPDATES ====================

export function useOptimisticDriverUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: DriverUpdate }) =>
      supabaseApiClient.updateDriver(id, data),
    
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: driverKeys.detail(id) })
      
      // Snapshot previous value
      const previousDriver = queryClient.getQueryData(driverKeys.detail(id))
      
      // Optimistically update
      if (previousDriver) {
        queryClient.setQueryData(driverKeys.detail(id), {
          ...previousDriver,
          ...data,
          updated_at: new Date().toISOString()
        })
      }
      
      return { previousDriver }
    },
    
    onError: (error, { id }, context) => {
      // Rollback on error
      if (context?.previousDriver) {
        queryClient.setQueryData(driverKeys.detail(id), context.previousDriver)
      }
      toast.error('فشل في تحديث السائق')
    },
    
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: driverKeys.detail(id) })
      queryClient.invalidateQueries({ queryKey: driverKeys.lists() })
    }
  })
}

// ==================== BULK OPERATIONS ====================

export function useBulkUpdateDrivers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: DriverUpdate }>) => {
      const results = await Promise.allSettled(
        updates.map(({ id, data }) => supabaseApiClient.updateDriver(id, data))
      )
      
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length
      
      return { successful, failed, total: updates.length }
    },
    
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: driverKeys.all })
      
      if (result.failed > 0) {
        toast.warning(`تم تحديث ${result.successful} سائق، فشل في ${result.failed}`)
      } else {
        toast.success(`تم تحديث ${result.successful} سائق بنجاح`)
      }
    },
    
    onError: () => {
      toast.error('فشل في التحديث المجمع للسائقين')
    }
  })
}

// ==================== ASSIGNMENT OPERATIONS ====================

export function useAssignDriverToVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ driverId, vehicleId }: { driverId: string; vehicleId: string }) => {
      // Update driver status to Active
      const driverUpdate = supabaseApiClient.updateDriver(driverId, { 
        status: 'Active' 
      })
      
      // Update vehicle to assign driver
      const vehicleUpdate = supabaseApiClient.updateVehicle(vehicleId, { 
        assigned_driver_id: driverId 
      })
      
      const [driver, vehicle] = await Promise.all([driverUpdate, vehicleUpdate])
      return { driver, vehicle }
    },
    
    onSuccess: ({ driver, vehicle }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: driverKeys.all })
      queryClient.invalidateQueries({ queryKey: ['vehicles'] })
      
      toast.success(`تم تعيين السائق ${driver.full_name} للمركبة ${vehicle.plate_number}`)
    },
    
    onError: (error) => {
      console.error('Assign driver error:', error)
      toast.error('فشل في تعيين السائق للمركبة')
    }
  })
}

export function useUnassignDriverFromVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ driverId, vehicleId }: { driverId: string; vehicleId: string }) => {
      // Update driver status to Available
      const driverUpdate = supabaseApiClient.updateDriver(driverId, { 
        status: 'Available' 
      })
      
      // Remove driver assignment from vehicle
      const vehicleUpdate = supabaseApiClient.updateVehicle(vehicleId, { 
        assigned_driver_id: null 
      })
      
      const [driver, vehicle] = await Promise.all([driverUpdate, vehicleUpdate])
      return { driver, vehicle }
    },
    
    onSuccess: ({ driver, vehicle }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: driverKeys.all })
      queryClient.invalidateQueries({ queryKey: ['vehicles'] })
      
      toast.success(`تم إلغاء تعيين السائق ${driver.full_name} من المركبة ${vehicle.plate_number}`)
    },
    
    onError: (error) => {
      console.error('Unassign driver error:', error)
      toast.error('فشل في إلغاء تعيين السائق')
    }
  })
}

// ==================== REAL-TIME SUBSCRIPTIONS ====================

export function useDriverSubscription() {
  const queryClient = useQueryClient()

  return useQuery({
    queryKey: [...driverKeys.all, 'subscription'],
    queryFn: () => {
      // Set up real-time subscription
      const subscription = supabase
        .channel('drivers_changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'drivers'
          },
          (payload: any) => {
            console.log('Driver change detected:', payload)
            
            // Invalidate relevant queries
            queryClient.invalidateQueries({ queryKey: driverKeys.all })
            
            // Show notification for real-time updates
            if (payload.eventType === 'INSERT') {
              toast.info('تم إضافة سائق جديد')
            } else if (payload.eventType === 'UPDATE') {
              toast.info('تم تحديث بيانات سائق')
            } else if (payload.eventType === 'DELETE') {
              toast.info('تم حذف سائق')
            }
          }
        )
        .subscribe()

      return subscription
    },
    staleTime: Infinity, // Never stale
    gcTime: Infinity, // Never garbage collect
  })
}
