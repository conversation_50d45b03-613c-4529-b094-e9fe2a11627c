"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, X } from "lucide-react"

interface DropdownSearchProps {
  onSearch: (query: string) => void
  onFilterCategory: (category: string | null) => void
  onFilterStatus: (status: string | null) => void
  searchQuery: string
  selectedCategory: string | null
  selectedStatus: string | null
  totalResults: number
}

const DROPDOWN_CATEGORIES = [
  { id: "vehicle_type", name: "Vehicle Type" },
  { id: "service_type", name: "Service Type" },
  { id: "fuel_type", name: "Fuel Type" },
  { id: "color", name: "Color" },
  { id: "department", name: "Department" },
  { id: "status", name: "Status" }
]

export function DropdownSearch({
  onSearch,
  onFilterCategory,
  onFilterStatus,
  searchQuery,
  selectedCategory,
  selectedStatus,
  totalResults
}: DropdownSearchProps) {
  const [localQuery, setLocalQuery] = useState(searchQuery)

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(localQuery)
  }

  const clearFilters = () => {
    setLocalQuery("")
    onSearch("")
    onFilterCategory(null)
    onFilterStatus(null)
  }

  const hasActiveFilters = searchQuery || selectedCategory || selectedStatus

  return (
    <div className="glass-effect rounded-xl p-6 mb-6">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Input */}
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search dropdown values..."
              value={localQuery}
              onChange={(e) => setLocalQuery(e.target.value)}
              className="pl-10 pr-4"
            />
          </div>
        </form>

        {/* Category Filter */}
        <Select value={selectedCategory || "all"} onValueChange={(value) => onFilterCategory(value === "all" ? null : value)}>
          <SelectTrigger className="w-full lg:w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {DROPDOWN_CATEGORIES.map((cat) => (
              <SelectItem key={cat.id} value={cat.id}>
                {cat.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select value={selectedStatus || "all"} onValueChange={(value) => onFilterStatus(value === "all" ? null : value)}>
          <SelectTrigger className="w-full lg:w-32">
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={clearFilters}
            className="flex items-center gap-2 whitespace-nowrap"
          >
            <X className="h-4 w-4" />
            Clear
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-white/20">
          <span className="text-sm text-gray-600">Active filters:</span>
          
          {searchQuery && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{searchQuery}"
              <button
                onClick={() => {
                  setLocalQuery("")
                  onSearch("")
                }}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {selectedCategory && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Category: {DROPDOWN_CATEGORIES.find(c => c.id === selectedCategory)?.name}
              <button
                onClick={() => onFilterCategory(null)}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {selectedStatus && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {selectedStatus === "active" ? "Active" : "Inactive"}
              <button
                onClick={() => onFilterStatus(null)}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          <Badge variant="outline" className="text-xs">
            {totalResults} result{totalResults !== 1 ? 's' : ''}
          </Badge>
        </div>
      )}
    </div>
  )
}