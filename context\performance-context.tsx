"use client"

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { realtimeManager, RealtimeEvent } from '@/lib/realtime-manager'
import { CacheInvalidationManager, CacheOptimizer, cacheKeys } from '@/lib/cache-manager'
import { queryPerformanceAnalyzer } from '@/lib/query-optimizer'
import { toast } from 'sonner'

// Performance metrics interface
interface PerformanceMetrics {
  queryCount: number
  averageQueryTime: number
  cacheHitRate: number
  slowQueries: number
  realtimeConnected: boolean
  activeSubscriptions: number
  memoryUsage: number
}

// Performance context interface
interface PerformanceContextType {
  metrics: PerformanceMetrics
  isRealtimeEnabled: boolean
  toggleRealtime: () => void
  optimizeCache: () => void
  getPerformanceReport: () => any
  subscribeToTable: (table: string, callback?: (event: RealtimeEvent) => void) => string
  unsubscribeFromTable: (subscriptionId: string) => void
  refreshMetrics: () => void
}

const PerformanceContext = createContext<PerformanceContextType | undefined>(undefined)

export function PerformanceProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient()
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    queryCount: 0,
    averageQueryTime: 0,
    cacheHitRate: 0,
    slowQueries: 0,
    realtimeConnected: false,
    activeSubscriptions: 0,
    memoryUsage: 0
  })
  const [isRealtimeEnabled, setIsRealtimeEnabled] = useState(true)
  const [cacheInvalidationManager] = useState(() => new CacheInvalidationManager(queryClient))
  const [cacheOptimizer] = useState(() => new CacheOptimizer(queryClient))

  // Update metrics periodically
  const updateMetrics = useCallback(() => {
    const performanceReport = queryPerformanceAnalyzer.getPerformanceReport()
    const cacheStats = cacheOptimizer.getCacheStats()
    const realtimeStats = realtimeManager.getStats()

    setMetrics({
      queryCount: performanceReport.totalQueries,
      averageQueryTime: performanceReport.averageTime || 0,
      cacheHitRate: performanceReport.cacheHitRate || 0,
      slowQueries: performanceReport.slowQueries,
      realtimeConnected: realtimeStats.isConnected,
      activeSubscriptions: realtimeStats.active,
      memoryUsage: cacheStats.memoryUsage
    })
  }, [cacheOptimizer])

  // Setup real-time subscriptions for all tables
  const setupRealtimeSubscriptions = useCallback(() => {
    if (!isRealtimeEnabled) return

    const tables = ['vehicles', 'drivers', 'maintenance_records', 'fuel_records', 'branches']
    
    tables.forEach(table => {
      realtimeManager.subscribe(table, (event: RealtimeEvent) => {
        console.log(`Real-time event for ${table}:`, event)
        
        // Smart cache invalidation based on the event
        cacheInvalidationManager.smartInvalidate(table, event.eventType.toLowerCase() as any, event.new?.id)
        
        // Show user-friendly notifications
        showRealtimeNotification(table, event)
      })
    })
  }, [isRealtimeEnabled, cacheInvalidationManager])

  // Show real-time notifications
  const showRealtimeNotification = (table: string, event: RealtimeEvent) => {
    const tableNames = {
      vehicles: 'المركبات',
      drivers: 'السائقين',
      maintenance_records: 'الصيانة',
      fuel_records: 'الوقود',
      branches: 'الفروع'
    }

    const eventTypes = {
      INSERT: 'إضافة',
      UPDATE: 'تحديث',
      DELETE: 'حذف'
    }

    const tableName = tableNames[table as keyof typeof tableNames] || table
    const eventType = eventTypes[event.eventType] || event.eventType

    // Only show notifications for important events
    if (event.eventType === 'INSERT') {
      toast.info(`تم ${eventType} عنصر جديد في ${tableName}`, {
        duration: 3000,
        action: {
          label: 'تحديث',
          onClick: () => {
            queryClient.invalidateQueries({ queryKey: [table] })
          }
        }
      })
    }
  }

  // Toggle real-time functionality
  const toggleRealtime = useCallback(() => {
    setIsRealtimeEnabled(prev => {
      const newState = !prev
      
      if (newState) {
        setupRealtimeSubscriptions()
        toast.success('تم تفعيل التحديثات الفورية')
      } else {
        realtimeManager.cleanup()
        toast.info('تم إيقاف التحديثات الفورية')
      }
      
      return newState
    })
  }, [setupRealtimeSubscriptions])

  // Optimize cache
  const optimizeCache = useCallback(() => {
    cacheOptimizer.optimizeCache()
    updateMetrics()
    toast.success('تم تحسين الذاكرة المؤقتة')
  }, [cacheOptimizer, updateMetrics])

  // Get performance report
  const getPerformanceReport = useCallback(() => {
    return {
      performance: queryPerformanceAnalyzer.getPerformanceReport(),
      cache: cacheOptimizer.getCacheStats(),
      realtime: realtimeManager.getStats()
    }
  }, [cacheOptimizer])

  // Subscribe to specific table
  const subscribeToTable = useCallback((
    table: string, 
    callback?: (event: RealtimeEvent) => void
  ) => {
    const defaultCallback = (event: RealtimeEvent) => {
      cacheInvalidationManager.smartInvalidate(table, event.eventType.toLowerCase() as any, event.new?.id)
      if (callback) callback(event)
    }

    return realtimeManager.subscribe(table, defaultCallback)
  }, [cacheInvalidationManager])

  // Unsubscribe from table
  const unsubscribeFromTable = useCallback((subscriptionId: string) => {
    realtimeManager.unsubscribe(subscriptionId)
  }, [])

  // Refresh metrics
  const refreshMetrics = useCallback(() => {
    updateMetrics()
  }, [updateMetrics])

  // Setup initial subscriptions and metrics
  useEffect(() => {
    if (isRealtimeEnabled) {
      setupRealtimeSubscriptions()
    }

    // Update metrics every 30 seconds
    const metricsInterval = setInterval(updateMetrics, 30000)
    
    // Initial metrics update
    updateMetrics()

    return () => {
      clearInterval(metricsInterval)
      if (isRealtimeEnabled) {
        realtimeManager.cleanup()
      }
    }
  }, [isRealtimeEnabled, setupRealtimeSubscriptions, updateMetrics])

  // Performance monitoring
  useEffect(() => {
    // Monitor query performance
    const originalFetch = queryClient.getQueryCache().subscribe
    
    // Override to track performance
    const performanceTracker = (callback: any) => {
      return originalFetch.call(queryClient.getQueryCache(), (event: any) => {
        if (event.type === 'queryUpdated' && event.query.state.status === 'success') {
          const duration = event.query.state.dataUpdatedAt - event.query.state.fetchedAt
          
          queryPerformanceAnalyzer.logQuery({
            queryKey: event.query.queryKey.join('_'),
            duration,
            resultCount: Array.isArray(event.query.state.data) ? event.query.state.data.length : 1,
            cacheHit: event.query.state.isStale === false,
            timestamp: Date.now()
          })
        }
        
        callback(event)
      })
    }

    return () => {
      // Cleanup if needed
    }
  }, [queryClient])

  // Memory usage monitoring
  useEffect(() => {
    const checkMemoryUsage = () => {
      if ('memory' in performance) {
        const memInfo = (performance as any).memory
        if (memInfo.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
          console.warn('High memory usage detected:', memInfo.usedJSHeapSize / 1024 / 1024, 'MB')
          toast.warning('استخدام ذاكرة عالي - يُنصح بتحسين الذاكرة المؤقتة')
        }
      }
    }

    const memoryInterval = setInterval(checkMemoryUsage, 60000) // Check every minute
    
    return () => clearInterval(memoryInterval)
  }, [])

  // Preload critical data on mount
  useEffect(() => {
    cacheOptimizer.preloadCriticalData().catch(error => {
      console.error('Failed to preload critical data:', error)
    })
  }, [cacheOptimizer])

  const value: PerformanceContextType = {
    metrics,
    isRealtimeEnabled,
    toggleRealtime,
    optimizeCache,
    getPerformanceReport,
    subscribeToTable,
    unsubscribeFromTable,
    refreshMetrics
  }

  return (
    <PerformanceContext.Provider value={value}>
      {children}
    </PerformanceContext.Provider>
  )
}

export function usePerformance() {
  const context = useContext(PerformanceContext)
  if (context === undefined) {
    throw new Error('usePerformance must be used within a PerformanceProvider')
  }
  return context
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const { metrics, refreshMetrics } = usePerformance()
  
  useEffect(() => {
    // Refresh metrics when component mounts
    refreshMetrics()
  }, [refreshMetrics])

  return {
    metrics,
    isPerformant: metrics.averageQueryTime < 1000 && metrics.cacheHitRate > 0.7,
    needsOptimization: metrics.slowQueries > 5 || metrics.memoryUsage > 50 * 1024 * 1024
  }
}

// Real-time subscription hook
export function useRealtimeSubscription(table: string, callback?: (event: RealtimeEvent) => void) {
  const { subscribeToTable, unsubscribeFromTable, isRealtimeEnabled } = usePerformance()
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null)

  useEffect(() => {
    if (isRealtimeEnabled) {
      const id = subscribeToTable(table, callback)
      setSubscriptionId(id)

      return () => {
        if (id) {
          unsubscribeFromTable(id)
        }
      }
    }
  }, [table, callback, subscribeToTable, unsubscribeFromTable, isRealtimeEnabled])

  return {
    isSubscribed: !!subscriptionId && isRealtimeEnabled,
    subscriptionId
  }
}

export default PerformanceProvider
