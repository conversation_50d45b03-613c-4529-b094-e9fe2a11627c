"use client"

import { useState } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/context/auth-context"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from "recharts"
import { Download, FileText, TrendingUp, Calendar, Filter, Shield } from "lucide-react"

// Mock report data
const monthlyReports = [
  { month: "Jan", vehicles: 56, maintenance: 12, fuel: 1280, distance: 45000 },
  { month: "Feb", vehicles: 58, maintenance: 15, fuel: 1350, distance: 48000 },
  { month: "Mar", vehicles: 60, maintenance: 8, fuel: 1420, distance: 52000 },
  { month: "Apr", vehicles: 62, maintenance: 18, fuel: 1380, distance: 49000 },
  { month: "May", vehicles: 64, maintenance: 22, fuel: 1450, distance: 55000 },
]

const vehicleUtilization = [
  { type: "Trucks", utilized: 85, idle: 15 },
  { type: "Vans", utilized: 92, idle: 8 },
  { type: "Cars", utilized: 78, idle: 22 },
]

const maintenanceCosts = [
  { category: "Oil Change", cost: 2400, fill: "#3b82f6" },
  { category: "Brake Service", cost: 3200, fill: "#8b5cf6" },
  { category: "Tire Service", cost: 1800, fill: "#06b6d4" },
  { category: "Engine Service", cost: 4500, fill: "#f59e0b" },
  { category: "Other", cost: 1200, fill: "#ef4444" },
]

const topPerformingVehicles = [
  { vehicle: "ABC123", efficiency: 9.2, uptime: 98, distance: 12500 },
  { vehicle: "DEF456", efficiency: 9.8, uptime: 96, distance: 11800 },
  { vehicle: "GHI789", efficiency: 10.1, uptime: 94, distance: 10200 },
  { vehicle: "JKL012", efficiency: 10.5, uptime: 92, distance: 9800 },
  { vehicle: "MNO345", efficiency: 11.2, uptime: 90, distance: 9200 },
]

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("monthly")
  const [selectedReport, setSelectedReport] = useState("overview")
  const { toast } = useToast()
  const { hasPermission } = useAuth()

  // Check if user has permission to access reports
  if (!hasPermission("access_reports")) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-96">
            <Card className="w-96">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5 text-red-500" />
                  Access Denied
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  You don't have permission to access reports. This feature is available to Managers and Super Admins.
                </p>
              </CardContent>
            </Card>
          </div>
        </MainLayout>
      </ProtectedRoute>
    )
  }

  const handleExportReport = (reportType: string) => {
    toast({
      title: "Report Exported",
      description: `${reportType} report has been exported successfully.`,
    })
  }

  const handleGenerateReport = () => {
    toast({
      title: "Report Generated",
      description: "Custom report has been generated and is ready for download.",
    })
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
              <p className="text-muted-foreground">Comprehensive fleet performance insights and analytics</p>
            </div>
            <div className="flex space-x-2">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleGenerateReport}>
                <FileText className="mr-2 h-4 w-4" />
                Generate Report
              </Button>
            </div>
          </div>

          {/* Report Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Report Categories</CardTitle>
              <CardDescription>Select the type of report you want to view</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <Button
                  variant={selectedReport === "overview" ? "default" : "outline"}
                  onClick={() => setSelectedReport("overview")}
                  className="h-20 flex-col"
                >
                  <TrendingUp className="h-6 w-6 mb-2" />
                  Fleet Overview
                </Button>
                <Button
                  variant={selectedReport === "maintenance" ? "default" : "outline"}
                  onClick={() => setSelectedReport("maintenance")}
                  className="h-20 flex-col"
                >
                  <FileText className="h-6 w-6 mb-2" />
                  Maintenance
                </Button>
                <Button
                  variant={selectedReport === "fuel" ? "default" : "outline"}
                  onClick={() => setSelectedReport("fuel")}
                  className="h-20 flex-col"
                >
                  <Calendar className="h-6 w-6 mb-2" />
                  Fuel Analysis
                </Button>
                <Button
                  variant={selectedReport === "performance" ? "default" : "outline"}
                  onClick={() => setSelectedReport("performance")}
                  className="h-20 flex-col"
                >
                  <Filter className="h-6 w-6 mb-2" />
                  Performance
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Fleet Overview Charts */}
          {selectedReport === "overview" && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Fleet Metrics</CardTitle>
                  <CardDescription>Key performance indicators over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      vehicles: {
                        label: "Active Vehicles",
                        color: "hsl(var(--chart-1))",
                      },
                      maintenance: {
                        label: "Maintenance Events",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                    className="h-[300px]"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={monthlyReports}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Line type="monotone" dataKey="vehicles" stroke="var(--color-vehicles)" strokeWidth={2} />
                        <Line type="monotone" dataKey="maintenance" stroke="var(--color-maintenance)" strokeWidth={2} />
                      </LineChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Vehicle Utilization</CardTitle>
                  <CardDescription>Utilization rates by vehicle type</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      utilized: {
                        label: "Utilized (%)",
                        color: "hsl(var(--chart-1))",
                      },
                      idle: {
                        label: "Idle (%)",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                    className="h-[300px]"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={vehicleUtilization}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="type" />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Bar dataKey="utilized" fill="var(--color-utilized)" />
                        <Bar dataKey="idle" fill="var(--color-idle)" />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Maintenance Report */}
          {selectedReport === "maintenance" && (
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Maintenance Cost Breakdown</CardTitle>
                  <CardDescription>Distribution of maintenance expenses</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      cost: {
                        label: "Cost ($)",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className="h-[300px]"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie data={maintenanceCosts} cx="50%" cy="50%" outerRadius={80} dataKey="cost">
                          {maintenanceCosts.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.fill} />
                          ))}
                        </Pie>
                        <ChartTooltip content={<ChartTooltipContent />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Maintenance Trend</CardTitle>
                  <CardDescription>Maintenance events and costs over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <ChartContainer
                    config={{
                      maintenance: {
                        label: "Maintenance Events",
                        color: "hsl(var(--chart-2))",
                      },
                    }}
                    className="h-[300px]"
                  >
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={monthlyReports}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <ChartTooltip content={<ChartTooltipContent />} />
                        <Bar dataKey="maintenance" fill="var(--color-maintenance)" />
                      </BarChart>
                    </ResponsiveContainer>
                  </ChartContainer>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Performance Report */}
          {selectedReport === "performance" && (
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Vehicles</CardTitle>
                <CardDescription>Best performing vehicles based on efficiency and uptime</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Vehicle</TableHead>
                      <TableHead>Fuel Efficiency (L/100km)</TableHead>
                      <TableHead>Uptime (%)</TableHead>
                      <TableHead>Distance (km)</TableHead>
                      <TableHead>Performance Score</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {topPerformingVehicles.map((vehicle, index) => (
                      <TableRow key={vehicle.vehicle}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <span
                              className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold mr-2 ${
                                index === 0
                                  ? "bg-yellow-100 text-yellow-800"
                                  : index === 1
                                    ? "bg-gray-100 text-gray-800"
                                    : index === 2
                                      ? "bg-orange-100 text-orange-800"
                                      : "bg-blue-100 text-blue-800"
                              }`}
                            >
                              {index + 1}
                            </span>
                            {vehicle.vehicle}
                          </div>
                        </TableCell>
                        <TableCell>{vehicle.efficiency}</TableCell>
                        <TableCell>{vehicle.uptime}%</TableCell>
                        <TableCell>{vehicle.distance.toLocaleString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div
                                className="bg-green-600 h-2 rounded-full"
                                style={{ width: `${vehicle.uptime}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">{vehicle.uptime}%</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle>Export Reports</CardTitle>
              <CardDescription>Download reports in various formats</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <Button variant="outline" onClick={() => handleExportReport("Fleet Summary")} className="h-16 flex-col">
                  <Download className="h-5 w-5 mb-2" />
                  Fleet Summary (PDF)
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleExportReport("Maintenance Report")}
                  className="h-16 flex-col"
                >
                  <Download className="h-5 w-5 mb-2" />
                  Maintenance Report (Excel)
                </Button>
                <Button variant="outline" onClick={() => handleExportReport("Fuel Analysis")} className="h-16 flex-col">
                  <Download className="h-5 w-5 mb-2" />
                  Fuel Analysis (CSV)
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
