"use client"

import { useQuery } from "@tanstack/react-query"
import { supabaseApiClient, DashboardData } from "@/lib/supabase-api-client"
import { useAuth } from "@/context/auth-context"

export function useDashboardData() {
  const { user, isLoading: authLoading } = useAuth()
  
  return useQuery<DashboardData[], Error>({
    queryKey: ["dashboard-data"],
    queryFn: async () => {
      const response = await supabaseApiClient.getDashboardData()
      console.log("✅ useDashboardData - API response received:", response)
      
      // The response should be an array directly
      if (Array.isArray(response) && response.length > 0) {
        console.log("✅ useDashboardData - Valid array with", response.length, "items")
        return response
      }
      
      console.log("❌ useDashboardData - Invalid or empty response")
      return []
    },
    enabled: !!user && !authLoading, // Only run query when user is authenticated
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  })
}