# دليل النشر والصيانة - نظام إدارة الأسطول

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر النظام في بيئة الإنتاج وصيانته بشكل مستمر.

## 🚀 خيارات النشر

### 1. Vercel (موصى به)

#### المزايا
- ✅ نشر سهل ومباشر
- ✅ CDN عالمي
- ✅ SSL تلقائي
- ✅ تكامل مع GitHub
- ✅ Serverless functions
- ✅ دعم Next.js الكامل

#### خطوات النشر

1. **إعداد المشروع في Vercel**
```bash
# تثبيت Vercel CLI
npm i -g vercel

# تسجيل الدخول
vercel login

# ربط المشروع
vercel
```

2. **إعداد متغيرات البيئة**
```bash
# في Vercel Dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

3. **إعداد النشر التلقائي**
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "regions": ["iad1"],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key"
  }
}
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
# Multi-stage build
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  fleet-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_KEY}
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - fleet-app
    restart: unless-stopped
```

### 3. AWS Deployment

#### Using AWS Amplify
```yaml
# amplify.yml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
```

#### Using ECS with Fargate
```json
{
  "family": "fleet-management",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "fleet-app",
      "image": "your-account.dkr.ecr.region.amazonaws.com/fleet-management:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NEXT_PUBLIC_SUPABASE_URL",
          "value": "your-supabase-url"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/fleet-management",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

## 🔧 إعداد قاعدة البيانات

### 1. Supabase Production Setup

#### إعداد المشروع
```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom functions
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT role FROM profiles 
    WHERE id = auth.uid() AND user_status = 'Active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_branch_id()
RETURNS UUID AS $$
BEGIN
  RETURN (
    SELECT branch_id FROM profiles 
    WHERE id = auth.uid() AND user_status = 'Active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### تطبيق RLS Policies
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel_records ENABLE ROW LEVEL SECURITY;

-- Apply policies (see DATABASE_SCHEMA.md for complete policies)
```

#### إنشاء الفهارس
```sql
-- Performance indexes
CREATE INDEX CONCURRENTLY idx_vehicles_branch_id ON vehicles(branch_id);
CREATE INDEX CONCURRENTLY idx_vehicles_status ON vehicles(vehicle_status);
CREATE INDEX CONCURRENTLY idx_drivers_license_expiry ON drivers(license_expiry);
CREATE INDEX CONCURRENTLY idx_maintenance_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX CONCURRENTLY idx_fuel_date ON fuel_records(date);
```

### 2. Database Backup Strategy

#### Automated Backups
```bash
#!/bin/bash
# backup-database.sh

DB_URL="postgresql://postgres:<EMAIL>:5432/postgres"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump "$DB_URL" > "$BACKUP_DIR/fleet_backup_$DATE.sql"

# Compress backup
gzip "$BACKUP_DIR/fleet_backup_$DATE.sql"

# Keep only last 30 days
find "$BACKUP_DIR" -name "fleet_backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: fleet_backup_$DATE.sql.gz"
```

#### Cron Job Setup
```bash
# Add to crontab
0 2 * * * /path/to/backup-database.sh
```

## 📊 مراقبة النظام

### 1. Application Monitoring

#### Vercel Analytics
```typescript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  )
}
```

#### Custom Monitoring
```typescript
// lib/monitoring.ts
export class ApplicationMonitor {
  static logError(error: Error, context?: any) {
    console.error('Application Error:', {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    })
    
    // Send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService(error, context)
    }
  }

  static logPerformance(metric: string, value: number) {
    console.log('Performance Metric:', {
      metric,
      value,
      timestamp: new Date().toISOString()
    })
  }

  private static sendToMonitoringService(error: Error, context?: any) {
    // Implementation for external monitoring service
    // e.g., Sentry, LogRocket, etc.
  }
}
```

### 2. Database Monitoring

#### Performance Queries
```sql
-- Monitor slow queries
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows
FROM pg_stat_statements 
WHERE mean_time > 1000
ORDER BY mean_time DESC;

-- Monitor table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Monitor connection count
SELECT 
  count(*) as total_connections,
  count(*) FILTER (WHERE state = 'active') as active_connections,
  count(*) FILTER (WHERE state = 'idle') as idle_connections
FROM pg_stat_activity;
```

### 3. Health Checks

#### API Health Check
```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  try {
    // Check database connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (error) throw error

    // Check external services
    const checks = {
      database: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0'
    }

    return NextResponse.json(checks, { status: 200 })
  } catch (error) {
    return NextResponse.json(
      { 
        status: 'unhealthy', 
        error: error.message,
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    )
  }
}
```

#### Uptime Monitoring
```bash
#!/bin/bash
# uptime-check.sh

URL="https://your-domain.com/api/health"
WEBHOOK_URL="your-slack-webhook-url"

response=$(curl -s -o /dev/null -w "%{http_code}" "$URL")

if [ "$response" != "200" ]; then
  curl -X POST -H 'Content-type: application/json' \
    --data "{\"text\":\"🚨 Fleet Management System is down! HTTP $response\"}" \
    "$WEBHOOK_URL"
fi
```

## 🔄 CI/CD Pipeline

### 1. GitHub Actions

```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run tests
        run: npm run test:coverage
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'

  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security audit
        run: npm audit --audit-level high
      
      - name: Run dependency check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'fleet-management'
          path: '.'
          format: 'HTML'
```

### 2. Deployment Environments

#### Staging Environment
```bash
# .env.staging
NEXT_PUBLIC_SUPABASE_URL=https://staging-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=staging-anon-key
SUPABASE_SERVICE_ROLE_KEY=staging-service-key
```

#### Production Environment
```bash
# .env.production
NEXT_PUBLIC_SUPABASE_URL=https://production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=production-anon-key
SUPABASE_SERVICE_ROLE_KEY=production-service-key
```

## 🔒 Security Checklist

### Pre-deployment Security
- [ ] جميع environment variables محمية
- [ ] لا توجد API keys في الكود
- [ ] تم تطبيق HTTPS
- [ ] تم تفعيل RLS على جميع الجداول
- [ ] تم اختبار جميع الصلاحيات
- [ ] تم تحديث جميع dependencies

### Post-deployment Security
- [ ] مراقبة logs للأنشطة المشبوهة
- [ ] تحديث كلمات المرور بانتظام
- [ ] مراجعة صلاحيات المستخدمين
- [ ] backup منتظم لقاعدة البيانات
- [ ] مراقبة استخدام الموارد

## 📈 Performance Optimization

### 1. Frontend Optimization

```javascript
// next.config.js
module.exports = {
  experimental: {
    optimizePackageImports: ['@supabase/supabase-js', 'lucide-react']
  },
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      }
    }
    return config
  }
}
```

### 2. Database Optimization

```sql
-- Analyze query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT v.*, b.name as branch_name 
FROM vehicles v 
JOIN branches b ON v.branch_id = b.id 
WHERE v.vehicle_status = 'Active';

-- Update table statistics
ANALYZE vehicles;
ANALYZE drivers;
ANALYZE maintenance_records;
ANALYZE fuel_records;
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clear cache and rebuild
rm -rf .next
rm -rf node_modules
npm install
npm run build
```

#### 2. Database Connection Issues
```typescript
// Check connection
const testConnection = async () => {
  try {
    const { data, error } = await supabase.from('profiles').select('count')
    if (error) throw error
    console.log('✅ Database connected')
  } catch (error) {
    console.error('❌ Database connection failed:', error)
  }
}
```

#### 3. Performance Issues
```bash
# Analyze bundle size
npm run build:analyze

# Check memory usage
node --inspect server.js
```

### Emergency Procedures

#### 1. Rollback Deployment
```bash
# Vercel rollback
vercel rollback [deployment-url]

# Docker rollback
docker-compose down
docker-compose up -d --scale fleet-app=0
docker-compose up -d previous-image
```

#### 2. Database Recovery
```bash
# Restore from backup
psql "$DATABASE_URL" < backup_file.sql

# Point-in-time recovery (if supported)
# Contact Supabase support for assistance
```

هذا الدليل يوفر إرشادات شاملة لنشر وصيانة النظام بكفاءة وأمان.
