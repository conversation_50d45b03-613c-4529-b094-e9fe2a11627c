import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect, useState, useCallback } from 'react'
import { useRealtimeSubscription } from '@/context/performance-context'
import { useDashboardData, useDashboardStats, useDashboardAlerts } from './use-supabase-dashboard'
import { RealtimeEvent } from '@/lib/realtime-manager'
import { toast } from 'sonner'

// Real-time dashboard hook
export function useRealtimeDashboard() {
  const queryClient = useQueryClient()
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date())
  const [realtimeEvents, setRealtimeEvents] = useState<RealtimeEvent[]>([])

  // Base dashboard data
  const dashboardData = useDashboardData()
  const dashboardStats = useDashboardStats()
  const dashboardAlerts = useDashboardAlerts()

  // Real-time event handler
  const handleRealtimeEvent = useCallback((event: RealtimeEvent) => {
    console.log('Dashboard real-time event:', event)
    
    // Add to events log
    setRealtimeEvents(prev => [event, ...prev.slice(0, 49)]) // Keep last 50 events
    setLastUpdate(new Date())

    // Handle different event types
    switch (event.table) {
      case 'vehicles':
        handleVehicleEvent(event)
        break
      case 'drivers':
        handleDriverEvent(event)
        break
      case 'maintenance_records':
        handleMaintenanceEvent(event)
        break
      case 'fuel_records':
        handleFuelEvent(event)
        break
    }

    // Invalidate dashboard queries
    queryClient.invalidateQueries({ queryKey: ['dashboard'] })
  }, [queryClient])

  // Vehicle event handler
  const handleVehicleEvent = (event: RealtimeEvent) => {
    if (event.eventType === 'INSERT') {
      toast.success('تم إضافة مركبة جديدة', {
        description: `رقم اللوحة: ${event.new?.plate_number}`,
        duration: 4000
      })
    } else if (event.eventType === 'UPDATE') {
      // Check for status changes
      if (event.old?.vehicle_status !== event.new?.vehicle_status) {
        const statusNames = {
          'Active': 'نشطة',
          'Maintenance': 'في الصيانة',
          'Inactive': 'غير نشطة',
          'Out of Service': 'خارج الخدمة'
        }
        
        toast.info('تم تغيير حالة مركبة', {
          description: `${event.new?.plate_number}: ${statusNames[event.new?.vehicle_status as keyof typeof statusNames] || event.new?.vehicle_status}`,
          duration: 3000
        })
      }
    }
  }

  // Driver event handler
  const handleDriverEvent = (event: RealtimeEvent) => {
    if (event.eventType === 'INSERT') {
      toast.success('تم إضافة سائق جديد', {
        description: `${event.new?.full_name}`,
        duration: 4000
      })
    } else if (event.eventType === 'UPDATE') {
      // Check for status changes
      if (event.old?.status !== event.new?.status) {
        const statusNames = {
          'Active': 'نشط',
          'Available': 'متاح',
          'Inactive': 'غير نشط',
          'On Leave': 'في إجازة'
        }
        
        toast.info('تم تغيير حالة سائق', {
          description: `${event.new?.full_name}: ${statusNames[event.new?.status as keyof typeof statusNames] || event.new?.status}`,
          duration: 3000
        })
      }
    }
  }

  // Maintenance event handler
  const handleMaintenanceEvent = (event: RealtimeEvent) => {
    if (event.eventType === 'INSERT') {
      toast.info('تم جدولة صيانة جديدة', {
        description: `نوع الصيانة: ${event.new?.maintenance_type}`,
        duration: 4000
      })
    } else if (event.eventType === 'UPDATE') {
      if (event.old?.maintenance_status !== event.new?.maintenance_status) {
        const statusNames = {
          'Scheduled': 'مجدولة',
          'In Progress': 'قيد التنفيذ',
          'Completed': 'مكتملة',
          'Cancelled': 'ملغية'
        }
        
        const newStatus = statusNames[event.new?.maintenance_status as keyof typeof statusNames] || event.new?.maintenance_status
        
        if (event.new?.maintenance_status === 'Completed') {
          toast.success('تم إكمال صيانة', {
            description: `${event.new?.maintenance_type}`,
            duration: 4000
          })
        } else {
          toast.info('تم تحديث حالة صيانة', {
            description: `الحالة: ${newStatus}`,
            duration: 3000
          })
        }
      }
    }
  }

  // Fuel event handler
  const handleFuelEvent = (event: RealtimeEvent) => {
    if (event.eventType === 'INSERT') {
      toast.info('تم إضافة سجل وقود جديد', {
        description: `الكمية: ${event.new?.quantity_liters} لتر`,
        duration: 3000
      })
    }
  }

  // Subscribe to real-time updates for all relevant tables
  useRealtimeSubscription('vehicles', handleRealtimeEvent)
  useRealtimeSubscription('drivers', handleRealtimeEvent)
  useRealtimeSubscription('maintenance_records', handleRealtimeEvent)
  useRealtimeSubscription('fuel_records', handleRealtimeEvent)

  // Calculate real-time metrics
  const realtimeMetrics = useQuery({
    queryKey: ['dashboard', 'realtime-metrics'],
    queryFn: () => {
      const now = new Date()
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
      
      const recentEvents = realtimeEvents.filter(event => 
        new Date(event.timestamp) >= oneHourAgo
      )

      const eventsByTable = recentEvents.reduce((acc, event) => {
        acc[event.table] = (acc[event.table] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const eventsByType = recentEvents.reduce((acc, event) => {
        acc[event.eventType] = (acc[event.eventType] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      return {
        totalEvents: recentEvents.length,
        eventsByTable,
        eventsByType,
        lastUpdate,
        eventsPerMinute: recentEvents.length / 60
      }
    },
    enabled: realtimeEvents.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  })

  return {
    // Base dashboard data
    data: dashboardData.data,
    stats: dashboardStats.data,
    alerts: dashboardAlerts.data,
    
    // Loading states
    isLoading: dashboardData.isLoading || dashboardStats.isLoading || dashboardAlerts.isLoading,
    isError: dashboardData.isError || dashboardStats.isError || dashboardAlerts.isError,
    
    // Real-time specific data
    realtimeEvents: realtimeEvents.slice(0, 20), // Last 20 events
    realtimeMetrics: realtimeMetrics.data,
    lastUpdate,
    
    // Utility functions
    clearEvents: () => setRealtimeEvents([]),
    refreshData: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboard'] })
      setLastUpdate(new Date())
    }
  }
}

// Hook for real-time alerts
export function useRealtimeAlerts() {
  const [criticalAlerts, setCriticalAlerts] = useState<any[]>([])
  const [alertHistory, setAlertHistory] = useState<any[]>([])

  const handleCriticalEvent = useCallback((event: RealtimeEvent) => {
    // Check for critical events that need immediate attention
    const criticalConditions = [
      // Vehicle breakdown
      event.table === 'vehicles' && 
      event.eventType === 'UPDATE' && 
      event.new?.vehicle_status === 'Out of Service',
      
      // Overdue maintenance
      event.table === 'maintenance_records' && 
      event.eventType === 'UPDATE' && 
      event.new?.maintenance_status === 'Overdue',
      
      // Driver license expired
      event.table === 'drivers' && 
      event.eventType === 'UPDATE' && 
      new Date(event.new?.license_expiry) < new Date(),
    ]

    if (criticalConditions.some(condition => condition)) {
      const alert = {
        id: `alert_${Date.now()}`,
        type: 'critical',
        event,
        timestamp: new Date(),
        acknowledged: false
      }

      setCriticalAlerts(prev => [alert, ...prev])
      setAlertHistory(prev => [alert, ...prev.slice(0, 99)]) // Keep last 100

      // Show critical notification
      toast.error('تنبيه عاجل!', {
        description: getCriticalAlertMessage(event),
        duration: 10000,
        action: {
          label: 'عرض التفاصيل',
          onClick: () => {
            // Navigate to relevant page
            console.log('Navigate to:', event.table, event.new?.id)
          }
        }
      })
    }
  }, [])

  const getCriticalAlertMessage = (event: RealtimeEvent) => {
    if (event.table === 'vehicles' && event.new?.vehicle_status === 'Out of Service') {
      return `مركبة خارج الخدمة: ${event.new?.plate_number}`
    }
    if (event.table === 'maintenance_records' && event.new?.maintenance_status === 'Overdue') {
      return `صيانة متأخرة: ${event.new?.maintenance_type}`
    }
    if (event.table === 'drivers' && new Date(event.new?.license_expiry) < new Date()) {
      return `رخصة منتهية الصلاحية: ${event.new?.full_name}`
    }
    return 'حدث عاجل يتطلب انتباهك'
  }

  const acknowledgeAlert = (alertId: string) => {
    setCriticalAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    )
  }

  const dismissAlert = (alertId: string) => {
    setCriticalAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }

  // Subscribe to critical events
  useRealtimeSubscription('vehicles', handleCriticalEvent)
  useRealtimeSubscription('drivers', handleCriticalEvent)
  useRealtimeSubscription('maintenance_records', handleCriticalEvent)

  return {
    criticalAlerts: criticalAlerts.filter(alert => !alert.acknowledged),
    alertHistory,
    acknowledgeAlert,
    dismissAlert,
    unacknowledgedCount: criticalAlerts.filter(alert => !alert.acknowledged).length
  }
}

// Hook for real-time performance monitoring
export function useRealtimePerformance() {
  const [performanceMetrics, setPerformanceMetrics] = useState({
    eventsPerSecond: 0,
    averageLatency: 0,
    connectionQuality: 'good' as 'excellent' | 'good' | 'poor',
    lastEventTime: null as Date | null
  })

  const handlePerformanceEvent = useCallback((event: RealtimeEvent) => {
    const now = new Date()
    const eventTime = new Date(event.timestamp)
    const latency = now.getTime() - eventTime.getTime()

    setPerformanceMetrics(prev => ({
      ...prev,
      averageLatency: (prev.averageLatency + latency) / 2,
      lastEventTime: now,
      connectionQuality: latency < 100 ? 'excellent' : latency < 500 ? 'good' : 'poor'
    }))
  }, [])

  // Subscribe to all tables for performance monitoring
  useRealtimeSubscription('vehicles', handlePerformanceEvent)
  useRealtimeSubscription('drivers', handlePerformanceEvent)
  useRealtimeSubscription('maintenance_records', handlePerformanceEvent)
  useRealtimeSubscription('fuel_records', handlePerformanceEvent)

  return performanceMetrics
}
