"use client"

import { useQuery } from "@tanstack/react-query"
import { supabaseApiClient, type DashboardData, type DashboardStats, type ChartData } from "@/lib/supabase-api-client"

export function useDashboardData() {
  return useQuery({
    queryKey: ["dashboard-data"],
    queryFn: () => supabaseApiClient.getDashboardData(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}

export function useDashboardStats() {
  return useQuery({
    queryKey: ["dashboard-stats"],
    queryFn: () => supabaseApiClient.getDashboardStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}

export function useVehicleStatusDistribution() {
  return useQuery({
    queryKey: ["vehicle-status-distribution"],
    queryFn: () => supabaseApiClient.getVehicleStatusDistribution(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}

export function useVehicleLocationDistribution() {
  return useQuery({
    queryKey: ["vehicle-location-distribution"],
    queryFn: () => supabaseApiClient.getVehicleLocationDistribution(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}

export function useVehicleTypeDistribution() {
  return useQuery({
    queryKey: ["vehicle-type-distribution"],
    queryFn: () => supabaseApiClient.getVehicleTypeDistribution(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })
}