
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { supabaseApiClient, type MaintenanceRecord } from "@/lib/supabase-api-client"
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

// Query hooks
export function useMaintenance() {
  return useQuery({
    queryKey: queryKeys.maintenance(),
    queryFn: () => supabaseApiClient.getMaintenance(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useMaintenanceRecord(id: string) {
  const { data: maintenanceRecords } = useMaintenance() // Depend on useMaintenance
  return useQuery({
    queryKey: queryKeys.maintenanceRecord(id),
    queryFn: () => maintenanceRecords?.find(m => m.id === id) || null,
    enabled: !!id && !!maintenanceRecords, // Only enable if maintenanceRecords data is available
  })
}

// Mutation hooks
export function useAddMaintenance() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (maintenanceData: any) =>
      supabaseApiClient.addMaintenance(maintenanceData),
    onMutate: async (newMaintenance) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.maintenance() })
      
      const previousMaintenance = queryClient.getQueryData(queryKeys.maintenance())
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.maintenance(), (old: any) => {
        if (!old) return [{ ...newMaintenance, ID: `temp_${Date.now()}` }]
        return [...old, { ...newMaintenance, ID: `temp_${Date.now()}` }]
      })
      
      return { previousMaintenance }
    },
    onError: (error, newMaintenance, context) => {
      if (context?.previousMaintenance) {
        queryClient.setQueryData(queryKeys.maintenance(), context.previousMaintenance)
      }
      toast.error('Failed to add maintenance record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Maintenance record added successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.maintenance() })
      // Also invalidate vehicles as maintenance affects vehicle status
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

export function useUpdateMaintenance() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<MaintenanceRecord> }) => 
      supabaseApiClient.updateMaintenance(id, data),
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.maintenance() })
      
      const previousMaintenance = queryClient.getQueryData(queryKeys.maintenance())
      
      queryClient.setQueryData(queryKeys.maintenance(), (old: any) => {
        if (!old) return []
        return old.map((record: any) => 
          record.ID === id ? { ...record, ...data } : record
        )
      })
      
      return { previousMaintenance }
    },
    onError: (error, variables, context) => {
      if (context?.previousMaintenance) {
        queryClient.setQueryData(queryKeys.maintenance(), context.previousMaintenance)
      }
      toast.error('Failed to update maintenance record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Maintenance record updated successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.maintenance() })
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

export function useDeleteMaintenance() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => supabaseApiClient.deleteMaintenance(id),
    onMutate: async (id) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.maintenance() })
      
      const previousMaintenance = queryClient.getQueryData(queryKeys.maintenance())
      
      queryClient.setQueryData(queryKeys.maintenance(), (old: any) => {
        if (!old) return []
        return old.filter((record: any) => record.ID !== id)
      })
      
      return { previousMaintenance }
    },
    onError: (error, id, context) => {
      if (context?.previousMaintenance) {
        queryClient.setQueryData(queryKeys.maintenance(), context.previousMaintenance)
      }
      toast.error('Failed to delete maintenance record. Please try again.')
    },
    onSuccess: () => {
      toast.success('Maintenance record deleted successfully!')
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.maintenance() })
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() })
    },
  })
}

// Search and filter hooks
export function useFilteredMaintenance(maintenanceRecords: MaintenanceRecord[] | undefined, filters: {
  status?: string
  type?: string
  vehicleId?: string
  searchTerm?: string
}) {
  return useQuery({
    queryKey: [...queryKeys.maintenance(), 'filtered', filters],
    queryFn: () => {
      if (!maintenanceRecords) {
        return []
      }

      let filtered = maintenanceRecords
      
      if (filters.status && filters.status !== 'all') {
        filtered = filtered.filter(m => m.maintenance_status === filters.status)
      }
      
      if (filters.type && filters.type !== 'all') {
        filtered = filtered.filter(m => m.maintenance_type === filters.type)
      }
      
      if (filters.vehicleId) {
        filtered = filtered.filter(m => m.vehicle_id === filters.vehicleId)
      }
      
      if (filters.searchTerm) {
        const searchTermLower = filters.searchTerm.toLowerCase()
        filtered = filtered.filter(m =>
          m.Description.toLowerCase().includes(searchTermLower) ||
          m.Technician.toLowerCase().includes(searchTermLower) ||
          m.Type.toLowerCase().includes(searchTermLower)
        )
      }
      
      return filtered
    },
    enabled: !!maintenanceRecords,
    staleTime: 3 * 60 * 1000,
  })
}

// Maintenance statistics hook
export function useMaintenanceStats(maintenanceRecords: MaintenanceRecord[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.maintenance(), 'stats'],
    queryFn: () => {
      if (!maintenanceRecords) {
        return {
          total: 0,
          pending: 0,
          inProgress: 0,
          completed: 0,
          cancelled: 0,
          thisMonth: 0,
          totalCost: 0,
          averageCost: 0,
        }
      }

      const today = new Date()
      const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      
      return {
        total: maintenanceRecords.length,
        pending: maintenanceRecords.filter(m => m.Status === 'Pending').length,
        inProgress: maintenanceRecords.filter(m => m.Status === 'In Progress').length,
        completed: maintenanceRecords.filter(m => m.Status === 'Completed').length,
        cancelled: maintenanceRecords.filter(m => m.Status === 'Cancelled').length,
        thisMonth: maintenanceRecords.filter(m => {
          const createdDate = new Date(m['Created Date'])
          return createdDate >= thisMonth
        }).length,
        totalCost: maintenanceRecords
          .filter(m => m.Status === 'Completed')
          .reduce((sum, m) => sum + (m.Cost || 0), 0),
        averageCost: (() => {
          const completedWithCost = maintenanceRecords.filter(m => m.Status === 'Completed' && m.Cost)
          const totalCost = completedWithCost.reduce((sum, m) => sum + m.Cost, 0)
          return completedWithCost.length > 0 ? totalCost / completedWithCost.length : 0
        })(),
      }
    },
    enabled: !!maintenanceRecords,
    staleTime: 2 * 60 * 1000,
  })
}

// Upcoming maintenance hook
export function useUpcomingMaintenance(maintenanceRecords: MaintenanceRecord[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.maintenance(), 'upcoming'],
    queryFn: () => {
      if (!maintenanceRecords) {
        return []
      }

      const today = new Date()
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      return maintenanceRecords.filter(m => {
        if (m.Status !== 'Pending') return false
        
        const scheduledDate = new Date(m['Scheduled Date'])
        return scheduledDate >= today && scheduledDate <= nextWeek
      }).sort((a, b) =>
        new Date(a['Scheduled Date']).getTime() - new Date(b['Scheduled Date']).getTime()
      )
    },
    enabled: !!maintenanceRecords,
    staleTime: 5 * 60 * 1000,
  })
}

// Overdue maintenance hook
export function useOverdueMaintenance(maintenanceRecords: MaintenanceRecord[] | undefined) {
  return useQuery({
    queryKey: [...queryKeys.maintenance(), 'overdue'],
    queryFn: () => {
      if (!maintenanceRecords) {
        return []
      }

      const today = new Date()
      
      return maintenanceRecords.filter(m => {
        if (m.Status !== 'Pending') return false
        
        const scheduledDate = new Date(m['Scheduled Date'])
        return scheduledDate < today
      }).sort((a, b) =>
        new Date(a['Scheduled Date']).getTime() - new Date(b['Scheduled Date']).getTime()
      )
    },
    enabled: !!maintenanceRecords,
    staleTime: 2 * 60 * 1000,
  })
}

// Maintenance history for a specific vehicle
export function useVehicleMaintenanceHistory(maintenanceRecords: MaintenanceRecord[] | undefined, vehicleId: string) {
  return useQuery({
    queryKey: [...queryKeys.maintenance(), 'vehicle', vehicleId],
    queryFn: () => {
      if (!maintenanceRecords) {
        return []
      }
      return maintenanceRecords
        .filter(m => m['Vehicle ID'] === vehicleId)
        .sort((a, b) =>
          new Date(b['Scheduled Date']).getTime() - new Date(a['Scheduled Date']).getTime()
        )
    },
    enabled: !!vehicleId && !!maintenanceRecords,
    staleTime: 5 * 60 * 1000,
  })
}