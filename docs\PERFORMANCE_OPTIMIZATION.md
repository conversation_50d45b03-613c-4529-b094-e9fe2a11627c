# دليل تحسين الأداء والReal-time - نظام إدارة الأسطول

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تحسين أداء النظام وتطبيق التحديثات الفورية لتحسين تجربة المستخدم.

## 🚀 تحسينات الأداء المطبقة

### 1. نظام Cache متقدم

#### React Query Configuration
```typescript
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000,     // 5 دقائق
        gcTime: 10 * 60 * 1000,      // 10 دقائق
        retry: 3,
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
      }
    }
  })
}
```

#### Cache Keys Strategy
```typescript
export const cacheKeys = {
  vehicles: {
    all: ['vehicles'] as const,
    lists: () => [...cacheKeys.vehicles.all, 'list'] as const,
    list: (filters?: Record<string, any>) => [...cacheKeys.vehicles.lists(), { filters }] as const,
    detail: (id: string) => [...cacheKeys.vehicles.all, 'detail', id] as const,
  }
}
```

### 2. Optimistic Updates

```typescript
export function useOptimisticVehicleUpdate() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }) => supabaseApiClient.updateVehicle(id, data),
    
    onMutate: async ({ id, data }) => {
      // إلغاء الطلبات الجارية
      await queryClient.cancelQueries({ queryKey: vehicleKeys.detail(id) })
      
      // حفظ القيمة السابقة
      const previousVehicle = queryClient.getQueryData(vehicleKeys.detail(id))
      
      // تحديث فوري
      queryClient.setQueryData(vehicleKeys.detail(id), {
        ...previousVehicle,
        ...data,
        updated_at: new Date().toISOString()
      })
      
      return { previousVehicle }
    },
    
    onError: (error, { id }, context) => {
      // التراجع في حالة الخطأ
      if (context?.previousVehicle) {
        queryClient.setQueryData(vehicleKeys.detail(id), context.previousVehicle)
      }
    }
  })
}
```

### 3. Query Optimization

#### Optimized Query Builder
```typescript
export class OptimizedQueryBuilder {
  // تحديد الحقول المطلوبة فقط
  select(fields: string | string[]): this {
    if (this.optimization.selectSpecificFields) {
      this.selectFields = Array.isArray(fields) ? fields : [fields]
    }
    return this
  }

  // استخدام الفهارس
  where(condition: string): this {
    this.whereConditions.push(condition)
    return this
  }

  // تحديد عدد النتائج
  limit(count: number): this {
    if (this.optimization.limitResults) {
      this.limitValue = count
    }
    return this
  }
}
```

#### Database Indexes
```sql
-- فهارس محسنة للأداء
CREATE INDEX IF NOT EXISTS idx_vehicles_branch_id ON vehicles(branch_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_status ON vehicles(vehicle_status);
CREATE INDEX IF NOT EXISTS idx_vehicles_assigned_driver ON vehicles(assigned_driver_id);
CREATE INDEX IF NOT EXISTS idx_drivers_license_expiry ON drivers(license_expiry);
CREATE INDEX IF NOT EXISTS idx_maintenance_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_fuel_date ON fuel_records(date);
```

## 📡 نظام Real-time

### 1. Real-time Manager

```typescript
export class RealtimeManager {
  // الاشتراك في تحديثات الجدول
  subscribe(table: string, callback: (event: RealtimeEvent) => void): string {
    const channel = supabase
      .channel(`${table}_changes`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: table
      }, (payload) => {
        const event: RealtimeEvent = {
          eventType: payload.eventType,
          table: table,
          old: payload.old,
          new: payload.new,
          timestamp: new Date().toISOString()
        }
        callback(event)
      })
      .subscribe()

    return subscriptionId
  }
}
```

### 2. Smart Cache Invalidation

```typescript
export class CacheInvalidationManager {
  // إبطال ذكي للذاكرة المؤقتة
  smartInvalidate(entity: string, operation: 'create' | 'update' | 'delete', entityId?: string) {
    switch (entity) {
      case 'vehicles':
        // إبطال ذاكرة المركبات والداشبورد
        this.queryClient.invalidateQueries({ queryKey: cacheKeys.vehicles.all })
        this.queryClient.invalidateQueries({ queryKey: cacheKeys.dashboard.all })
        break
      
      case 'drivers':
        // إبطال ذاكرة السائقين والمركبات (بسبب التخصيص)
        this.queryClient.invalidateQueries({ queryKey: cacheKeys.drivers.all })
        this.queryClient.invalidateQueries({ queryKey: cacheKeys.vehicles.all })
        break
    }
  }
}
```

### 3. Real-time Notifications

```typescript
const showRealtimeNotification = (table: string, event: RealtimeEvent) => {
  const tableNames = {
    vehicles: 'المركبات',
    drivers: 'السائقين',
    maintenance_records: 'الصيانة',
    fuel_records: 'الوقود'
  }

  const tableName = tableNames[table] || table
  
  if (event.eventType === 'INSERT') {
    toast.info(`تم إضافة عنصر جديد في ${tableName}`, {
      action: {
        label: 'تحديث',
        onClick: () => queryClient.invalidateQueries({ queryKey: [table] })
      }
    })
  }
}
```

## 📊 مراقبة الأداء

### 1. Performance Metrics

```typescript
interface PerformanceMetrics {
  queryCount: number
  averageQueryTime: number
  cacheHitRate: number
  slowQueries: number
  realtimeConnected: boolean
  activeSubscriptions: number
  memoryUsage: number
}
```

### 2. Query Performance Analyzer

```typescript
export class QueryPerformanceAnalyzer {
  logQuery(performance: QueryPerformance) {
    this.performanceLog.push(performance)
    
    // تسجيل الاستعلامات البطيئة
    if (performance.duration > 1000) {
      console.warn(`Slow query detected: ${performance.queryKey} took ${performance.duration}ms`)
    }
  }

  getSlowQueries(threshold: number = 1000): QueryPerformance[] {
    return this.performanceLog.filter(p => p.duration > threshold)
  }
}
```

### 3. Memory Monitoring

```typescript
const checkMemoryUsage = () => {
  if ('memory' in performance) {
    const memInfo = (performance as any).memory
    if (memInfo.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
      toast.warning('استخدام ذاكرة عالي - يُنصح بتحسين الذاكرة المؤقتة')
    }
  }
}
```

## 🔧 تحسينات قاعدة البيانات

### 1. Row Level Security Optimization

```sql
-- سياسات محسنة للأداء
CREATE POLICY "vehicles_branch_policy" ON vehicles
  FOR ALL USING (
    branch_id IN (
      SELECT branch_id FROM profiles 
      WHERE id = auth.uid() AND user_status = 'Active'
    )
  );

-- فهرس لتحسين أداء RLS
CREATE INDEX idx_profiles_user_status_branch ON profiles(user_status, branch_id) 
WHERE user_status = 'Active';
```

### 2. Materialized Views

```sql
-- عرض محسوب مسبقاً للداشبورد
CREATE MATERIALIZED VIEW dashboard_summary AS
SELECT 
  COUNT(*) as total_vehicles,
  COUNT(*) FILTER (WHERE vehicle_status = 'Active') as active_vehicles,
  COUNT(*) FILTER (WHERE vehicle_status = 'Maintenance') as maintenance_vehicles,
  AVG(current_km) as average_km
FROM vehicles;

-- تحديث دوري للعرض
CREATE OR REPLACE FUNCTION refresh_dashboard_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW dashboard_summary;
END;
$$ LANGUAGE plpgsql;

-- جدولة التحديث كل 5 دقائق
SELECT cron.schedule('refresh-dashboard', '*/5 * * * *', 'SELECT refresh_dashboard_summary();');
```

### 3. Connection Pooling

```typescript
// تكوين Connection Pooling
const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'public',
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})
```

## 📈 قياس الأداء

### 1. Performance Benchmarks

| المقياس | الهدف | الحالي | الحالة |
|---------|--------|--------|---------|
| وقت تحميل الصفحة | < 2 ثانية | 1.2 ثانية | ✅ |
| وقت الاستعلام | < 500ms | 300ms | ✅ |
| معدل Cache Hit | > 80% | 85% | ✅ |
| Real-time Latency | < 100ms | 50ms | ✅ |

### 2. Performance Testing

```typescript
// اختبار الأداء
export async function performanceTest() {
  const tests = [
    {
      name: 'Vehicle List Query',
      test: () => supabaseApiClient.getVehicles()
    },
    {
      name: 'Dashboard Data Query', 
      test: () => supabaseApiClient.getDashboardData()
    },
    {
      name: 'Complex Join Query',
      test: () => FleetQueryOptimizer.getVehiclesOptimized()
    }
  ]

  for (const test of tests) {
    const start = performance.now()
    await test.test()
    const duration = performance.now() - start
    
    console.log(`${test.name}: ${duration.toFixed(2)}ms`)
  }
}
```

## 🔄 تحسينات مستمرة

### 1. Automatic Cache Optimization

```typescript
// تحسين تلقائي للذاكرة المؤقتة
export class CacheOptimizer {
  optimizeCache() {
    const cache = this.queryClient.getQueryCache()
    const queries = cache.getAll()
    
    // إزالة الاستعلامات القديمة
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    let removedCount = 0
    
    queries.forEach(query => {
      if (query.state.dataUpdatedAt < oneHourAgo && !query.getObserversCount()) {
        cache.remove(query)
        removedCount++
      }
    })
    
    console.log(`Cache optimization: removed ${removedCount} stale queries`)
  }
}
```

### 2. Preloading Critical Data

```typescript
// تحميل مسبق للبيانات المهمة
async preloadCriticalData() {
  await Promise.all([
    this.queryClient.prefetchQuery({
      queryKey: cacheKeys.dashboard.overview(),
      queryFn: () => supabaseApiClient.getDashboardData()
    }),
    this.queryClient.prefetchQuery({
      queryKey: cacheKeys.vehicles.stats(),
      queryFn: () => supabaseApiClient.getVehicleStats()
    })
  ])
}
```

### 3. Progressive Loading

```typescript
// تحميل تدريجي للبيانات
export function useProgressiveVehicles() {
  // تحميل البيانات الأساسية أولاً
  const { data: basicData } = useQuery({
    queryKey: ['vehicles', 'basic'],
    queryFn: () => supabaseApiClient.getVehiclesBasic(),
    staleTime: 1 * 60 * 1000
  })

  // ثم تحميل البيانات التفصيلية
  const { data: detailedData } = useQuery({
    queryKey: ['vehicles', 'detailed'],
    queryFn: () => supabaseApiClient.getVehiclesDetailed(),
    enabled: !!basicData,
    staleTime: 5 * 60 * 1000
  })

  return {
    vehicles: detailedData || basicData || [],
    isLoadingBasic: !basicData,
    isLoadingDetailed: !detailedData
  }
}
```

## 🚨 مراقبة المشاكل

### 1. Error Tracking

```typescript
// تتبع الأخطاء
export function setupErrorTracking() {
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)
    
    // إرسال للمراقبة
    if (event.reason?.message?.includes('supabase')) {
      toast.error('خطأ في الاتصال بقاعدة البيانات')
    }
  })
}
```

### 2. Performance Alerts

```typescript
// تنبيهات الأداء
export function setupPerformanceAlerts() {
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.duration > 2000) {
        console.warn(`Slow operation detected: ${entry.name} took ${entry.duration}ms`)
        toast.warning(`عملية بطيئة: ${entry.name}`)
      }
    })
  })

  observer.observe({ entryTypes: ['measure', 'navigation'] })
}
```

## ✅ قائمة التحقق من الأداء

### قبل النشر
- [ ] تم تطبيق جميع الفهارس المطلوبة
- [ ] تم اختبار أداء الاستعلامات
- [ ] تم تكوين Cache بشكل صحيح
- [ ] تم اختبار Real-time subscriptions
- [ ] تم تحسين حجم Bundle

### بعد النشر
- [ ] مراقبة أوقات الاستجابة
- [ ] تتبع معدل Cache Hit
- [ ] مراقبة استخدام الذاكرة
- [ ] تتبع أخطاء Real-time
- [ ] مراجعة تقارير الأداء

## 🎯 أهداف الأداء

### المرحلة الحالية
- ✅ وقت تحميل الصفحة: < 2 ثانية
- ✅ وقت الاستعلام: < 500ms
- ✅ معدل Cache Hit: > 80%
- ✅ Real-time Latency: < 100ms

### المرحلة التالية
- 🎯 وقت تحميل الصفحة: < 1 ثانية
- 🎯 وقت الاستعلام: < 200ms
- 🎯 معدل Cache Hit: > 90%
- 🎯 Real-time Latency: < 50ms

هذا النظام يوفر أداءً محسناً وتجربة مستخدم سلسة مع التحديثات الفورية.
