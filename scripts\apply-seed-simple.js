#!/usr/bin/env node

/**
 * Fleet Management System - Seed Data Application
 * Applies sample data using Supabase REST API
 * This is the main seed data script for the project
 */

const fs = require('fs')
const path = require('path')
const https = require('https')

// Load environment variables manually
function loadEnvVars() {
  const envPath = path.join(process.cwd(), '.env.local')
  const envVars = {}
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')
    
    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=')
        const value = valueParts.join('=').trim()
        envVars[key.trim()] = value
      }
    }
  }
  
  return envVars
}

const envVars = loadEnvVars()
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL || 'https://vjozjofhwlpskbgbgzve.supabase.co'
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE0NzIxOCwiZXhwIjoyMDY4NzIzMjE4fQ.lL5Dw3NPc8HPQS6_pi5-hnDUsexj4Ao9EOXxvuIzZaU'

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Make HTTP request to Supabase
function makeSupabaseRequest(method, endpoint, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(endpoint, supabaseUrl)
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
        'Prefer': 'return=minimal'
      }
    }

    const req = https.request(url, options, (res) => {
      let responseData = ''
      
      res.on('data', (chunk) => {
        responseData += chunk
      })
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsed = responseData ? JSON.parse(responseData) : {}
            resolve(parsed)
          } catch (error) {
            resolve(responseData)
          }
        } else {
          try {
            const parsed = JSON.parse(responseData)
            reject(new Error(`HTTP ${res.statusCode}: ${parsed.message || responseData}`))
          } catch (error) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`))
          }
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Sample data
const sampleData = {
  branches: [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      name: 'فرع القاهرة',
      location: 'القاهرة',
      address: 'شارع التحرير، وسط البلد، القاهرة',
      phone: '+20-2-1234567',
      email: '<EMAIL>',
      branch_status: 'Active'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440002',
      name: 'فرع الإسكندرية',
      location: 'الإسكندرية',
      address: 'كورنيش الإسكندرية، الإسكندرية',
      phone: '+20-3-2345678',
      email: '<EMAIL>',
      branch_status: 'Active'
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440003',
      name: 'فرع الجونة',
      location: 'الجونة',
      address: 'الجونة، البحر الأحمر',
      phone: '+20-65-3456789',
      email: '<EMAIL>',
      branch_status: 'Active'
    }
  ],
  
  drivers: [
    {
      id: '660e8400-e29b-41d4-a716-446655440001',
      full_name: 'Ahmed Mohamed Ali',
      license_number: 'DL123456789',
      license_expiry: '2026-12-31',
      phone: '+20-10-1234567',
      email: '<EMAIL>',
      status: 'Available',
      hire_date: '2023-01-15',
      branch_id: '550e8400-e29b-41d4-a716-446655440001'
    },
    {
      id: '660e8400-e29b-41d4-a716-446655440002',
      full_name: 'Omar Hassan Ibrahim',
      license_number: 'DL987654321',
      license_expiry: '2026-08-30',
      phone: '+20-11-2345678',
      email: '<EMAIL>',
      status: 'Available',
      hire_date: '2023-02-20',
      branch_id: '550e8400-e29b-41d4-a716-446655440001'
    },
    {
      id: '660e8400-e29b-41d4-a716-446655440003',
      full_name: 'Mohamed Khaled Saeed',
      license_number: 'DL456789123',
      license_expiry: '2026-09-15',
      phone: '+20-12-3456789',
      email: '<EMAIL>',
      status: 'Available',
      hire_date: '2023-03-10',
      branch_id: '550e8400-e29b-41d4-a716-446655440002'
    },
    {
      id: '660e8400-e29b-41d4-a716-446655440004',
      full_name: 'Ali Ahmed Mahmoud',
      license_number: 'DL789123456',
      license_expiry: '2026-11-20',
      phone: '+20-10-4567890',
      email: '<EMAIL>',
      status: 'Available',
      hire_date: '2023-04-05',
      branch_id: '550e8400-e29b-41d4-a716-446655440002'
    },
    {
      id: '660e8400-e29b-41d4-a716-446655440005',
      full_name: 'Hassan Omar Farouk',
      license_number: 'DL321654987',
      license_expiry: '2026-08-10',
      phone: '+20-11-5678901',
      email: '<EMAIL>',
      status: 'Available',
      hire_date: '2023-05-12',
      branch_id: '550e8400-e29b-41d4-a716-446655440003'
    }
  ],
  
  vehicles: [
    {
      id: '770e8400-e29b-41d4-a716-446655440001',
      plate_number: 'CAI-001',
      vin_number: 'LEVC123456789',
      model: 2023,
      vehicle_type: 'Taxi',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Electric',
      tank_capacity_liters: null,
      engine_cc: null,
      color: 'Black',
      current_km: 15000,
      branch_id: '550e8400-e29b-41d4-a716-446655440001',
      assigned_driver_id: '660e8400-e29b-41d4-a716-446655440001',
      vehicle_status: 'Active'
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440002',
      plate_number: 'CAI-002',
      vin_number: 'LEVC987654321',
      model: 2023,
      vehicle_type: 'Taxi',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Electric',
      tank_capacity_liters: null,
      engine_cc: null,
      color: 'Black',
      current_km: 12000,
      branch_id: '550e8400-e29b-41d4-a716-446655440001',
      assigned_driver_id: '660e8400-e29b-41d4-a716-446655440002',
      vehicle_status: 'Active'
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440003',
      plate_number: 'ALEX-001',
      vin_number: 'TOYOTA123456789',
      model: 2022,
      vehicle_type: 'Microbus',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Gasoline',
      tank_capacity_liters: 70,
      engine_cc: 2700,
      color: 'White',
      current_km: 25000,
      branch_id: '550e8400-e29b-41d4-a716-446655440002',
      assigned_driver_id: '660e8400-e29b-41d4-a716-446655440003',
      vehicle_status: 'Active'
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440004',
      plate_number: 'ALEX-002',
      vin_number: 'TOYOTA987654321',
      model: 2022,
      vehicle_type: 'Microbus',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Gasoline',
      tank_capacity_liters: 70,
      engine_cc: 2700,
      color: 'Blue',
      current_km: 30000,
      branch_id: '550e8400-e29b-41d4-a716-446655440002',
      assigned_driver_id: '660e8400-e29b-41d4-a716-446655440004',
      vehicle_status: 'Active'
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440005',
      plate_number: 'GOUNA-001',
      vin_number: 'HYUNDAI123456789',
      model: 2021,
      vehicle_type: 'Microbus',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Gasoline',
      tank_capacity_liters: 65,
      engine_cc: 2500,
      color: 'Silver',
      current_km: 35000,
      branch_id: '550e8400-e29b-41d4-a716-446655440003',
      assigned_driver_id: '660e8400-e29b-41d4-a716-446655440005',
      vehicle_status: 'Active'
    },
    {
      id: '770e8400-e29b-41d4-a716-446655440006',
      plate_number: 'GOUNA-002',
      vin_number: 'HYUNDAI987654321',
      model: 2021,
      vehicle_type: 'Microbus',
      service_type: 'Passenger',
      department: 'Transportation',
      fuel_type: 'Gasoline',
      tank_capacity_liters: 65,
      engine_cc: 2500,
      color: 'Red',
      current_km: 28000,
      branch_id: '550e8400-e29b-41d4-a716-446655440003',
      assigned_driver_id: null,
      vehicle_status: 'Maintenance'
    }
  ],

  maintenance_records: [
    {
      id: '880e8400-e29b-41d4-a716-446655440001',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440001',
      maintenance_type: 'Routine Maintenance',
      maintenance_status: 'Completed',
      scheduled_date: '2025-07-15',
      completed_date: '2025-07-15',
      cost: 500.00,
      description: 'تغيير زيت وفلاتر',
      technician: 'أحمد الفني',
      next_maintenance_km: 20000
    },
    {
      id: '880e8400-e29b-41d4-a716-446655440002',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440002',
      maintenance_type: 'Emergency Repair',
      maintenance_status: 'Completed',
      scheduled_date: '2025-07-10',
      completed_date: '2025-07-12',
      cost: 1200.00,
      description: 'إصلاح نظام الفرامل',
      technician: 'محمد الفني',
      next_maintenance_km: 17000
    },
    {
      id: '880e8400-e29b-41d4-a716-446655440003',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440006',
      maintenance_type: 'Major Repair',
      maintenance_status: 'In Progress',
      scheduled_date: '2025-07-20',
      completed_date: null,
      cost: 2500.00,
      description: 'إصلاح المحرك',
      technician: 'علي الفني',
      next_maintenance_km: 30000
    }
  ],

  fuel_records: [
    {
      id: '990e8400-e29b-41d4-a716-446655440001',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440003',
      date: '2025-07-20',
      quantity_liters: 65.0,
      distance_km: 450,
      cost: 520.00,
      station: 'محطة الإسكندرية الرئيسية'
    },
    {
      id: '990e8400-e29b-41d4-a716-446655440002',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440004',
      date: '2025-07-18',
      quantity_liters: 70.0,
      distance_km: 380,
      cost: 560.00,
      station: 'محطة الإسكندرية الشرقية'
    },
    {
      id: '990e8400-e29b-41d4-a716-446655440003',
      vehicle_id: '770e8400-e29b-41d4-a716-446655440005',
      date: '2025-07-16',
      quantity_liters: 60.0,
      distance_km: 420,
      cost: 480.00,
      station: 'محطة الجونة المركزية'
    }
  ]
}

// Insert data into table
async function insertData(tableName, data) {
  try {
    log(`   Inserting ${data.length} records into ${tableName}...`, 'blue')
    
    for (const record of data) {
      try {
        await makeSupabaseRequest('POST', `/rest/v1/${tableName}`, record)
      } catch (error) {
        if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
          // Record already exists, skip
          continue
        } else {
          throw error
        }
      }
    }
    
    log(`   ✅ ${tableName} data inserted successfully`, 'green')
    return true
  } catch (error) {
    if (error.message.includes('duplicate key') || error.message.includes('already exists')) {
      log(`   ⚠️  ${tableName}: Data already exists (skipping)`, 'yellow')
      return true
    } else {
      log(`   ❌ ${tableName} failed: ${error.message}`, 'red')
      return false
    }
  }
}

// Check table data count
async function checkTableData(tableName) {
  try {
    const result = await makeSupabaseRequest('GET', `/rest/v1/${tableName}?select=count&limit=1`)
    return { exists: true, count: Array.isArray(result) ? result.length : 0 }
  } catch (error) {
    return { exists: false, error: error.message }
  }
}

// Apply seed data using direct inserts
async function applySeedDataSimple() {
  log('🌱 Fleet Management System - Simple Seed Data Application', 'bold')
  log('=' .repeat(60), 'blue')

  try {
    // Test connection
    log('📡 Testing Supabase connection...', 'blue')
    await makeSupabaseRequest('GET', '/rest/v1/branches?select=count&limit=1')
    log('   ✅ Connection successful', 'green')

    // Check existing data
    log('\n🔍 Checking existing data...', 'blue')
    const tables = ['branches', 'drivers', 'vehicles', 'maintenance_records', 'fuel_records']

    for (const table of tables) {
      const result = await checkTableData(table)
      if (result.exists) {
        log(`   ${table}: ${result.count} records`, result.count > 0 ? 'yellow' : 'green')
      } else {
        log(`   ${table}: Error - ${result.error}`, 'red')
      }
    }

    // Apply data in correct order (respecting foreign keys)
    log('\n🔧 Applying seed data...', 'blue')
    
    const results = []
    
    // 1. Branches first (no dependencies)
    const branchResult = await insertData('branches', sampleData.branches)
    results.push({ table: 'branches', success: branchResult })
    
    // 2. Drivers (depends on branches)
    const driverResult = await insertData('drivers', sampleData.drivers)
    results.push({ table: 'drivers', success: driverResult })
    
    // 3. Vehicles (depends on branches and drivers)
    const vehicleResult = await insertData('vehicles', sampleData.vehicles)
    results.push({ table: 'vehicles', success: vehicleResult })

    // 4. Maintenance Records (depends on vehicles)
    const maintenanceResult = await insertData('maintenance_records', sampleData.maintenance_records)
    results.push({ table: 'maintenance_records', success: maintenanceResult })

    // 5. Fuel Records (depends on vehicles)
    const fuelResult = await insertData('fuel_records', sampleData.fuel_records)
    results.push({ table: 'fuel_records', success: fuelResult })

    // Summary
    log('\n' + '=' .repeat(60), 'blue')
    log('📊 Simple Seed Data Application Summary', 'bold')
    log('=' .repeat(60), 'blue')

    const successful = results.filter(r => r.success).length
    const failed = results.filter(r => !r.success).length

    log(`✅ Successful: ${successful}`, 'green')
    log(`❌ Failed: ${failed}`, failed > 0 ? 'red' : 'green')

    if (failed > 0) {
      log('\n🚨 Failed tables:', 'red')
      results.filter(r => !r.success).forEach(r => {
        log(`   ${r.table}`, 'red')
      })
    }

    // Verify data
    log('\n🔍 Verifying applied data...', 'blue')
    for (const table of tables) {
      const result = await checkTableData(table)
      if (result.exists) {
        log(`   ${table}: ${result.count} records`, result.count > 0 ? 'green' : 'yellow')
      }
    }

    log('\n✅ Simple seed data application completed!', 'green')
    
    if (failed === 0) {
      log('🎉 All sample data applied successfully!', 'green')
      log('\nNext steps:', 'blue')
      log('1. Apply RLS policies: npm run apply-rls', 'blue')
      log('2. Start development: npm run dev', 'blue')
      log('3. Open: http://localhost:3000', 'blue')
    }

    return { successful, failed, results }

  } catch (error) {
    log(`\n❌ Seed data application failed: ${error.message}`, 'red')
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      log('\n🔧 Connection troubleshooting:', 'yellow')
      log('1. Check your internet connection', 'yellow')
      log('2. Verify Supabase URL in .env.local', 'yellow')
      log('3. Ensure Supabase project is active', 'yellow')
    }
    
    throw error
  }
}

// Main execution
async function main() {
  const command = process.argv[2]

  switch (command) {
    case 'apply':
      await applySeedDataSimple()
      break
    
    default:
      log('Fleet Management System - Simple Seed Data Manager', 'bold')
      log('')
      log('This script applies basic seed data using direct table inserts.')
      log('No exec_sql function required.')
      log('')
      log('Usage: node apply-seed-simple.js apply')
      log('')
      log('Example:')
      log('  node scripts/apply-seed-simple.js apply')
      
      // Auto-run if no command provided
      log('\nAuto-running seed data application...', 'yellow')
      await applySeedDataSimple()
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`❌ Script failed: ${error.message}`, 'red')
    process.exit(1)
  })
}

module.exports = { applySeedDataSimple }
