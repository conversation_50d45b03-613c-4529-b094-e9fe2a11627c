// React Query configuration and setup
import { QueryClient } from '@tanstack/react-query'

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes (increased to reduce requests)
      gcTime: 20 * 60 * 1000, // 20 minutes (increased to keep data longer)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors or rate limiting
        if (error && typeof error === 'object' && 'status' in error) {
          const status = error.status as number
          if (status >= 400 && status < 500) {
            return false
          }
        }
        // Don't retry on rate limiting errors
        if (error && typeof error === 'object' && 'message' in error) {
          const message = (error.message as string).toLowerCase()
          if (message.includes('rate limit') || message.includes('too many requests')) {
            return false
          }
        }
        return failureCount < 2 // Reduced retry attempts
      },
      retryDelay: (attemptIndex) => Math.min(2000 * 2 ** attemptIndex, 30000), // Longer delays
      refetchOnWindowFocus: false, // Prevent unnecessary refetches
      refetchOnMount: false, // Use cached data when available
    },
    mutations: {
      retry: (failureCount, error) => {
        // Don't retry mutations on rate limiting
        if (error && typeof error === 'object' && 'message' in error) {
          const message = (error.message as string).toLowerCase()
          if (message.includes('rate limit') || message.includes('too many requests')) {
            return false
          }
        }
        return failureCount < 1 // Only retry once for mutations
      },
      retryDelay: 3000, // 3 second delay for mutation retries
    },
  },
})

// Query keys factory
export const queryKeys = {
  all: ['fleet-management'] as const,
  vehicles: () => [...queryKeys.all, 'vehicles'] as const,
  vehicle: (id: string) => [...queryKeys.vehicles(), id] as const,
  drivers: () => [...queryKeys.all, 'drivers'] as const,
  driver: (id: string) => [...queryKeys.drivers(), id] as const,
  maintenance: () => [...queryKeys.all, 'maintenance'] as const,
  maintenanceRecord: (id: string) => [...queryKeys.maintenance(), id] as const,
  fuel: () => [...queryKeys.all, 'fuel'] as const,
  fuelRecord: (id: string) => [...queryKeys.fuel(), id] as const,
  users: () => [...queryKeys.all, 'users'] as const,
  user: (id: string) => [...queryKeys.users(), id] as const,
  branches: () => [...queryKeys.all, 'branches'] as const,
  branch: (id: string) => [...queryKeys.branches(), id] as const,
  reports: () => [...queryKeys.all, 'reports'] as const,
  vehicleReport: (filters: any) => [...queryKeys.reports(), 'vehicles', filters] as const,
  search: (entity: string, term: string) => [...queryKeys.all, 'search', entity, term] as const,
}

// Cache invalidation helpers
export const invalidateQueries = {
  vehicles: () => queryClient.invalidateQueries({ queryKey: queryKeys.vehicles() }),
  drivers: () => queryClient.invalidateQueries({ queryKey: queryKeys.drivers() }),
  maintenance: () => queryClient.invalidateQueries({ queryKey: queryKeys.maintenance() }),
  fuel: () => queryClient.invalidateQueries({ queryKey: queryKeys.fuel() }),
  users: () => queryClient.invalidateQueries({ queryKey: queryKeys.users() }),
  branches: () => queryClient.invalidateQueries({ queryKey: queryKeys.branches() }),
  reports: () => queryClient.invalidateQueries({ queryKey: queryKeys.reports() }),
  all: () => queryClient.invalidateQueries({ queryKey: queryKeys.all }),
}

// Prefetch helpers
export const prefetchQueries = {
  vehicles: () => queryClient.prefetchQuery({
    queryKey: queryKeys.vehicles(),
    queryFn: async () => {
      const { supabaseApiClient } = await import('./supabase-api-client')
      return supabaseApiClient.getVehicles()
    },
  }),
  drivers: () => queryClient.prefetchQuery({
    queryKey: queryKeys.drivers(),
    queryFn: async () => {
      const { supabaseApiClient } = await import('./supabase-api-client')
      return supabaseApiClient.getDrivers()
    },
  }),
  maintenance: () => queryClient.prefetchQuery({
    queryKey: queryKeys.maintenance(),
    queryFn: async () => {
      const { supabaseApiClient } = await import('./supabase-api-client')
      return supabaseApiClient.getMaintenance()
    },
  }),
  fuel: () => queryClient.prefetchQuery({
    queryKey: queryKeys.fuel(),
    queryFn: async () => {
      const { supabaseApiClient } = await import('./supabase-api-client')
      return supabaseApiClient.getFuel()
    },
  }),
  branches: () => queryClient.prefetchQuery({
    queryKey: queryKeys.branches(),
    queryFn: async () => {
      const { supabaseApiClient } = await import('./supabase-api-client')
      return supabaseApiClient.getBranches()
    },
  }),
}

// Error handlers
export const errorHandlers = {
  onError: (error: Error) => {
    console.error('React Query Error:', error)
    
    // You can add global error handling here
    // For example, show a toast notification
    if (typeof window !== 'undefined') {
      // Import toast dynamically to avoid SSR issues
      import('sonner').then(({ toast }) => {
        toast.error('Something went wrong. Please try again.')
      })
    }
  },
  
  onMutationError: (error: Error) => {
    console.error('React Query Mutation Error:', error)
    
    if (typeof window !== 'undefined') {
      import('sonner').then(({ toast }) => {
        toast.error('Failed to save changes. Please try again.')
      })
    }
  },
}

// Optimistic update helpers
export const optimisticUpdates = {
  addVehicle: (newVehicle: any) => {
    queryClient.setQueryData(queryKeys.vehicles(), (oldData: any) => {
      if (!oldData) return [newVehicle]
      return [...oldData, newVehicle]
    })
  },
  
  updateVehicle: (updatedVehicle: any) => {
    queryClient.setQueryData(queryKeys.vehicles(), (oldData: any) => {
      if (!oldData) return []
      return oldData.map((vehicle: any) =>
        vehicle.vehicle_id === updatedVehicle.vehicle_id ? { ...vehicle, ...updatedVehicle } : vehicle
      )
    })
  },
  
  deleteVehicle: (vehicleId: string) => {
    queryClient.setQueryData(queryKeys.vehicles(), (oldData: any) => {
      if (!oldData) return []
      return oldData.filter((vehicle: any) => vehicle.vehicle_id !== vehicleId)
    })
  },
  
  addDriver: (newDriver: any) => {
    queryClient.setQueryData(queryKeys.drivers(), (oldData: any) => {
      if (!oldData) return [newDriver]
      return [...oldData, newDriver]
    })
  },
  
  updateDriver: (updatedDriver: any) => {
    queryClient.setQueryData(queryKeys.drivers(), (oldData: any) => {
      if (!oldData) return []
      return oldData.map((driver: any) => 
        driver.ID === updatedDriver.ID ? { ...driver, ...updatedDriver } : driver
      )
    })
  },
  
  deleteDriver: (driverId: string) => {
    queryClient.setQueryData(queryKeys.drivers(), (oldData: any) => {
      if (!oldData) return []
      return oldData.filter((driver: any) => driver.ID !== driverId)
    })
  },
  
  addBranch: (newBranch: any) => {
    queryClient.setQueryData(queryKeys.branches(), (oldData: any) => {
      if (!oldData) return [newBranch]
      return [...oldData, newBranch]
    })
  },
  
  updateBranch: (updatedBranch: any) => {
    queryClient.setQueryData(queryKeys.branches(), (oldData: any) => {
      if (!oldData) return []
      console.log("Optimistic update - oldData:", oldData)
      console.log("Optimistic update - updatedBranch:", updatedBranch)
      
      const updated = oldData.map((branch: any) => {
        if (branch.branch_id === updatedBranch.branch_id) {
          console.log("Found matching branch, updating:", branch.branch_id)
          return { ...branch, ...updatedBranch }
        }
        return branch
      })
      
      console.log("Optimistic update result:", updated)
      return updated
    })
  },
  
  deleteBranch: (branchId: string) => {
    queryClient.setQueryData(queryKeys.branches(), (oldData: any) => {
      if (!oldData) return []
      return oldData.filter((branch: any) => branch.branch_id !== branchId)
    })
  },
}

// Background sync for offline support
export const backgroundSync = {
  syncAll: async () => {
    try {
      await Promise.all([
        queryClient.refetchQueries({ queryKey: queryKeys.vehicles() }),
        queryClient.refetchQueries({ queryKey: queryKeys.drivers() }),
        queryClient.refetchQueries({ queryKey: queryKeys.maintenance() }),
        queryClient.refetchQueries({ queryKey: queryKeys.fuel() }),
        queryClient.refetchQueries({ queryKey: queryKeys.branches() }),
      ])
    } catch (error) {
      console.error('Background sync failed:', error)
    }
  },
  
  enablePeriodicSync: (intervalMs: number = 5 * 60 * 1000) => {
    if (typeof window !== 'undefined') {
      setInterval(backgroundSync.syncAll, intervalMs)
    }
  },
}