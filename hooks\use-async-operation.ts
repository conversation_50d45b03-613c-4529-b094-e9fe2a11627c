"use client"

import { useState, useCallback } from "react"
import { useToast } from "@/components/ui/use-toast"

interface UseAsyncOperationOptions {
  successMessage?: string
  errorMessage?: string
  onSuccess?: (data?: any) => void
  onError?: (error: Error) => void
}

export function useAsyncOperation<T = any>(options: UseAsyncOperationOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [data, setData] = useState<T | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()

  const execute = useCallback(
    async (operation: () => Promise<T>) => {
      setIsLoading(true)
      setError(null)

      try {
        const result = await operation()
        setData(result)

        if (options.successMessage) {
          toast({
            title: "Success",
            description: options.successMessage,
            className: "bg-green-50 border-green-200 text-green-800",
          })
        }

        options.onSuccess?.(result)
        return result
      } catch (err) {
        const error = err instanceof Error ? err : new Error("An error occurred")
        setError(error)

        toast({
          title: "Error",
          description: options.errorMessage || error.message,
          variant: "destructive",
          className: "bg-red-50 border-red-200 text-red-800",
        })

        options.onError?.(error)
        // --- removed: throw error ---
      } finally {
        setIsLoading(false)
      }
    },
    [toast, options],
  )

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    execute,
    isLoading,
    data,
    error,
    reset,
  }
}
