-- Fleet Management System - Initial Schema Migration
-- This migration creates the complete database schema for the fleet management system

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better data validation
CREATE TYPE user_role AS ENUM ('Super Admin', 'Manager', 'Employee');
CREATE TYPE user_status AS ENUM ('Active', 'Inactive', 'Suspended');
CREATE TYPE branch_status AS ENUM ('Active', 'Inactive');
CREATE TYPE driver_status AS ENUM ('Available', 'Active', 'Inactive', 'On Leave');
CREATE TYPE vehicle_status AS ENUM ('Active', 'Inactive', 'Maintenance', 'Out of Service');
CREATE TYPE maintenance_status AS ENUM ('Scheduled', 'In Progress', 'Completed', 'Cancelled');

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 1. Branches table (must be created first due to foreign key dependencies)
CREATE TABLE branches (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    location TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    email TEXT,
    manager_id UUID, -- Will be linked after profiles table is created
    branch_status branch_status DEFAULT 'Active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role user_role NOT NULL,
    branch_id UUID REFERENCES branches(id),
    user_status user_status DEFAULT 'Active',
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add foreign key constraint for branch manager after profiles table exists
ALTER TABLE branches ADD CONSTRAINT fk_branches_manager 
    FOREIGN KEY (manager_id) REFERENCES profiles(id);

-- 3. Drivers table
CREATE TABLE drivers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    code SERIAL UNIQUE NOT NULL, -- Keep for compatibility with existing system
    full_name TEXT NOT NULL,
    license_number TEXT UNIQUE NOT NULL,
    license_expiry DATE NOT NULL,
    phone TEXT NOT NULL,
    email TEXT UNIQUE,
    status driver_status DEFAULT 'Available',
    hire_date DATE NOT NULL,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_license_expiry_future CHECK (license_expiry > CURRENT_DATE),
    CONSTRAINT chk_hire_date_past CHECK (hire_date <= CURRENT_DATE)
);

-- 4. Vehicles table
CREATE TABLE vehicles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    plate_number TEXT UNIQUE NOT NULL,
    vin_number TEXT UNIQUE,
    model INTEGER NOT NULL,
    vehicle_type TEXT NOT NULL,
    service_type TEXT NOT NULL,
    department TEXT NOT NULL,
    fuel_type TEXT NOT NULL,
    tank_capacity_liters DECIMAL(8,2),
    engine_cc INTEGER,
    color TEXT,
    current_km INTEGER DEFAULT 0,
    branch_id UUID REFERENCES branches(id) NOT NULL,
    assigned_driver_id UUID REFERENCES drivers(id),
    vehicle_status vehicle_status DEFAULT 'Active',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_current_km_positive CHECK (current_km >= 0),
    CONSTRAINT chk_tank_capacity_positive CHECK (tank_capacity_liters > 0),
    CONSTRAINT chk_engine_cc_positive CHECK (engine_cc > 0),
    CONSTRAINT chk_model_year_valid CHECK (model >= 1900 AND model <= EXTRACT(YEAR FROM CURRENT_DATE) + 1)
);

-- 5. Maintenance records table
CREATE TABLE maintenance_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE NOT NULL,
    maintenance_type TEXT NOT NULL,
    maintenance_status maintenance_status DEFAULT 'Scheduled',
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    cost DECIMAL(10,2) DEFAULT 0,
    description TEXT,
    technician TEXT,
    next_maintenance_km INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_cost_positive CHECK (cost >= 0),
    CONSTRAINT chk_completed_after_scheduled CHECK (
        completed_date IS NULL OR completed_date >= scheduled_date
    ),
    CONSTRAINT chk_next_maintenance_km_positive CHECK (next_maintenance_km > 0)
);

-- 6. Fuel records table
CREATE TABLE fuel_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    quantity_liters DECIMAL(8,2) NOT NULL,
    distance_km DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2) NOT NULL,
    station TEXT NOT NULL,
    consumption_per_100km DECIMAL(6,2) GENERATED ALWAYS AS (
        CASE 
            WHEN distance_km > 0 THEN quantity_liters * 100.0 / distance_km
            ELSE NULL
        END
    ) STORED,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT chk_quantity_positive CHECK (quantity_liters > 0),
    CONSTRAINT chk_distance_positive CHECK (distance_km > 0),
    CONSTRAINT chk_cost_positive CHECK (cost > 0),
    CONSTRAINT chk_date_not_future CHECK (date <= CURRENT_DATE)
);

-- Create indexes for better performance
CREATE INDEX idx_profiles_branch_id ON profiles(branch_id);
CREATE INDEX idx_profiles_role ON profiles(role);
CREATE INDEX idx_profiles_email ON profiles(email);

CREATE INDEX idx_drivers_branch_id ON drivers(branch_id);
CREATE INDEX idx_drivers_status ON drivers(status);
CREATE INDEX idx_drivers_license_expiry ON drivers(license_expiry);
CREATE INDEX idx_drivers_code ON drivers(code);

CREATE INDEX idx_vehicles_branch_id ON vehicles(branch_id);
CREATE INDEX idx_vehicles_assigned_driver_id ON vehicles(assigned_driver_id);
CREATE INDEX idx_vehicles_status ON vehicles(vehicle_status);
CREATE INDEX idx_vehicles_plate_number ON vehicles(plate_number);

CREATE INDEX idx_maintenance_vehicle_id ON maintenance_records(vehicle_id);
CREATE INDEX idx_maintenance_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX idx_maintenance_status ON maintenance_records(maintenance_status);

CREATE INDEX idx_fuel_vehicle_id ON fuel_records(vehicle_id);
CREATE INDEX idx_fuel_date ON fuel_records(date);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_branches_updated_at 
    BEFORE UPDATE ON branches 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drivers_updated_at 
    BEFORE UPDATE ON drivers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vehicles_updated_at 
    BEFORE UPDATE ON vehicles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_maintenance_updated_at 
    BEFORE UPDATE ON maintenance_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fuel_updated_at 
    BEFORE UPDATE ON fuel_records 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a function to handle new user registration
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (id, full_name, email, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'role', 'Employee')::user_role
    );
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Trigger to automatically create profile when user signs up
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create a view for dashboard data (optimized for performance)
CREATE VIEW dashboard_view AS
SELECT 
    v.id as vehicle_id,
    v.plate_number,
    v.model,
    v.vehicle_type,
    v.service_type,
    v.vehicle_status,
    v.current_km,
    b.name as branch_name,
    v.department,
    d.full_name as assigned_driver,
    d.license_expiry,
    CASE 
        WHEN d.license_expiry <= CURRENT_DATE THEN 0
        ELSE d.license_expiry - CURRENT_DATE
    END as license_days_remaining,
    -- Fuel statistics
    COALESCE(fuel_stats.total_fuel_liters, 0) as total_fuel_liters,
    COALESCE(fuel_stats.total_fuel_cost, 0) as total_fuel_cost,
    CASE 
        WHEN v.current_km > 0 AND fuel_stats.total_fuel_cost > 0 
        THEN fuel_stats.total_fuel_cost / v.current_km
        ELSE 0
    END as fuel_efficiency_cost_per_km,
    -- Maintenance statistics
    COALESCE(maint_stats.total_maintenance_cost, 0) as total_maintenance_cost,
    maint_stats.last_maintenance_km,
    maint_stats.next_maintenance_km
FROM vehicles v
LEFT JOIN branches b ON v.branch_id = b.id
LEFT JOIN drivers d ON v.assigned_driver_id = d.id
LEFT JOIN (
    SELECT 
        vehicle_id,
        SUM(quantity_liters) as total_fuel_liters,
        SUM(cost) as total_fuel_cost
    FROM fuel_records
    GROUP BY vehicle_id
) fuel_stats ON v.id = fuel_stats.vehicle_id
LEFT JOIN (
    SELECT 
        vehicle_id,
        SUM(cost) as total_maintenance_cost,
        MAX(CASE WHEN maintenance_status = 'Completed' THEN next_maintenance_km END) as last_maintenance_km,
        MIN(CASE WHEN maintenance_status IN ('Scheduled', 'In Progress') THEN next_maintenance_km END) as next_maintenance_km
    FROM maintenance_records
    GROUP BY vehicle_id
) maint_stats ON v.id = maint_stats.vehicle_id;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
