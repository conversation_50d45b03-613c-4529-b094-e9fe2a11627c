-- Fleet Management System - Row Level Security Setup
-- This migration sets up comprehensive Row Level Security policies

-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel_records ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user's profile
CREATE OR REPLACE FUNCTION get_current_user_profile()
RETURNS profiles AS $$
DECLARE
    user_profile profiles;
BEGIN
    SELECT * INTO user_profile
    FROM profiles
    WHERE id = auth.uid();
    
    RETURN user_profile;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is Super Admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role = 'Super Admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is Manager of specific branch
CREATE OR REPLACE FUNCTION is_branch_manager(branch_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() 
        AND role = 'Manager' 
        AND branch_id = branch_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user's branch_id
CREATE OR REPLACE FUNCTION get_user_branch_id()
RETURNS UUID AS $$
DECLARE
    user_branch_id UUID;
BEGIN
    SELECT branch_id INTO user_branch_id
    FROM profiles
    WHERE id = auth.uid();
    
    RETURN user_branch_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- PROFILES TABLE POLICIES
-- =====================================================

-- Super Admin can view all profiles
CREATE POLICY "Super Admin can view all profiles" ON profiles
    FOR SELECT USING (is_super_admin());

-- Manager can view profiles in their branch
CREATE POLICY "Manager can view branch profiles" ON profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = profiles.branch_id
        )
    );

-- Employee can view their own profile
CREATE POLICY "Employee can view own profile" ON profiles
    FOR SELECT USING (id = auth.uid());

-- Super Admin can insert/update/delete all profiles
CREATE POLICY "Super Admin can manage all profiles" ON profiles
    FOR ALL USING (is_super_admin());

-- Manager can update profiles in their branch (except role changes)
CREATE POLICY "Manager can update branch profiles" ON profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = profiles.branch_id
        )
    );

-- Users can update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (id = auth.uid())
    WITH CHECK (
        id = auth.uid() 
        AND role = (SELECT role FROM profiles WHERE id = auth.uid()) -- Prevent role change
    );

-- =====================================================
-- BRANCHES TABLE POLICIES
-- =====================================================

-- Super Admin can view all branches
CREATE POLICY "Super Admin can view all branches" ON branches
    FOR SELECT USING (is_super_admin());

-- Manager can view their own branch
CREATE POLICY "Manager can view own branch" ON branches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = branches.id
        )
    );

-- Employee can view their branch
CREATE POLICY "Employee can view own branch" ON branches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.branch_id = branches.id
        )
    );

-- Super Admin can manage all branches
CREATE POLICY "Super Admin can manage all branches" ON branches
    FOR ALL USING (is_super_admin());

-- =====================================================
-- DRIVERS TABLE POLICIES
-- =====================================================

-- Super Admin can view all drivers
CREATE POLICY "Super Admin can view all drivers" ON drivers
    FOR SELECT USING (is_super_admin());

-- Manager can view drivers in their branch
CREATE POLICY "Manager can view branch drivers" ON drivers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = drivers.branch_id
        )
    );

-- Employee can view drivers in their branch
CREATE POLICY "Employee can view branch drivers" ON drivers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.branch_id = drivers.branch_id
        )
    );

-- Super Admin can manage all drivers
CREATE POLICY "Super Admin can manage all drivers" ON drivers
    FOR ALL USING (is_super_admin());

-- Manager can manage drivers in their branch
CREATE POLICY "Manager can manage branch drivers" ON drivers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = drivers.branch_id
        )
    );

-- =====================================================
-- VEHICLES TABLE POLICIES
-- =====================================================

-- Super Admin can view all vehicles
CREATE POLICY "Super Admin can view all vehicles" ON vehicles
    FOR SELECT USING (is_super_admin());

-- Manager can view vehicles in their branch
CREATE POLICY "Manager can view branch vehicles" ON vehicles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = vehicles.branch_id
        )
    );

-- Employee can view vehicles in their branch
CREATE POLICY "Employee can view branch vehicles" ON vehicles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.branch_id = vehicles.branch_id
        )
    );

-- Super Admin can manage all vehicles
CREATE POLICY "Super Admin can manage all vehicles" ON vehicles
    FOR ALL USING (is_super_admin());

-- Manager can manage vehicles in their branch
CREATE POLICY "Manager can manage branch vehicles" ON vehicles
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid() 
            AND p.role = 'Manager' 
            AND p.branch_id = vehicles.branch_id
        )
    );

-- =====================================================
-- MAINTENANCE RECORDS TABLE POLICIES
-- =====================================================

-- Super Admin can view all maintenance records
CREATE POLICY "Super Admin can view all maintenance" ON maintenance_records
    FOR SELECT USING (is_super_admin());

-- Manager can view maintenance records for vehicles in their branch
CREATE POLICY "Manager can view branch maintenance" ON maintenance_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = maintenance_records.vehicle_id
            AND p.id = auth.uid() 
            AND p.role = 'Manager'
        )
    );

-- Employee can view maintenance records for vehicles in their branch
CREATE POLICY "Employee can view branch maintenance" ON maintenance_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = maintenance_records.vehicle_id
            AND p.id = auth.uid()
        )
    );

-- Super Admin can manage all maintenance records
CREATE POLICY "Super Admin can manage all maintenance" ON maintenance_records
    FOR ALL USING (is_super_admin());

-- Manager can manage maintenance records for vehicles in their branch
CREATE POLICY "Manager can manage branch maintenance" ON maintenance_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = maintenance_records.vehicle_id
            AND p.id = auth.uid() 
            AND p.role = 'Manager'
        )
    );

-- =====================================================
-- FUEL RECORDS TABLE POLICIES
-- =====================================================

-- Super Admin can view all fuel records
CREATE POLICY "Super Admin can view all fuel" ON fuel_records
    FOR SELECT USING (is_super_admin());

-- Manager can view fuel records for vehicles in their branch
CREATE POLICY "Manager can view branch fuel" ON fuel_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = fuel_records.vehicle_id
            AND p.id = auth.uid() 
            AND p.role = 'Manager'
        )
    );

-- Employee can view fuel records for vehicles in their branch
CREATE POLICY "Employee can view branch fuel" ON fuel_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = fuel_records.vehicle_id
            AND p.id = auth.uid()
        )
    );

-- Super Admin can manage all fuel records
CREATE POLICY "Super Admin can manage all fuel" ON fuel_records
    FOR ALL USING (is_super_admin());

-- Manager can manage fuel records for vehicles in their branch
CREATE POLICY "Manager can manage branch fuel" ON fuel_records
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = fuel_records.vehicle_id
            AND p.id = auth.uid() 
            AND p.role = 'Manager'
        )
    );

-- =====================================================
-- ADDITIONAL SECURITY FUNCTIONS
-- =====================================================

-- Function to check if user can access vehicle data
CREATE OR REPLACE FUNCTION can_access_vehicle(vehicle_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        is_super_admin() OR
        EXISTS (
            SELECT 1 FROM vehicles v
            JOIN profiles p ON p.branch_id = v.branch_id
            WHERE v.id = vehicle_uuid
            AND p.id = auth.uid()
        )
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM profiles
    WHERE id = auth.uid();

    RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant basic permissions to authenticated users
GRANT SELECT ON profiles TO authenticated;
GRANT SELECT ON branches TO authenticated;
GRANT SELECT ON drivers TO authenticated;
GRANT SELECT ON vehicles TO authenticated;
GRANT SELECT ON maintenance_records TO authenticated;
GRANT SELECT ON fuel_records TO authenticated;

-- Grant insert/update/delete permissions (will be controlled by RLS)
GRANT INSERT, UPDATE, DELETE ON profiles TO authenticated;
GRANT INSERT, UPDATE, DELETE ON branches TO authenticated;
GRANT INSERT, UPDATE, DELETE ON drivers TO authenticated;
GRANT INSERT, UPDATE, DELETE ON vehicles TO authenticated;
GRANT INSERT, UPDATE, DELETE ON maintenance_records TO authenticated;
GRANT INSERT, UPDATE, DELETE ON fuel_records TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
