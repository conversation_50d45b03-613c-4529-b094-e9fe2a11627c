#!/usr/bin/env node

/**
 * Fleet Management System - API Imports Updater
 * Updates all imports from api-client to supabase-api-client
 */

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Get all TypeScript/JavaScript files recursively
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath)

  files.forEach(file => {
    const fullPath = path.join(dirPath, file)
    
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and .git
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(file)) {
        arrayOfFiles = getAllFiles(fullPath, arrayOfFiles)
      }
    } else {
      // Only check TypeScript/JavaScript files
      if (/\.(ts|tsx|js|jsx)$/.test(file)) {
        arrayOfFiles.push(fullPath)
      }
    }
  })

  return arrayOfFiles
}

// Update imports in a file
function updateFileImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let updatedContent = content
    let hasChanges = false

    // Replace api-client imports with supabase-api-client
    const apiClientImportRegex = /from ['"]@\/lib\/api-client['"]/g
    if (apiClientImportRegex.test(content)) {
      updatedContent = updatedContent.replace(apiClientImportRegex, 'from "@/lib/supabase-api-client"')
      hasChanges = true
    }

    // Replace supabaseApiClient usage with supabaseApiClient
    const apiClientUsageRegex = /\bapiClient\b/g
    if (apiClientUsageRegex.test(content)) {
      updatedContent = updatedContent.replace(apiClientUsageRegex, 'supabaseApiClient')
      hasChanges = true
    }

    // Update specific type imports that might have changed
    const typeUpdates = [
      { from: /\bvehicle_id\b/g, to: 'id' },
      { from: /\bdriver_id\b/g, to: 'id' },
      { from: /\bbranch_id\b/g, to: 'id' },
      { from: /\buser_id\b/g, to: 'id' },
      { from: /\bmaintenance_id\b/g, to: 'id' },
      { from: /\bfuel_id\b/g, to: 'id' }
    ]

    for (const update of typeUpdates) {
      if (update.from.test(content)) {
        // Only update in specific contexts (like find operations)
        const findRegex = new RegExp(`\\.find\\([^)]*${update.from.source}[^)]*\\)`, 'g')
        if (findRegex.test(content)) {
          updatedContent = updatedContent.replace(findRegex, (match) => {
            return match.replace(update.from, update.to)
          })
          hasChanges = true
        }
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, updatedContent, 'utf8')
      return true
    }

    return false
  } catch (error) {
    log(`   ❌ Error updating ${filePath}: ${error.message}`, 'red')
    return false
  }
}

// Main function to update all files
function updateAllApiImports() {
  log('🔄 Fleet Management System - API Imports Updater', 'bold')
  log('=' .repeat(60), 'blue')

  const projectRoot = process.cwd()
  const allFiles = getAllFiles(projectRoot)
  
  log(`\n🔍 Scanning ${allFiles.length} files for api-client imports...`, 'blue')
  
  let updatedFiles = 0
  let totalUpdates = 0

  for (const filePath of allFiles) {
    const relativePath = path.relative(projectRoot, filePath)
    
    // Skip certain files
    if (relativePath.includes('node_modules') || 
        relativePath.includes('.git') ||
        relativePath.includes('scripts/update-api-imports.js') ||
        relativePath.includes('lib/api-client.ts') ||
        relativePath.includes('lib/supabase-api-client.ts')) {
      continue
    }

    const wasUpdated = updateFileImports(filePath)
    if (wasUpdated) {
      log(`   ✅ Updated: ${relativePath}`, 'green')
      updatedFiles++
      totalUpdates++
    }
  }

  // Summary
  log('\n' + '=' .repeat(60), 'blue')
  log('📊 API Imports Update Summary', 'bold')
  log('=' .repeat(60), 'blue')

  if (updatedFiles === 0) {
    log('✅ No files needed updating - all imports are already correct!', 'green')
  } else {
    log(`✅ Updated ${updatedFiles} files successfully`, 'green')
    log(`📝 Total updates applied: ${totalUpdates}`, 'blue')
  }

  log('\n🎯 Next Steps:', 'blue')
  log('1. Run: npm run type-check (to verify TypeScript)', 'blue')
  log('2. Run: npm run lint (to check for any issues)', 'blue')
  log('3. Test the application to ensure everything works', 'blue')

  return updatedFiles
}

// Main execution
function main() {
  try {
    const updatedCount = updateAllApiImports()
    
    if (updatedCount > 0) {
      log('\n🚀 API imports updated successfully!', 'green')
      log('Please test the application to ensure everything works correctly.', 'yellow')
    } else {
      log('\n✨ All API imports are already up to date!', 'green')
    }
    
    process.exit(0)
  } catch (error) {
    log(`\n❌ Update failed: ${error.message}`, 'red')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { updateAllApiImports }
