"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useSupabaseAuth } from '@/context/supabase-auth-context'
import { Loader2 } from 'lucide-react'

interface SupabaseProtectedRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  requiredRole?: 'Super Admin' | 'Manager' | 'Employee'
  fallbackPath?: string
}

export function SupabaseProtectedRoute({ 
  children, 
  requiredPermission, 
  requiredRole,
  fallbackPath = '/login' 
}: SupabaseProtectedRouteProps) {
  const { user, loading, hasPermission } = useSupabaseAuth()
  const router = useRouter()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (!loading) {
      if (!user) {
        // User not authenticated, redirect to login
        router.push(fallbackPath)
        return
      }

      // Check if user status is active
      if (user.user_status !== 'Active') {
        router.push('/unauthorized?reason=inactive')
        return
      }

      // Check required role
      if (requiredRole && user.role !== requiredRole) {
        // For role-based access, check if user has higher privileges
        const roleHierarchy = {
          'Employee': 1,
          'Manager': 2,
          'Super Admin': 3
        }
        
        const userLevel = roleHierarchy[user.role]
        const requiredLevel = roleHierarchy[requiredRole]
        
        if (userLevel < requiredLevel) {
          router.push('/unauthorized?reason=insufficient_role')
          return
        }
      }

      // Check required permission
      if (requiredPermission && !hasPermission(requiredPermission)) {
        router.push('/unauthorized?reason=insufficient_permission')
        return
      }

      setIsChecking(false)
    }
  }, [user, loading, requiredPermission, requiredRole, router, hasPermission, fallbackPath])

  // Show loading spinner while checking authentication
  if (loading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    )
  }

  // If we reach here, user is authenticated and authorized
  return <>{children}</>
}

// Higher-order component for page-level protection
export function withSupabaseAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredPermission?: string
    requiredRole?: 'Super Admin' | 'Manager' | 'Employee'
    fallbackPath?: string
  }
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <SupabaseProtectedRoute {...options}>
        <Component {...props} />
      </SupabaseProtectedRoute>
    )
  }
}

// Role-based component wrapper
export function RoleBasedAccess({ 
  children, 
  allowedRoles, 
  fallback 
}: {
  children: React.ReactNode
  allowedRoles: ('Super Admin' | 'Manager' | 'Employee')[]
  fallback?: React.ReactNode
}) {
  const { user } = useSupabaseAuth()

  if (!user || !allowedRoles.includes(user.role)) {
    return fallback || null
  }

  return <>{children}</>
}

// Permission-based component wrapper
export function PermissionBasedAccess({ 
  children, 
  requiredPermission, 
  fallback 
}: {
  children: React.ReactNode
  requiredPermission: string
  fallback?: React.ReactNode
}) {
  const { hasPermission } = useSupabaseAuth()

  if (!hasPermission(requiredPermission)) {
    return fallback || null
  }

  return <>{children}</>
}

// Branch-based access control
export function BranchBasedAccess({ 
  children, 
  allowedBranches, 
  fallback 
}: {
  children: React.ReactNode
  allowedBranches?: string[]
  fallback?: React.ReactNode
}) {
  const { user } = useSupabaseAuth()

  // Super Admin can access all branches
  if (user?.role === 'Super Admin') {
    return <>{children}</>
  }

  // If no specific branches are required, allow access
  if (!allowedBranches || allowedBranches.length === 0) {
    return <>{children}</>
  }

  // Check if user's branch is in allowed branches
  if (!user?.branch_id || !allowedBranches.includes(user.branch_id)) {
    return fallback || null
  }

  return <>{children}</>
}
