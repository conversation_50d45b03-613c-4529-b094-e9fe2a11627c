#!/usr/bin/env node

/**
 * Fleet Management System - Installation Fixer
 * Fixes common installation issues
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, description) {
  log(`🔧 ${description}...`, 'blue')
  try {
    execSync(command, { stdio: 'inherit' })
    log(`   ✅ ${description} completed`, 'green')
    return true
  } catch (error) {
    log(`   ❌ ${description} failed: ${error.message}`, 'red')
    return false
  }
}

async function fixInstallation() {
  log('🔧 Fleet Management System - Installation Fixer', 'bold')
  log('=' .repeat(50), 'blue')

  // Check if package.json exists
  const packageJsonPath = path.join(process.cwd(), 'package.json')
  if (!fs.existsSync(packageJsonPath)) {
    log('❌ package.json not found!', 'red')
    return false
  }

  // Read package.json to check dependencies
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  const hasSupabase = packageJson.dependencies && packageJson.dependencies['@supabase/supabase-js']

  log('\n📋 Diagnosis:', 'blue')
  log(`   Package.json: ✅ Found`, 'green')
  log(`   Supabase in deps: ${hasSupabase ? '✅ Yes' : '❌ No'}`, hasSupabase ? 'green' : 'red')

  // Check node_modules
  const nodeModulesPath = path.join(process.cwd(), 'node_modules')
  const supabaseModulePath = path.join(nodeModulesPath, '@supabase', 'supabase-js')
  
  log(`   node_modules: ${fs.existsSync(nodeModulesPath) ? '✅ Exists' : '❌ Missing'}`, 
      fs.existsSync(nodeModulesPath) ? 'green' : 'red')
  log(`   @supabase/supabase-js: ${fs.existsSync(supabaseModulePath) ? '✅ Installed' : '❌ Missing'}`, 
      fs.existsSync(supabaseModulePath) ? 'green' : 'red')

  // Fix steps
  log('\n🔧 Applying fixes...', 'blue')

  // Step 1: Clean install
  if (!fs.existsSync(supabaseModulePath)) {
    log('\n1. Installing missing @supabase/supabase-js...', 'yellow')
    
    const installSuccess = runCommand(
      'npm install @supabase/supabase-js', 
      'Installing @supabase/supabase-js'
    )
    
    if (!installSuccess) {
      log('\n   Trying alternative installation methods...', 'yellow')
      
      // Try with --force
      runCommand(
        'npm install @supabase/supabase-js --force', 
        'Installing with --force'
      )
    }
  }

  // Step 2: Install all dependencies
  log('\n2. Ensuring all dependencies are installed...', 'yellow')
  runCommand('npm install', 'Installing all dependencies')

  // Step 3: Verify installation
  log('\n3. Verifying installation...', 'yellow')
  const finalCheck = fs.existsSync(path.join(nodeModulesPath, '@supabase', 'supabase-js'))
  
  if (finalCheck) {
    log('   ✅ @supabase/supabase-js is now installed!', 'green')
  } else {
    log('   ❌ @supabase/supabase-js still missing', 'red')
    log('\n🚨 Manual fix required:', 'red')
    log('   1. Delete node_modules and package-lock.json', 'yellow')
    log('   2. Run: npm cache clean --force', 'yellow')
    log('   3. Run: npm install', 'yellow')
    return false
  }

  // Step 4: Check environment variables
  log('\n4. Checking environment variables...', 'yellow')
  const envPath = path.join(process.cwd(), '.env.local')
  
  if (fs.existsSync(envPath)) {
    log('   ✅ .env.local exists', 'green')
    
    // Load and check env vars
    const envContent = fs.readFileSync(envPath, 'utf8')
    const hasUrl = envContent.includes('NEXT_PUBLIC_SUPABASE_URL=https://')
    const hasKey = envContent.includes('NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ')
    
    log(`   Supabase URL: ${hasUrl ? '✅ Set' : '❌ Missing'}`, hasUrl ? 'green' : 'red')
    log(`   Supabase Key: ${hasKey ? '✅ Set' : '❌ Missing'}`, hasKey ? 'green' : 'red')
    
    if (!hasUrl || !hasKey) {
      log('\n   🔧 Fixing environment variables...', 'blue')
      
      let newEnvContent = envContent
      
      if (!hasUrl) {
        newEnvContent += '\nNEXT_PUBLIC_SUPABASE_URL=https://vjozjofhwlpskbgbgzve.supabase.co'
      }
      
      if (!hasKey) {
        newEnvContent += '\nNEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxNDcyMTgsImV4cCI6MjA2ODcyMzIxOH0.bKGKnGCZxP5PootP06OVKxmRQvR3nWSkVKX4GI5d1Ko'
        newEnvContent += '\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE0NzIxOCwiZXhwIjoyMDY4NzIzMjE4fQ.lL5Dw3NPc8HPQS6_pi5-hnDUsexj4Ao9EOXxvuIzZaU'
      }
      
      fs.writeFileSync(envPath, newEnvContent)
      log('   ✅ Environment variables updated', 'green')
    }
  } else {
    log('   ❌ .env.local missing', 'red')
    log('   🔧 Creating .env.local...', 'blue')
    
    const envContent = `# Fleet Management System - Environment Variables

NEXT_PUBLIC_SUPABASE_URL=https://vjozjofhwlpskbgbgzve.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxNDcyMTgsImV4cCI6MjA2ODcyMzIxOH0.bKGKnGCZxP5PootP06OVKxmRQvR3nWSkVKX4GI5d1Ko
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE0NzIxOCwiZXhwIjoyMDY4NzIzMjE4fQ.lL5Dw3NPc8HPQS6_pi5-hnDUsexj4Ao9EOXxvuIzZaU
SUPABASE_JWT_SECRET=NRWC5/yeFpryiZvL5y7FbZqRJ1jvWUtBKxVzygRr1Q4eVXu8ETIXkYF0IxkHxSS+EsFYKGIPvw2rjcqTQwVpEg==

NODE_ENV=development
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=FleetManagementSystemSecret2024
`
    
    fs.writeFileSync(envPath, envContent)
    log('   ✅ .env.local created with Supabase credentials', 'green')
  }

  // Final summary
  log('\n' + '=' .repeat(50), 'blue')
  log('🎉 Installation Fix Complete!', 'bold')
  log('=' .repeat(50), 'blue')
  
  log('\n✅ What was fixed:', 'green')
  log('   - @supabase/supabase-js installed', 'green')
  log('   - Environment variables configured', 'green')
  log('   - Dependencies verified', 'green')
  
  log('\n🚀 Next steps:', 'blue')
  log('1. Apply seed data: node scripts/apply-seed-direct.js apply', 'blue')
  log('2. Or use npm commands: npm run seed', 'blue')
  log('3. Start development: npm run dev', 'blue')
  
  return true
}

// Quick test function
function testInstallation() {
  log('🧪 Testing installation...', 'blue')
  
  try {
    require('@supabase/supabase-js')
    log('   ✅ @supabase/supabase-js can be imported', 'green')
    return true
  } catch (error) {
    log('   ❌ @supabase/supabase-js import failed', 'red')
    return false
  }
}

// Main execution
async function main() {
  const command = process.argv[2]

  switch (command) {
    case 'fix':
      await fixInstallation()
      break
    
    case 'test':
      testInstallation()
      break
    
    default:
      log('Fleet Management System - Installation Fixer', 'bold')
      log('')
      log('Usage: node fix-installation.js <command>')
      log('')
      log('Commands:')
      log('  fix   - Fix installation issues')
      log('  test  - Test if installation works')
      log('')
      log('Example:')
      log('  node scripts/fix-installation.js fix')
      
      // Auto-run fix if no command provided
      log('\nAuto-running fix...', 'yellow')
      await fixInstallation()
  }
}

if (require.main === module) {
  main().catch(error => {
    log(`❌ Fix failed: ${error.message}`, 'red')
    process.exit(1)
  })
}

module.exports = { fixInstallation, testInstallation }
