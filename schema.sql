-- Fleet Management System - Supabase Schema
-- Enhanced PostgreSQL schema with optimizations and best practices

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON>reate custom types
CREATE TYPE vehicle_status_enum AS ENUM ('active', 'inactive', 'maintenance', 'retired');
CREATE TYPE driver_status_enum AS ENUM ('active', 'inactive', 'on_leave', 'terminated');
CREATE TYPE maintenance_status_enum AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE user_role_enum AS ENUM ('admin', 'manager', 'operator', 'viewer');
CREATE TYPE user_status_enum AS ENUM ('active', 'inactive', 'suspended');
CREATE TYPE branch_status_enum AS ENUM ('active', 'inactive', 'closed');
CREATE TYPE fuel_type_enum AS ENUM ('gasoline', 'diesel', 'hybrid', 'electric', 'cng');

-- =============================================
-- CORE TABLES
-- =============================================

-- Branches table (foundational)
CREATE TABLE branches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    branch_id VARCHAR(50) UNIQUE NOT NULL, -- Keep original ID for compatibility
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    status branch_status_enum DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT branches_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT branches_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL)
);

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    user_id VARCHAR(50) UNIQUE NOT NULL, -- Keep original ID for compatibility
    full_name VARCHAR(255) NOT NULL,
    role user_role_enum DEFAULT 'viewer',
    branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
    status user_status_enum DEFAULT 'active',
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT users_full_name_not_empty CHECK (LENGTH(TRIM(full_name)) > 0)
);

-- Drivers table
CREATE TABLE drivers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL, -- Keep original code for compatibility
    full_name VARCHAR(255) NOT NULL,
    license_number VARCHAR(100) UNIQUE NOT NULL,
    license_expiry DATE NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    hire_date DATE,
    status driver_status_enum DEFAULT 'active',
    branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
    emergency_contact JSONB, -- Store emergency contact info
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT drivers_full_name_not_empty CHECK (LENGTH(TRIM(full_name)) > 0),
    CONSTRAINT drivers_license_expiry_future CHECK (license_expiry > CURRENT_DATE - INTERVAL '1 year'),
    CONSTRAINT drivers_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' OR email IS NULL)
);

-- Vehicles table
CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    vehicle_id VARCHAR(50) UNIQUE NOT NULL, -- Keep original ID for compatibility
    plate_number VARCHAR(20) UNIQUE NOT NULL,
    vin_number VARCHAR(17) UNIQUE NOT NULL,
    model_year INTEGER NOT NULL,
    make VARCHAR(100), -- Added make field
    model VARCHAR(100), -- Changed from year to model name
    vehicle_type VARCHAR(100) NOT NULL,
    service_type VARCHAR(100) NOT NULL,
    department VARCHAR(100) NOT NULL,
    fuel_type fuel_type_enum NOT NULL,
    tank_capacity_liters DECIMAL(8,2),
    engine_cc INTEGER,
    color VARCHAR(50) NOT NULL,
    current_km INTEGER DEFAULT 0,
    purchase_date DATE,
    purchase_price DECIMAL(12,2),
    insurance_expiry DATE,
    registration_expiry DATE,
    branch_id UUID REFERENCES branches(id) ON DELETE SET NULL,
    assigned_driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    status vehicle_status_enum DEFAULT 'active',
    specifications JSONB DEFAULT '{}', -- Store additional specs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT vehicles_model_year_valid CHECK (model_year BETWEEN 1900 AND EXTRACT(YEAR FROM NOW()) + 2),
    CONSTRAINT vehicles_current_km_positive CHECK (current_km >= 0),
    CONSTRAINT vehicles_tank_capacity_positive CHECK (tank_capacity_liters > 0 OR tank_capacity_liters IS NULL),
    CONSTRAINT vehicles_engine_cc_positive CHECK (engine_cc > 0 OR engine_cc IS NULL),
    CONSTRAINT vehicles_vin_length CHECK (LENGTH(vin_number) = 17)
);

-- Maintenance records table
CREATE TABLE maintenance_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    maintenance_id VARCHAR(50) UNIQUE NOT NULL, -- Keep original ID for compatibility
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL,
    status maintenance_status_enum DEFAULT 'scheduled',
    scheduled_date DATE NOT NULL,
    completed_date DATE,
    cost DECIMAL(10,2) DEFAULT 0,
    description TEXT,
    technician VARCHAR(255),
    service_provider VARCHAR(255),
    current_km INTEGER,
    next_maintenance_km INTEGER,
    parts_replaced JSONB DEFAULT '[]', -- Array of parts
    labor_hours DECIMAL(5,2),
    warranty_expiry DATE,
    attachments JSONB DEFAULT '[]', -- Store file URLs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT maintenance_cost_positive CHECK (cost >= 0),
    CONSTRAINT maintenance_completed_after_scheduled CHECK (completed_date >= scheduled_date OR completed_date IS NULL),
    CONSTRAINT maintenance_current_km_positive CHECK (current_km >= 0 OR current_km IS NULL),
    CONSTRAINT maintenance_next_km_greater CHECK (next_maintenance_km > current_km OR next_maintenance_km IS NULL OR current_km IS NULL)
);

-- Fuel records table
CREATE TABLE fuel_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    fuel_id VARCHAR(50) UNIQUE NOT NULL, -- Keep original ID for compatibility
    vehicle_id UUID REFERENCES vehicles(id) ON DELETE CASCADE,
    driver_id UUID REFERENCES drivers(id) ON DELETE SET NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    quantity_liters DECIMAL(8,2) NOT NULL,
    distance_km INTEGER,
    cost DECIMAL(10,2) NOT NULL,
    price_per_liter DECIMAL(6,3),
    station VARCHAR(255),
    location VARCHAR(255),
    consumption_per_100km DECIMAL(6,2),
    odometer_reading INTEGER,
    receipt_number VARCHAR(100),
    payment_method VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fuel_quantity_positive CHECK (quantity_liters > 0),
    CONSTRAINT fuel_cost_positive CHECK (cost >= 0),
    CONSTRAINT fuel_distance_positive CHECK (distance_km >= 0 OR distance_km IS NULL),
    CONSTRAINT fuel_consumption_positive CHECK (consumption_per_100km >= 0 OR consumption_per_100km IS NULL),
    CONSTRAINT fuel_odometer_positive CHECK (odometer_reading >= 0 OR odometer_reading IS NULL)
);

-- =============================================
-- CONFIGURATION TABLES
-- =============================================

-- Dropdown configuration table (replaces Google Sheets dropdown admin)
CREATE TABLE dropdown_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category VARCHAR(100) NOT NULL,
    value VARCHAR(255) NOT NULL,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(category, value)
);

-- System settings table
CREATE TABLE system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE, -- Whether setting can be accessed by non-admins
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- AUDIT AND LOGGING TABLES
-- =============================================

-- Audit log table for tracking changes
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- info, warning, error, success
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Branches indexes
CREATE INDEX idx_branches_status ON branches(status);
CREATE INDEX idx_branches_manager ON branches(manager_id);

-- Users indexes
CREATE INDEX idx_users_branch ON users(branch_id);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

-- Drivers indexes
CREATE INDEX idx_drivers_branch ON drivers(branch_id);
CREATE INDEX idx_drivers_status ON drivers(status);
CREATE INDEX idx_drivers_license_expiry ON drivers(license_expiry);
CREATE INDEX idx_drivers_full_name_trgm ON drivers USING gin(full_name gin_trgm_ops);

-- Vehicles indexes
CREATE INDEX idx_vehicles_branch ON vehicles(branch_id);
CREATE INDEX idx_vehicles_driver ON vehicles(assigned_driver_id);
CREATE INDEX idx_vehicles_status ON vehicles(status);
CREATE INDEX idx_vehicles_type ON vehicles(vehicle_type);
CREATE INDEX idx_vehicles_fuel_type ON vehicles(fuel_type);
CREATE INDEX idx_vehicles_plate_trgm ON vehicles USING gin(plate_number gin_trgm_ops);
CREATE INDEX idx_vehicles_model_year ON vehicles(model_year);

-- Maintenance indexes
CREATE INDEX idx_maintenance_vehicle ON maintenance_records(vehicle_id);
CREATE INDEX idx_maintenance_status ON maintenance_records(status);
CREATE INDEX idx_maintenance_scheduled_date ON maintenance_records(scheduled_date);
CREATE INDEX idx_maintenance_type ON maintenance_records(maintenance_type);

-- Fuel indexes
CREATE INDEX idx_fuel_vehicle ON fuel_records(vehicle_id);
CREATE INDEX idx_fuel_driver ON fuel_records(driver_id);
CREATE INDEX idx_fuel_date ON fuel_records(date);
CREATE INDEX idx_fuel_station ON fuel_records(station);

-- Audit logs indexes
CREATE INDEX idx_audit_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_changed_by ON audit_logs(changed_by);
CREATE INDEX idx_audit_changed_at ON audit_logs(changed_at);

-- Notifications indexes
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to all main tables
CREATE TRIGGER update_branches_updated_at BEFORE UPDATE ON branches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_drivers_updated_at BEFORE UPDATE ON drivers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vehicles_updated_at BEFORE UPDATE ON vehicles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_maintenance_updated_at BEFORE UPDATE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_fuel_updated_at BEFORE UPDATE ON fuel_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dropdown_updated_at BEFORE UPDATE ON dropdown_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function for audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (table_name, record_id, action, old_values, changed_by)
        VALUES (TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD), auth.uid());
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (table_name, record_id, action, old_values, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW), auth.uid());
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (table_name, record_id, action, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW), auth.uid());
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to main tables
CREATE TRIGGER audit_branches AFTER INSERT OR UPDATE OR DELETE ON branches FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_drivers AFTER INSERT OR UPDATE OR DELETE ON drivers FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_vehicles AFTER INSERT OR UPDATE OR DELETE ON vehicles FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_maintenance AFTER INSERT OR UPDATE OR DELETE ON maintenance_records FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_fuel AFTER INSERT OR UPDATE OR DELETE ON fuel_records FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Function to calculate fuel consumption
CREATE OR REPLACE FUNCTION calculate_fuel_consumption()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.quantity_liters > 0 AND NEW.distance_km > 0 THEN
        NEW.consumption_per_100km = (NEW.quantity_liters / NEW.distance_km) * 100;
    END IF;
    
    IF NEW.quantity_liters > 0 AND NEW.cost > 0 THEN
        NEW.price_per_liter = NEW.cost / NEW.quantity_liters;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER calculate_fuel_consumption_trigger 
    BEFORE INSERT OR UPDATE ON fuel_records 
    FOR EACH ROW EXECUTE FUNCTION calculate_fuel_consumption();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE branches ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE drivers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE fuel_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE dropdown_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for branches
CREATE POLICY "Users can view branches they belong to" ON branches
    FOR SELECT USING (
        auth.uid() IN (
            SELECT id FROM users WHERE branch_id = branches.id
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Admins and managers can manage branches" ON branches
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

-- RLS Policies for users
CREATE POLICY "Users can view their own profile and branch colleagues" ON users
    FOR SELECT USING (
        id = auth.uid() OR
        branch_id IN (
            SELECT branch_id FROM users WHERE id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage all users" ON users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for drivers (branch-based access)
CREATE POLICY "Branch-based driver access" ON drivers
    FOR SELECT USING (
        branch_id IN (
            SELECT branch_id FROM users WHERE id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Managers can manage drivers in their branch" ON drivers
    FOR ALL USING (
        (branch_id IN (
            SELECT branch_id FROM users WHERE id = auth.uid() AND role IN ('manager', 'admin')
        )) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for vehicles (branch-based access)
CREATE POLICY "Branch-based vehicle access" ON vehicles
    FOR SELECT USING (
        branch_id IN (
            SELECT branch_id FROM users WHERE id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

CREATE POLICY "Managers can manage vehicles in their branch" ON vehicles
    FOR ALL USING (
        (branch_id IN (
            SELECT branch_id FROM users WHERE id = auth.uid() AND role IN ('manager', 'admin')
        )) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for maintenance records
CREATE POLICY "Vehicle-based maintenance access" ON maintenance_records
    FOR SELECT USING (
        vehicle_id IN (
            SELECT id FROM vehicles WHERE 
            branch_id IN (
                SELECT branch_id FROM users WHERE id = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
        )
    );

CREATE POLICY "Operators can manage maintenance in their branch" ON maintenance_records
    FOR ALL USING (
        vehicle_id IN (
            SELECT id FROM vehicles WHERE 
            branch_id IN (
                SELECT branch_id FROM users WHERE id = auth.uid() AND role IN ('operator', 'manager', 'admin')
            )
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for fuel records
CREATE POLICY "Vehicle-based fuel access" ON fuel_records
    FOR SELECT USING (
        vehicle_id IN (
            SELECT id FROM vehicles WHERE 
            branch_id IN (
                SELECT branch_id FROM users WHERE id = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
        )
    );

CREATE POLICY "Operators can manage fuel records in their branch" ON fuel_records
    FOR ALL USING (
        vehicle_id IN (
            SELECT id FROM vehicles WHERE 
            branch_id IN (
                SELECT branch_id FROM users WHERE id = auth.uid() AND role IN ('operator', 'manager', 'admin')
            )
        ) OR
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for dropdown config
CREATE POLICY "All authenticated users can view dropdown config" ON dropdown_config
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage dropdown config" ON dropdown_config
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for system settings
CREATE POLICY "Users can view public settings" ON system_settings
    FOR SELECT USING (is_public = true OR EXISTS (
        SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
    ));

CREATE POLICY "Admins can manage system settings" ON system_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for audit logs
CREATE POLICY "Admins can view all audit logs" ON audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for notifications
CREATE POLICY "Users can view their own notifications" ON notifications
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own notifications" ON notifications
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "System can create notifications" ON notifications
    FOR INSERT WITH CHECK (true);

-- =============================================
-- INITIAL DATA
-- =============================================

-- Insert default dropdown values
INSERT INTO dropdown_config (category, value, display_order) VALUES
-- Vehicle Types
('vehicle_type', 'سيارة', 1),
('vehicle_type', 'شاحنة', 2),
('vehicle_type', 'حافلة', 3),
('vehicle_type', 'دراجة نارية', 4),

-- Service Types
('service_type', 'نقل عام', 1),
('service_type', 'نقل خاص', 2),
('service_type', 'توصيل', 3),
('service_type', 'إسعاف', 4),

-- Fuel Types
('fuel_type', 'بنزين', 1),
('fuel_type', 'ديزل', 2),
('fuel_type', 'هجين', 3),
('fuel_type', 'كهربائي', 4),

-- Colors
('color', 'أبيض', 1),
('color', 'أسود', 2),
('color', 'أزرق', 3),
('color', 'أحمر', 4),
('color', 'فضي', 5),

-- Departments
('department', 'النقل', 1),
('department', 'الصيانة', 2),
('department', 'الإدارة', 3),
('department', 'الطوارئ', 4),

-- Maintenance Types
('maintenance_type', 'صيانة دورية', 1),
('maintenance_type', 'إصلاح', 2),
('maintenance_type', 'تغيير زيت', 3),
('maintenance_type', 'تغيير إطارات', 4),
('maintenance_type', 'فحص شامل', 5);

-- Insert default system settings
INSERT INTO system_settings (key, value, description, is_public) VALUES
('maintenance_reminder_days', '30', 'عدد الأيام للتذكير بالصيانة القادمة', true),
('license_expiry_reminder_days', '60', 'عدد الأيام للتذكير بانتهاء الرخصة', true),
('fuel_efficiency_threshold', '15', 'الحد الأدنى لكفاءة الوقود (لتر/100كم)', true),
('max_file_upload_size', '10485760', 'الحد الأقصى لحجم الملف المرفوع (بايت)', false),
('backup_retention_days', '90', 'عدد الأيام للاحتفاظ بالنسخ الاحتياطية', false);

-- Create views for dashboard and reporting
CREATE VIEW dashboard_summary AS
SELECT 
    b.name as branch_name,
    COUNT(DISTINCT v.id) as total_vehicles,
    COUNT(DISTINCT CASE WHEN v.status = 'active' THEN v.id END) as active_vehicles,
    COUNT(DISTINCT CASE WHEN v.status = 'maintenance' THEN v.id END) as vehicles_in_maintenance,
    COUNT(DISTINCT d.id) as total_drivers,
    COUNT(DISTINCT CASE WHEN d.status = 'active' THEN d.id END) as active_drivers,
    COALESCE(SUM(fr.cost), 0) as total_fuel_cost_this_month,
    COALESCE(SUM(mr.cost), 0) as total_maintenance_cost_this_month
FROM branches b
LEFT JOIN vehicles v ON b.id = v.branch_id
LEFT JOIN drivers d ON b.id = d.branch_id
LEFT JOIN fuel_records fr ON v.id = fr.vehicle_id AND fr.date >= DATE_TRUNC('month', CURRENT_DATE)
LEFT JOIN maintenance_records mr ON v.id = mr.vehicle_id AND mr.completed_date >= DATE_TRUNC('month', CURRENT_DATE)
WHERE b.status = 'active'
GROUP BY b.id, b.name;

-- Create materialized view for performance-critical queries
CREATE MATERIALIZED VIEW vehicle_performance_stats AS
SELECT 
    v.id,
    v.vehicle_id,
    v.plate_number,
    v.model_year,
    v.vehicle_type,
    b.name as branch_name,
    d.full_name as driver_name,
    COALESCE(fuel_stats.total_fuel_cost, 0) as total_fuel_cost,
    COALESCE(fuel_stats.avg_consumption, 0) as avg_fuel_consumption,
    COALESCE(maintenance_stats.total_maintenance_cost, 0) as total_maintenance_cost,
    COALESCE(maintenance_stats.maintenance_count, 0) as maintenance_count,
    v.current_km,
    v.status,
    v.updated_at
FROM vehicles v
LEFT JOIN branches b ON v.branch_id = b.id
LEFT JOIN drivers d ON v.assigned_driver_id = d.id
LEFT JOIN (
    SELECT 
        vehicle_id,
        SUM(cost) as total_fuel_cost,
        AVG(consumption_per_100km) as avg_consumption
    FROM fuel_records 
    WHERE date >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY vehicle_id
) fuel_stats ON v.id = fuel_stats.vehicle_id
LEFT JOIN (
    SELECT 
        vehicle_id,
        SUM(cost) as total_maintenance_cost,
        COUNT(*) as maintenance_count
    FROM maintenance_records 
    WHERE completed_date >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY vehicle_id
) maintenance_stats ON v.id = maintenance_stats.vehicle_id;

-- Create index on materialized view
CREATE INDEX idx_vehicle_performance_stats_branch ON vehicle_performance_stats(branch_name);
CREATE INDEX idx_vehicle_performance_stats_status ON vehicle_performance_stats(status);

-- Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_vehicle_performance_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY vehicle_performance_stats;
END;
$$ LANGUAGE plpgsql;

-- Schedule materialized view refresh (requires pg_cron extension)
-- SELECT cron.schedule('refresh-vehicle-stats', '0 */6 * * *', 'SELECT refresh_vehicle_performance_stats();');

COMMENT ON SCHEMA public IS 'Fleet Management System - Enhanced PostgreSQL Schema with RLS, Audit Logging, and Performance Optimizations';