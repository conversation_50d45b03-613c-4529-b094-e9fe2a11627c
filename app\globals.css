@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 252 100% 99%;
    --foreground: 230 15% 15%;
    --card: 255 100% 98%;
    --card-foreground: 230 15% 15%;
    --popover: 255 100% 98%;
    --popover-foreground: 230 15% 15%;
    --primary: 200 100% 85%;
    --primary-foreground: 200 50% 25%;
    --secondary: 280 30% 92%;
    --secondary-foreground: 280 20% 25%;
    --muted: 260 20% 95%;
    --muted-foreground: 230 15% 45%;
    --accent: 320 40% 90%;
    --accent-foreground: 320 30% 25%;
    --destructive: 0 70% 88%;
    --destructive-foreground: 0 60% 30%;
    --border: 240 20% 90%;
    --input: 240 20% 90%;
    --ring: 200 100% 75%;
    --chart-1: 200 100% 80%;
    --chart-2: 280 60% 85%;
    --chart-3: 320 70% 85%;
    --chart-4: 60 80% 85%;
    --chart-5: 120 60% 85%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 230 25% 8%;
    --foreground: 210 40% 95%;
    --card: 230 25% 10%;
    --card-foreground: 210 40% 95%;
    --popover: 230 25% 10%;
    --popover-foreground: 210 40% 95%;
    --primary: 200 80% 70%;
    --primary-foreground: 200 20% 15%;
    --secondary: 280 20% 15%;
    --secondary-foreground: 280 40% 85%;
    --muted: 260 15% 15%;
    --muted-foreground: 230 20% 65%;
    --accent: 320 30% 20%;
    --accent-foreground: 320 60% 85%;
    --destructive: 0 50% 70%;
    --destructive-foreground: 0 20% 15%;
    --border: 240 15% 20%;
    --input: 240 15% 20%;
    --ring: 200 80% 60%;
    --chart-1: 200 80% 60%;
    --chart-2: 280 50% 65%;
    --chart-3: 320 60% 65%;
    --chart-4: 60 70% 65%;
    --chart-5: 120 50% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(260 30% 97%) 100%);
    min-height: 100vh;
  }
}

/* Custom pastel gradients */
.gradient-bg-primary {
  background: linear-gradient(135deg, hsl(200 100% 90%) 0%, hsl(220 80% 92%) 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, hsl(280 40% 92%) 0%, hsl(300 50% 94%) 100%);
}

.gradient-bg-accent {
  background: linear-gradient(135deg, hsl(320 50% 92%) 0%, hsl(340 60% 94%) 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, hsl(120 60% 90%) 0%, hsl(140 50% 92%) 100%);
}

.gradient-bg-warning {
  background: linear-gradient(135deg, hsl(60 80% 90%) 0%, hsl(40 70% 92%) 100%);
}

.gradient-bg-danger {
  background: linear-gradient(135deg, hsl(0 70% 92%) 0%, hsl(20 60% 94%) 100%);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
