"use client"

import { memo } from "react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

// Generic loading skeleton for cards
export const CardSkeleton = memo(function CardSkeleton({
  className,
  showHeader = true,
  headerHeight = 'h-6',
  contentRows = 3,
  rowHeight = 'h-4',
  children
}: {
  className?: string
  showHeader?: boolean
  headerHeight?: string
  contentRows?: number
  rowHeight?: string
  children?: React.ReactNode
}) {
  return (
    <Card className={cn("border-0 shadow-lg bg-white/60 backdrop-blur-sm", className)}>
      {showHeader && (
        <CardHeader className="space-y-2">
          <Skeleton className={cn("w-3/4", headerHeight)} />
          <Skeleton className="h-3 w-1/2" />
        </CardHeader>
      )}
      <CardContent className="space-y-2">
        {children || Array.from({ length: contentRows }).map((_, i) => (
          <Skeleton key={i} className={cn("w-full", rowHeight)} />
        ))}
      </CardContent>
    </Card>
  )
})

// Table loading skeleton
export const TableSkeleton = memo(function TableSkeleton({
  columns = 5,
  rows = 5,
  showHeader = true,
  className
}: {
  columns?: number
  rows?: number
  showHeader?: boolean
  className?: string
}) {
  return (
    <div className={cn("border rounded-lg overflow-hidden", className)}>
      {showHeader && (
        <div className="bg-gray-50 p-4 border-b">
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, i) => (
              <Skeleton key={i} className="h-4 w-full" />
            ))}
          </div>
        </div>
      )}
      
      <div className="divide-y">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton key={colIndex} className="h-4 w-full" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

// Stats grid loading skeleton
export const StatsGridSkeleton = memo(function StatsGridSkeleton({
  cards = 4,
  columns = 4,
  className
}: {
  cards?: number
  columns?: number
  className?: string
}) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
  }

  return (
    <div className={cn("grid gap-4", gridCols[columns as keyof typeof gridCols], className)}>
      {Array.from({ length: cards }).map((_, i) => (
        <Card key={i} className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-24" />
            <div className="p-2 rounded-lg bg-gray-200 animate-pulse">
              <div className="h-4 w-4" />
            </div>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-8 w-16 mb-2" />
            <Skeleton className="h-3 w-32" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
})

// Form loading skeleton
export const FormSkeleton = memo(function FormSkeleton({
  fields = 5,
  showButtons = true,
  className
}: {
  fields?: number
  showButtons?: boolean
  className?: string
}) {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="grid gap-4">
        {Array.from({ length: fields }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
      
      {showButtons && (
        <div className="flex justify-end gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-20" />
        </div>
      )}
    </div>
  )
})

// Page loading skeleton
export const PageSkeleton = memo(function PageSkeleton({
  showHeader = true,
  showStats = true,
  showTable = true,
  className
}: {
  showHeader?: boolean
  showStats?: boolean
  showTable?: boolean
  className?: string
}) {
  return (
    <div className={cn("space-y-6", className)}>
      {showHeader && (
        <div className="flex justify-between items-center">
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
      )}
      
      {showStats && <StatsGridSkeleton />}
      
      {showTable && (
        <CardSkeleton
          showHeader={false}
          contentRows={1}
          className="p-0"
        >
          <TableSkeleton />
        </CardSkeleton>
      )}
    </div>
  )
})

// Shimmer loading effect
export const ShimmerEffect = memo(function ShimmerEffect({
  className,
  children
}: {
  className?: string
  children?: React.ReactNode
}) {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      {children}
      <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" />
    </div>
  )
})

// Inline loading spinner
export const InlineSpinner = memo(function InlineSpinner({
  size = 'sm',
  className
}: {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div className={cn("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600", sizes[size], className)} />
  )
})

// Loading overlay
export const LoadingOverlay = memo(function LoadingOverlay({
  show,
  message = "Loading...",
  className
}: {
  show: boolean
  message?: string
  className?: string
}) {
  if (!show) return null

  return (
    <div className={cn(
      "absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50",
      className
    )}>
      <div className="text-center">
        <InlineSpinner size="lg" className="mx-auto mb-2" />
        <p className="text-sm text-gray-600">{message}</p>
      </div>
    </div>
  )
})

// Pulsing dot indicator
export const PulsingDot = memo(function PulsingDot({
  color = 'blue',
  className
}: {
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'gray'
  className?: string
}) {
  const colors = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    gray: 'bg-gray-500'
  }

  return (
    <div className={cn("relative", className)}>
      <div className={cn("h-2 w-2 rounded-full", colors[color])}>
        <div className={cn("absolute inset-0 rounded-full animate-ping", colors[color])} />
      </div>
    </div>
  )
})

// Progress bar
export const ProgressBar = memo(function ProgressBar({
  value,
  max = 100,
  className,
  showPercentage = false,
  color = 'blue'
}: {
  value: number
  max?: number
  className?: string
  showPercentage?: boolean
  color?: 'blue' | 'green' | 'red' | 'yellow'
}) {
  const percentage = Math.round((value / max) * 100)
  
  const colors = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500'
  }

  return (
    <div className={cn("space-y-1", className)}>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={cn("h-2 rounded-full transition-all duration-300", colors[color])}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showPercentage && (
        <div className="text-xs text-gray-600 text-right">
          {percentage}%
        </div>
      )}
    </div>
  )
})

// Add shimmer animation to global CSS
const shimmerAnimation = `
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
`

// Export animation for use in global CSS
export { shimmerAnimation }