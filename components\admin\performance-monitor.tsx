"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts'
import {
  Activity,
  Zap,
  Database,
  Globe,
  HardDrive,
  Wifi,
  WifiOff,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Monitor,
  Smartphone,
  Tablet
} from "lucide-react"
import { usePerformanceMonitor, useNetworkQuality, useCacheManager, useBundleSize } from "@/hooks/use-performance"

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  status: 'good' | 'warning' | 'error'
  trend?: 'up' | 'down' | 'stable'
}

interface SystemInfo {
  userAgent: string
  platform: string
  cores: number
  memory: number
  language: string
  timezone: string
  screenResolution: string
  deviceType: 'desktop' | 'tablet' | 'mobile'
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [performanceHistory, setPerformanceHistory] = useState<any[]>([])
  
  const performanceData = usePerformanceMonitor()
  const networkQuality = useNetworkQuality()
  const { clearCache, getCacheSize } = useCacheManager()
  const bundleInfo = useBundleSize()

  useEffect(() => {
    // Gather system information
    const info: SystemInfo = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      cores: navigator.hardwareConcurrency || 1,
      memory: (navigator as any).deviceMemory || 0,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      screenResolution: `${screen.width}x${screen.height}`,
      deviceType: getDeviceType()
    }
    setSystemInfo(info)

    // Start monitoring
    startMonitoring()
  }, [])

  useEffect(() => {
    // Update metrics when performance data changes
    const newMetrics: PerformanceMetric[] = [
      {
        name: 'Page Load Time',
        value: performanceData.loadTime,
        unit: 'ms',
        status: performanceData.loadTime < 3000 ? 'good' : performanceData.loadTime < 5000 ? 'warning' : 'error'
      },
      {
        name: 'Memory Usage',
        value: performanceData.memoryUsage,
        unit: 'MB',
        status: performanceData.memoryUsage < 100 ? 'good' : performanceData.memoryUsage < 200 ? 'warning' : 'error'
      },
      {
        name: 'Network RTT',
        value: networkQuality.rtt,
        unit: 'ms',
        status: networkQuality.rtt < 100 ? 'good' : networkQuality.rtt < 300 ? 'warning' : 'error'
      },
      {
        name: 'Downlink Speed',
        value: networkQuality.downlink,
        unit: 'Mbps',
        status: networkQuality.downlink > 10 ? 'good' : networkQuality.downlink > 1 ? 'warning' : 'error'
      },
      {
        name: 'Bundle Size',
        value: bundleInfo.bundleSize,
        unit: 'KB',
        status: bundleInfo.bundleSize < 1000 ? 'good' : bundleInfo.bundleSize < 2000 ? 'warning' : 'error'
      }
    ]

    setMetrics(newMetrics)
  }, [performanceData, networkQuality, bundleInfo])

  const getDeviceType = (): 'desktop' | 'tablet' | 'mobile' => {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }

  const startMonitoring = () => {
    setIsMonitoring(true)
    
    const interval = setInterval(() => {
      const timestamp = new Date().toISOString()
      
      // Collect performance data
      const performanceEntry = {
        timestamp,
        loadTime: performanceData.loadTime,
        memoryUsage: performanceData.memoryUsage,
        isOnline: performanceData.isOnline,
        networkQuality: networkQuality.networkQuality,
        rtt: networkQuality.rtt,
        downlink: networkQuality.downlink
      }
      
      setPerformanceHistory(prev => [...prev.slice(-20), performanceEntry])
    }, 5000)

    return () => {
      clearInterval(interval)
      setIsMonitoring(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'error': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'mobile': return <Smartphone className="h-4 w-4" />
      case 'tablet': return <Tablet className="h-4 w-4" />
      default: return <Monitor className="h-4 w-4" />
    }
  }

  const handleClearCache = async () => {
    try {
      await clearCache()
      alert('Cache cleared successfully')
    } catch (error) {
      console.error('Failed to clear cache:', error)
      alert('Failed to clear cache')
    }
  }

  const colors = ['#2563eb', '#dc2626', '#16a34a', '#ca8a04', '#9333ea']

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Performance Monitor</h2>
          <p className="text-gray-600">Real-time application performance metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isMonitoring ? "default" : "outline"}>
            {isMonitoring ? 'Monitoring' : 'Stopped'}
          </Badge>
          <Button variant="outline" onClick={handleClearCache}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="network">Network</TabsTrigger>
          <TabsTrigger value="system">System Info</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {metrics.map((metric) => (
              <Card key={metric.name}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
                  {getStatusIcon(metric.status)}
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">
                      {metric.value.toLocaleString()}
                    </span>
                    <span className="text-sm text-gray-500">{metric.unit}</span>
                  </div>
                  <div className="mt-2">
                    <Progress 
                      value={metric.status === 'good' ? 100 : metric.status === 'warning' ? 60 : 30} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Trend</CardTitle>
                <CardDescription>Memory usage and load times over time</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={performanceHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="timestamp" 
                      tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                    />
                    <YAxis />
                    <Tooltip 
                      labelFormatter={(value) => new Date(value).toLocaleString()}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="memoryUsage" 
                      stroke="#2563eb" 
                      name="Memory (MB)"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="loadTime" 
                      stroke="#dc2626" 
                      name="Load Time (ms)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Network Quality</CardTitle>
                <CardDescription>Real-time connection status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {performanceData.isOnline ? (
                        <Wifi className="h-4 w-4 text-green-600" />
                      ) : (
                        <WifiOff className="h-4 w-4 text-red-600" />
                      )}
                      <span>Connection Status</span>
                    </div>
                    <Badge variant={performanceData.isOnline ? "default" : "destructive"}>
                      {performanceData.isOnline ? 'Online' : 'Offline'}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span>Connection Speed</span>
                    <Badge variant="outline">
                      {performanceData.connectionSpeed}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span>Network Quality</span>
                    <Badge variant={
                      networkQuality.networkQuality === 'good' ? 'default' :
                      networkQuality.networkQuality === 'fair' ? 'secondary' : 'destructive'
                    }>
                      {networkQuality.networkQuality}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>RTT</span>
                      <span>{networkQuality.rtt}ms</span>
                    </div>
                    <Progress value={Math.min(100, (300 - networkQuality.rtt) / 3)} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Downlink</span>
                      <span>{networkQuality.downlink} Mbps</span>
                    </div>
                    <Progress value={Math.min(100, networkQuality.downlink * 10)} />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Bundle Analysis</CardTitle>
                <CardDescription>JavaScript bundle size breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Total Bundle Size</span>
                    <Badge variant="outline">
                      {bundleInfo.bundleSize.toFixed(1)} KB
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bundle Size</span>
                      <span>{bundleInfo.bundleSize.toFixed(1)} KB</span>
                    </div>
                    <Progress value={Math.min(100, bundleInfo.bundleSize / 20)} />
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Chunk Sizes</h4>
                    {Object.entries(bundleInfo.chunkSizes).map(([chunk, size]) => (
                      <div key={chunk} className="flex justify-between text-xs">
                        <span className="truncate">{chunk}</span>
                        <span>{size.toFixed(1)} KB</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.map((metric) => (
                    <div key={metric.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(metric.status)}
                        <span className="text-sm">{metric.name}</span>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${getStatusColor(metric.status)}`}>
                          {metric.value.toLocaleString()} {metric.unit}
                        </div>
                        {metric.trend && (
                          <div className="flex items-center gap-1 text-xs">
                            {metric.trend === 'up' ? (
                              <TrendingUp className="h-3 w-3 text-green-500" />
                            ) : metric.trend === 'down' ? (
                              <TrendingDown className="h-3 w-3 text-red-500" />
                            ) : (
                              <div className="h-3 w-3" />
                            )}
                            <span className="text-gray-500">{metric.trend}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="network" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Network Performance</CardTitle>
              <CardDescription>Real-time network metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={performanceHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="rtt" 
                    stroke="#2563eb" 
                    fill="#2563eb" 
                    fillOpacity={0.6}
                    name="RTT (ms)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          {systemInfo && (
            <Card>
              <CardHeader>
                <CardTitle>System Information</CardTitle>
                <CardDescription>Device and browser details</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Device Type</span>
                      <div className="flex items-center gap-2">
                        {getDeviceIcon(systemInfo.deviceType)}
                        <span className="text-sm font-medium">{systemInfo.deviceType}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">CPU Cores</span>
                      <span className="text-sm font-medium">{systemInfo.cores}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Memory</span>
                      <span className="text-sm font-medium">
                        {systemInfo.memory ? `${systemInfo.memory} GB` : 'Unknown'}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Platform</span>
                      <span className="text-sm font-medium">{systemInfo.platform}</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Screen Resolution</span>
                      <span className="text-sm font-medium">{systemInfo.screenResolution}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Language</span>
                      <span className="text-sm font-medium">{systemInfo.language}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Timezone</span>
                      <span className="text-sm font-medium">{systemInfo.timezone}</span>
                    </div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t">
                  <h4 className="font-medium text-sm mb-2">User Agent</h4>
                  <p className="text-xs text-gray-600 break-all">{systemInfo.userAgent}</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}