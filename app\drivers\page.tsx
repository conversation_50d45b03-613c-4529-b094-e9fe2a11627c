"use client"

import { useState, useEffect } from "react"
import { MainLayout } from "@/components/layout/main-layout"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { DriverForm } from "@/components/forms/driver-form"
import { useAsyncOperation } from "@/hooks/use-async-operation"
import { supabaseApiClient, type Driver } from "@/lib/supabase-api-client"
import { Plus, Search, Filter, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Users, User } from "lucide-react"

export default function DriversPage() {
  const [drivers, setDrivers] = useState<Driver[]>([])
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null)
  const [deletingDriver, setDeletingDriver] = useState<Driver | null>(null)

  const { execute: loadDrivers, isLoading: loadingDrivers } = useAsyncOperation({
    onSuccess: (data) => setDrivers(data || []),
  })

  const { execute: deleteDriver, isLoading: deletingDriverLoading } = useAsyncOperation({
    successMessage: "Driver deleted successfully",
    onSuccess: () => {
      setDeletingDriver(null)
      loadDriversData()
    },
  })

  const loadDriversData = () => {
    loadDrivers(() => supabaseApiClient.getDrivers())
  }

  useEffect(() => {
    loadDriversData()
  }, [])

  useEffect(() => {
    let filtered = drivers

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (driver) =>
          driver.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          driver.license_number?.toString().toLowerCase().includes(searchTerm.toLowerCase()) ||
          driver.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          driver.phone?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((driver) => driver.status === statusFilter)
    }

    setFilteredDrivers(filtered)
  }, [drivers, searchTerm, statusFilter])

  const getStatusColor = (status: string) => {
    if (!status) {
      return "bg-gray-100 text-gray-800 border-gray-200"
    }
    
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200"
      case "available":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getLicenseStatus = (expiryDate: string) => {
    if (!expiryDate) {
      return { status: "Unknown", color: "text-gray-600", days: 0, icon: AlertTriangle }
    }
    
    const expiry = new Date(expiryDate)
    const today = new Date()
    
    // Check if the date is invalid
    if (isNaN(expiry.getTime())) {
      return { status: "Invalid Date", color: "text-gray-600", days: 0, icon: AlertTriangle }
    }
    
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 3600 * 24))

    if (daysUntilExpiry < 0) {
      return { status: "Expired", color: "text-red-600", days: Math.abs(daysUntilExpiry), icon: AlertTriangle }
    } else if (daysUntilExpiry <= 30) {
      return { status: "Expiring Soon", color: "text-yellow-600", days: daysUntilExpiry, icon: AlertTriangle }
    } else {
      return { status: "Valid", color: "text-green-600", days: daysUntilExpiry, icon: CheckCircle }
    }
  }

  const handleEdit = (driver: Driver) => {
    setEditingDriver(driver)
  }

  const handleDelete = (driver: Driver) => {
    setDeletingDriver(driver)
  }

  const confirmDelete = () => {
    if (deletingDriver) {
      deleteDriver(() => supabaseApiClient.deleteDriver(deletingDriver.code.toString()))
    }
  }

  const handleFormSuccess = () => {
    setIsAddDialogOpen(false)
    setEditingDriver(null)
    loadDriversData()
  }

  const activeDrivers = drivers.filter((d) => d.status === "Active").length
  const availableDrivers = drivers.filter((d) => d.status === "Available").length
  const expiringLicenses = drivers.filter((d) => {
    const expiry = new Date(d.license_expiry)
    const today = new Date()
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 3600 * 24))
    return daysUntilExpiry <= 30 && daysUntilExpiry >= 0
  }).length

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-8">
          {/* Header */}
          <div className="gradient-bg-primary p-6 rounded-2xl flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Driver Management</h1>
              <p className="text-gray-600 mt-2">Manage drivers, licenses, and vehicle assignments</p>
            </div>
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="gradient-bg-accent text-purple-700 hover:opacity-90 shadow-lg"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Driver
            </Button>
          </div>

          {/* Statistics Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Total Drivers</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-primary">
                  <Users className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{drivers.length}</div>
                <p className="text-xs text-gray-600 mt-1">Registered drivers</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Active Drivers</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-success">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{activeDrivers}</div>
                <p className="text-xs text-gray-600 mt-1">Currently assigned</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">Available</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-secondary">
                  <User className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{availableDrivers}</div>
                <p className="text-xs text-gray-600 mt-1">Ready for assignment</p>
              </CardContent>
            </Card>

            <Card className="card-hover border-0 shadow-lg bg-white/60 backdrop-blur-sm">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-700">License Expiring</CardTitle>
                <div className="p-2 rounded-lg gradient-bg-warning">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-800">{expiringLicenses}</div>
                <p className="text-xs text-gray-600 mt-1">Within 30 days</p>
              </CardContent>
            </Card>
          </div>

          {/* Drivers Table */}
          <Card className="border-0 shadow-lg bg-white/60 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-gray-800">Fleet Drivers</CardTitle>
              <CardDescription className="text-gray-600">
                Manage driver information and vehicle assignments
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search drivers by name, license, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-blue-400 focus:ring-blue-400"
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-md focus:border-blue-400 focus:ring-blue-400 bg-white"
                  >
                    <option value="all">All Status</option>
                    <option value="Active">Active</option>
                    <option value="Available">Available</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                  <Button variant="outline" className="border-gray-200 hover:bg-gray-50 bg-transparent">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>

              {/* Loading State */}
              {loadingDrivers ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading drivers...</span>
                </div>
              ) : (
                /* Drivers Table */
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className="text-gray-700">Name</TableHead>
                        <TableHead className="text-gray-700">License Number</TableHead>
                        <TableHead className="text-gray-700">License Status</TableHead>
                        <TableHead className="text-gray-700">Contact</TableHead>
                        <TableHead className="text-gray-700">Vehicle</TableHead>
                        <TableHead className="text-gray-700">Status</TableHead>
                        <TableHead className="text-gray-700">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredDrivers.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                            {searchTerm || statusFilter !== "all"
                              ? "No drivers found matching your criteria"
                              : "No drivers added yet"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredDrivers.map((driver) => {
                          const licenseStatus = getLicenseStatus(driver.license_expiry)
                          const LicenseIcon = licenseStatus.icon

                          return (
                            <TableRow key={driver.code} className="border-gray-100 hover:bg-blue-50/30">
                              <TableCell className="font-medium text-gray-800">{driver.full_name || "N/A"}</TableCell>
                              <TableCell className="text-gray-700">{driver.license_number || "N/A"}</TableCell>
                              <TableCell>
                                <div className="flex items-center">
                                  <LicenseIcon className={`h-4 w-4 mr-2 ${licenseStatus.color}`} />
                                  <div>
                                    <span className={`text-sm font-medium ${licenseStatus.color}`}>
                                      {licenseStatus.status}
                                    </span>
                                    {licenseStatus.status !== "Expired" && (
                                      <div className="text-xs text-gray-500">{licenseStatus.days} days</div>
                                    )}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="text-sm">
                                  <div className="text-gray-800">{driver.phone || "N/A"}</div>
                                  <div className="text-gray-500">{driver.email || "N/A"}</div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {driver.assigned_vehicle_id ? (
                                  <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                                    {driver.assigned_vehicle_id}
                                  </Badge>
                                ) : (
                                  <span className="text-gray-500 text-sm">Unassigned</span>
                                )}
                              </TableCell>
                              <TableCell>
                                <Badge className={getStatusColor(driver.status)}>{driver.status || "Unknown"}</Badge>
                              </TableCell>
                              <TableCell>
                                <div className="flex space-x-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleEdit(driver)}
                                    className="hover:bg-blue-100 hover:text-blue-700"
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDelete(driver)}
                                    className="hover:bg-red-100 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add Driver Dialog */}
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Add New Driver</DialogTitle>
                <DialogDescription className="text-gray-600">
                  Add a new driver to your fleet management system.
                </DialogDescription>
              </DialogHeader>
              <DriverForm onSuccess={handleFormSuccess} onCancel={() => setIsAddDialogOpen(false)} />
            </DialogContent>
          </Dialog>

          {/* Edit Driver Dialog */}
          <Dialog open={!!editingDriver} onOpenChange={() => setEditingDriver(null)}>
            <DialogContent className="sm:max-w-[600px] border-0 shadow-2xl bg-white/90 backdrop-blur-lg">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Edit Driver</DialogTitle>
                <DialogDescription className="text-gray-600">Update the driver information below.</DialogDescription>
              </DialogHeader>
              {editingDriver && (
                <DriverForm
                  driver={editingDriver}
                  onSuccess={handleFormSuccess}
                  onCancel={() => setEditingDriver(null)}
                />
              )}
            </DialogContent>
          </Dialog>

          {/* Delete Confirmation Dialog */}
          <ConfirmationDialog
            open={!!deletingDriver}
            onOpenChange={() => setDeletingDriver(null)}
            title="Delete Driver"
            description={`Are you sure you want to delete driver "${deletingDriver?.full_name}"? This action cannot be undone.`}
            confirmText="Delete Driver"
            onConfirm={confirmDelete}
            variant="destructive"
            loading={deletingDriverLoading}
          />
        </div>
      </MainLayout>
    </ProtectedRoute>
  )
}
