"use client"

import React, { useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LoadingButton } from "@/components/ui/loading-button"
import { useAddVehicle, useUpdateVehicle } from "@/hooks/use-vehicles"
import { useDrivers, useAvailableDrivers } from "@/hooks/use-drivers"
import { type Vehicle } from "@/lib/supabase-api-client"

const vehicleSchema = z.object({
  plate_number: z.string().min(1, "Plate number is required"),
  make: z.string().min(1, "Make is required"),
  model: z.string().min(1, "Model is required"),
  year: z.number().min(1900, "Year must be 1900 or later").max(new Date().getFullYear() + 1, "Year cannot be in the future"),
  vin_number: z.string().optional(),
  vehicle_type: z.string().min(1, "Vehicle type is required"),
  fuel_type: z.string().min(1, "Fuel type is required"),
  current_km: z.number().min(0, "Current KM must be 0 or greater").optional(),
  branch_id: z.string().optional(),
  vehicle_status: z.string().min(1, "Vehicle status is required"),
  purchase_date: z.string().optional(),
  purchase_price: z.number().min(0, "Purchase price must be 0 or greater").optional(),
  insurance_expiry: z.string().optional(),
  registration_expiry: z.string().optional(),
})

type VehicleFormData = z.infer<typeof vehicleSchema>

interface VehicleFormProps {
  vehicle?: Vehicle
  onSuccess: () => void
}

export function VehicleForm({ vehicle, onSuccess }: VehicleFormProps) {
  const { data: drivers = [] } = useDrivers()
  const { data: availableDrivers = [] } = useAvailableDrivers()
  
  const addVehicleMutation = useAddVehicle()
  const updateVehicleMutation = useUpdateVehicle()

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch
  } = useForm<VehicleFormData>({
    resolver: zodResolver(vehicleSchema),
    defaultValues: {
      plate_number: "",
      make: "",
      model: "",
      year: new Date().getFullYear(),
      vin_number: "",
      vehicle_type: "",
      fuel_type: "",
      current_km: 0,
      branch_id: "",
      vehicle_status: "Active",
      purchase_date: "",
      purchase_price: 0,
      insurance_expiry: "",
      registration_expiry: "",
    }
  })

  useEffect(() => {
    if (vehicle) {
      reset({
        plate_number: vehicle.plate_number || "",
        make: vehicle.make || "",
        model: vehicle.model || "",
        year: vehicle.year || new Date().getFullYear(),
        vin_number: vehicle.vin_number || "",
        vehicle_type: vehicle.vehicle_type || "",
        fuel_type: vehicle.fuel_type || "",
        current_km: vehicle.current_km || 0,
        branch_id: vehicle.branch_id || "",
        vehicle_status: vehicle.vehicle_status || "Active",
        purchase_date: vehicle.purchase_date || "",
        purchase_price: vehicle.purchase_price || 0,
        insurance_expiry: vehicle.insurance_expiry || "",
        registration_expiry: vehicle.registration_expiry || "",
      })
    }
  }, [vehicle, reset])

  const onSubmit = async (data: VehicleFormData) => {
    try {
      const vehicleData = {
        ...data,
        purchase_date: data.purchase_date || null,
        insurance_expiry: data.insurance_expiry || null,
        registration_expiry: data.registration_expiry || null,
      }

      if (vehicle) {
        await updateVehicleMutation.mutateAsync({ id: vehicle.id, data: vehicleData })
      } else {
        await addVehicleMutation.mutateAsync(vehicleData)
      }

      reset()
      onSuccess()
    } catch (error) {
      console.error('Error saving vehicle:', error)
    }
  }

  const isLoading = isSubmitting || addVehicleMutation.isPending || updateVehicleMutation.isPending

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Plate Number */}
        <div>
          <Label htmlFor="plate_number" className="text-gray-700">
            Plate Number *
          </Label>
          <Input
            id="plate_number"
            placeholder="Enter plate number"
            {...register("plate_number")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.plate_number ? "border-red-500" : ""
            }`}
          />
          {errors.plate_number && <p className="text-red-500 text-sm mt-1">{errors.plate_number.message}</p>}
        </div>

        {/* Make */}
        <div>
          <Label htmlFor="make" className="text-gray-700">
            Make *
          </Label>
          <Input
            id="make"
            placeholder="Enter vehicle make"
            {...register("make")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.make ? "border-red-500" : ""
            }`}
          />
          {errors.make && <p className="text-red-500 text-sm mt-1">{errors.make.message}</p>}
        </div>

        {/* Model */}
        <div>
          <Label htmlFor="model" className="text-gray-700">
            Model *
          </Label>
          <Input
            id="model"
            placeholder="Enter vehicle model"
            {...register("model")}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.model ? "border-red-500" : ""
            }`}
          />
          {errors.model && <p className="text-red-500 text-sm mt-1">{errors.model.message}</p>}
        </div>

        {/* Year */}
        <div>
          <Label htmlFor="year" className="text-gray-700">
            Year *
          </Label>
          <Input
            id="year"
            type="number"
            placeholder="Enter year"
            {...register("year", { valueAsNumber: true })}
            className={`border-gray-200 focus:border-blue-400 focus:ring-blue-400 ${
              errors.year ? "border-red-500" : ""
            }`}
          />
          {errors.year && <p className="text-red-500 text-sm mt-1">{errors.year.message}</p>}
        </div>

        {/* VIN Number */}
        <div>
          <Label htmlFor="vin_number" className="text-gray-700">
            VIN Number
          </Label>
          <Input
            id="vin_number"
            placeholder="Enter VIN number"
            {...register("vin_number")}
            className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
          />
        </div>

        {/* Vehicle Type */}
        <div>
          <Label htmlFor="vehicle_type" className="text-gray-700">
            Vehicle Type *
          </Label>
          <Select value={watch("vehicle_type") || ""} onValueChange={(value) => setValue("vehicle_type", value)}>
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder="Select vehicle type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Car">Car</SelectItem>
              <SelectItem value="Truck">Truck</SelectItem>
              <SelectItem value="Van">Van</SelectItem>
              <SelectItem value="Bus">Bus</SelectItem>
              <SelectItem value="Motorcycle">Motorcycle</SelectItem>
            </SelectContent>
          </Select>
          {errors.vehicle_type && <p className="text-red-500 text-sm mt-1">{errors.vehicle_type.message}</p>}
        </div>

        {/* Fuel Type */}
        <div>
          <Label htmlFor="fuel_type" className="text-gray-700">
            Fuel Type *
          </Label>
          <Select value={watch("fuel_type") || ""} onValueChange={(value) => setValue("fuel_type", value)}>
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder="Select fuel type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Gasoline">Gasoline</SelectItem>
              <SelectItem value="Diesel">Diesel</SelectItem>
              <SelectItem value="Electric">Electric</SelectItem>
              <SelectItem value="Hybrid">Hybrid</SelectItem>
            </SelectContent>
          </Select>
          {errors.fuel_type && <p className="text-red-500 text-sm mt-1">{errors.fuel_type.message}</p>}
        </div>

        {/* Current KM */}
        <div>
          <Label htmlFor="current_km" className="text-gray-700">
            Current KM
          </Label>
          <Input
            id="current_km"
            type="number"
            placeholder="Enter current kilometers"
            {...register("current_km", { valueAsNumber: true })}
            className="border-gray-200 focus:border-blue-400 focus:ring-blue-400"
          />
        </div>

        {/* Vehicle Status */}
        <div>
          <Label htmlFor="vehicle_status" className="text-gray-700">
            Vehicle Status *
          </Label>
          <Select value={watch("vehicle_status") || ""} onValueChange={(value) => setValue("vehicle_status", value)}>
            <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Inactive">Inactive</SelectItem>
              <SelectItem value="Maintenance">Maintenance</SelectItem>
              <SelectItem value="Retired">Retired</SelectItem>
            </SelectContent>
          </Select>
          {errors.vehicle_status && <p className="text-red-500 text-sm mt-1">{errors.vehicle_status.message}</p>}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <LoadingButton
          type="submit"
          loading={isLoading}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg"
        >
          {vehicle ? "Update Vehicle" : "Add Vehicle"}
        </LoadingButton>
      </div>
    </form>
  )
}
