#!/usr/bin/env node

/**
 * Fleet Management System - Test Users Creator
 * Creates test users for authentication testing
 */

const fs = require('fs')
const path = require('path')
const https = require('https')

// Load environment variables manually
function loadEnvVars() {
  const envPath = path.join(process.cwd(), '.env.local')
  const envVars = {}
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')
    
    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=')
        const value = valueParts.join('=').trim()
        envVars[key.trim()] = value
      }
    }
  }
  
  return envVars
}

const envVars = loadEnvVars()
const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL || 'https://vjozjofhwlpskbgbgzve.supabase.co'
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZqb3pqb2Zod2xwc2tiZ2JnenZlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE0NzIxOCwiZXhwIjoyMDY4NzIzMjE4fQ.lL5Dw3NPc8HPQS6_pi5-hnDUsexj4Ao9EOXxvuIzZaU'

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Make HTTP request to Supabase
function makeSupabaseRequest(method, endpoint, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(endpoint, supabaseUrl)
    
    const options = {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      }
    }

    const req = https.request(url, options, (res) => {
      let responseData = ''
      
      res.on('data', (chunk) => {
        responseData += chunk
      })
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          try {
            const parsed = responseData ? JSON.parse(responseData) : {}
            resolve(parsed)
          } catch (error) {
            resolve(responseData)
          }
        } else {
          try {
            const parsed = JSON.parse(responseData)
            reject(new Error(`HTTP ${res.statusCode}: ${parsed.message || responseData}`))
          } catch (error) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseData}`))
          }
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Test users data
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'FleetAdmin123!',
    full_name: 'System Administrator',
    role: 'Super Admin',
    branch_id: null
  },
  {
    email: '<EMAIL>',
    password: 'FleetManager123!',
    full_name: 'Branch Manager',
    role: 'Manager',
    branch_id: '550e8400-e29b-41d4-a716-446655440001' // Cairo branch
  },
  {
    email: '<EMAIL>',
    password: 'FleetEmployee123!',
    full_name: 'Fleet Employee',
    role: 'Employee',
    branch_id: '550e8400-e29b-41d4-a716-446655440001' // Cairo branch
  }
]

// Create a user via Supabase Admin API
async function createUser(userData) {
  try {
    log(`   Creating user: ${userData.email}...`, 'blue')
    
    // Create user via Supabase Admin API
    const createUserResponse = await makeSupabaseRequest('POST', '/auth/v1/admin/users', {
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.full_name,
        role: userData.role
      }
    })

    if (createUserResponse.id) {
      log(`   ✅ User created: ${userData.email}`, 'green')
      
      // Update profile with additional data
      await makeSupabaseRequest('PATCH', `/rest/v1/profiles?id=eq.${createUserResponse.id}`, {
        full_name: userData.full_name,
        role: userData.role,
        branch_id: userData.branch_id
      })
      
      log(`   ✅ Profile updated for: ${userData.email}`, 'green')
      return true
    } else {
      log(`   ❌ Failed to create user: ${userData.email}`, 'red')
      return false
    }
  } catch (error) {
    if (error.message.includes('already registered')) {
      log(`   ⚠️  User already exists: ${userData.email}`, 'yellow')
      return true
    } else {
      log(`   ❌ Error creating user ${userData.email}: ${error.message}`, 'red')
      return false
    }
  }
}

// Main function to create test users
async function createTestUsers() {
  log('👥 Fleet Management System - Test Users Creator', 'bold')
  log('=' .repeat(60), 'blue')

  try {
    // Test connection
    log('📡 Testing Supabase connection...', 'blue')
    await makeSupabaseRequest('GET', '/rest/v1/branches?select=count&limit=1')
    log('   ✅ Connection successful', 'green')

    log('\n👤 Creating test users...', 'blue')
    
    let successCount = 0
    let totalCount = testUsers.length

    for (const userData of testUsers) {
      const success = await createUser(userData)
      if (success) successCount++
    }

    // Summary
    log('\n' + '=' .repeat(60), 'blue')
    log('📊 Test Users Creation Summary', 'bold')
    log('=' .repeat(60), 'blue')

    log(`✅ Successfully created/verified: ${successCount}/${totalCount} users`, 'green')

    if (successCount === totalCount) {
      log('\n🎉 All test users are ready!', 'green')
      log('\n📋 Login Credentials:', 'blue')
      testUsers.forEach(user => {
        log(`   ${user.role}: ${user.email} / ${user.password}`, 'yellow')
      })
      
      log('\n🚀 You can now test authentication at http://localhost:3000/login', 'green')
    } else {
      log('\n⚠️  Some users could not be created. Check the errors above.', 'yellow')
    }

    return successCount === totalCount

  } catch (error) {
    log(`\n❌ Test users creation failed: ${error.message}`, 'red')
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
      log('\n🔧 Connection troubleshooting:', 'yellow')
      log('1. Check your internet connection', 'yellow')
      log('2. Verify Supabase URL in .env.local', 'yellow')
      log('3. Ensure Supabase project is active', 'yellow')
    }
    
    throw error
  }
}

// Main execution
async function main() {
  try {
    const success = await createTestUsers()
    
    if (success) {
      log('\n🎉 Test users created successfully!', 'green')
      process.exit(0)
    } else {
      log('\n⚠️  Some issues occurred during user creation.', 'yellow')
      process.exit(1)
    }
  } catch (error) {
    log(`❌ Script failed: ${error.message}`, 'red')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = { createTestUsers }
