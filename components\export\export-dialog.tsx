"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  FileJson, 
  Settings,
  Filter,
  Calendar,
  Users,
  Car,
  Fuel,
  Wrench,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { toast } from "sonner"
import { 
  exportData,
  exportMultipleDataSets,
  formatDataForVehicleExport,
  formatDataForDriverExport,
  formatDataForMaintenanceExport,
  formatDataForFuelExport,
  generateReportSummary,
  type ExportFormat 
} from "@/lib/export-utils"

interface ExportDialogProps {
  data: {
    vehicles?: any[]
    drivers?: any[]
    maintenance?: any[]
    fuel?: any[]
  }
  defaultType?: 'vehicles' | 'drivers' | 'maintenance' | 'fuel' | 'all'
  trigger?: React.ReactNode
}

export function ExportDialog({ data, defaultType = 'vehicles', trigger }: ExportDialogProps) {
  const [open, setOpen] = useState(false)
  const [exportType, setExportType] = useState<'single' | 'multiple' | 'custom'>('single')
  const [selectedData, setSelectedData] = useState(defaultType)
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv')
  const [exportConfig, setExportConfig] = useState({
    filename: '',
    title: '',
    subtitle: '',
    includeHeaders: true,
    includeMetadata: true,
    customHeaders: [] as string[],
    dateRange: { from: '', to: '' },
    filters: {} as Record<string, any>
  })
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>(['vehicles'])
  const [isExporting, setIsExporting] = useState(false)

  const dataTypes = [
    { value: 'vehicles', label: 'Vehicles', icon: Car, count: data.vehicles?.length || 0 },
    { value: 'drivers', label: 'Drivers', icon: Users, count: data.drivers?.length || 0 },
    { value: 'maintenance', label: 'Maintenance', icon: Wrench, count: data.maintenance?.length || 0 },
    { value: 'fuel', label: 'Fuel Records', icon: Fuel, count: data.fuel?.length || 0 },
  ]

  const exportFormats = [
    { value: 'csv', label: 'CSV', icon: FileText, description: 'Comma-separated values' },
    { value: 'excel', label: 'Excel', icon: FileSpreadsheet, description: 'Excel spreadsheet' },
    { value: 'json', label: 'JSON', icon: FileJson, description: 'JSON data format' },
    { value: 'pdf', label: 'PDF', icon: FileText, description: 'PDF report' },
  ]

  const getDataForExport = (type: string) => {
    switch (type) {
      case 'vehicles':
        return data.vehicles ? formatDataForVehicleExport(data.vehicles) : []
      case 'drivers':
        return data.drivers ? formatDataForDriverExport(data.drivers) : []
      case 'maintenance':
        return data.maintenance ? formatDataForMaintenanceExport(data.maintenance) : []
      case 'fuel':
        return data.fuel ? formatDataForFuelExport(data.fuel) : []
      default:
        return []
    }
  }

  const handleSingleExport = async () => {
    setIsExporting(true)
    
    try {
      const dataToExport = getDataForExport(selectedData)
      
      if (dataToExport.length === 0) {
        toast.error('No data to export')
        return
      }

      const config = {
        filename: exportConfig.filename || selectedData,
        title: exportConfig.title || `${selectedData.charAt(0).toUpperCase() + selectedData.slice(1)} Report`,
        subtitle: exportConfig.subtitle,
        includeHeaders: exportConfig.includeHeaders,
        includeMetadata: exportConfig.includeMetadata,
        customHeaders: exportConfig.customHeaders.length > 0 ? exportConfig.customHeaders : undefined,
        filters: exportConfig.filters
      }

      await exportData(dataToExport, exportFormat, config)
      
      toast.success('Export completed successfully', {
        description: `${dataToExport.length} records exported as ${exportFormat.toUpperCase()}`
      })
      
      setOpen(false)
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Export failed', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleMultipleExport = async () => {
    setIsExporting(true)
    
    try {
      const datasets = selectedDatasets.map(type => ({
        name: type,
        data: getDataForExport(type),
        config: {
          title: `${type.charAt(0).toUpperCase() + type.slice(1)} Report`,
          subtitle: exportConfig.subtitle,
          includeHeaders: exportConfig.includeHeaders,
          includeMetadata: exportConfig.includeMetadata,
        }
      })).filter(dataset => dataset.data.length > 0)

      if (datasets.length === 0) {
        toast.error('No data to export')
        return
      }

      exportMultipleDataSets(datasets, exportFormat)
      
      const totalRecords = datasets.reduce((sum, dataset) => sum + dataset.data.length, 0)
      
      toast.success('Multiple exports completed successfully', {
        description: `${totalRecords} total records exported across ${datasets.length} datasets`
      })
      
      setOpen(false)
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Export failed', {
        description: error instanceof Error ? error.message : 'An unexpected error occurred'
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleCustomExport = async () => {
    // Implementation for custom export logic
    toast.info('Custom export functionality coming soon!')
  }

  const handleExport = () => {
    switch (exportType) {
      case 'single':
        handleSingleExport()
        break
      case 'multiple':
        handleMultipleExport()
        break
      case 'custom':
        handleCustomExport()
        break
    }
  }

  const getPreviewData = () => {
    if (exportType === 'single') {
      return getDataForExport(selectedData).slice(0, 5)
    }
    
    if (exportType === 'multiple') {
      return selectedDatasets.reduce((acc, type) => {
        const typeData = getDataForExport(type)
        acc[type] = typeData.slice(0, 3)
        return acc
      }, {} as Record<string, any[]>)
    }
    
    return {}
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Data
          </DialogTitle>
          <DialogDescription>
            Export your fleet data in various formats for analysis and reporting
          </DialogDescription>
        </DialogHeader>

        <Tabs value={exportType} onValueChange={(value) => setExportType(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="single">Single Dataset</TabsTrigger>
            <TabsTrigger value="multiple">Multiple Datasets</TabsTrigger>
            <TabsTrigger value="custom">Custom Export</TabsTrigger>
          </TabsList>

          <TabsContent value="single" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Select Data Type</CardTitle>
                  <CardDescription>Choose the type of data to export</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {dataTypes.map((type) => (
                      <div
                        key={type.value}
                        className={`flex items-center space-x-3 p-3 rounded-lg border-2 cursor-pointer transition-all ${
                          selectedData === type.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setSelectedData(type.value as any)}
                      >
                        <type.icon className="h-5 w-5 text-gray-600" />
                        <div className="flex-1">
                          <p className="font-medium">{type.label}</p>
                          <p className="text-sm text-gray-600">{type.count} records</p>
                        </div>
                        <Badge variant="outline">{type.count}</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Export Format</CardTitle>
                  <CardDescription>Choose your preferred export format</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {exportFormats.map((format) => (
                      <div
                        key={format.value}
                        className={`flex items-center space-x-3 p-3 rounded-lg border-2 cursor-pointer transition-all ${
                          exportFormat === format.value
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => setExportFormat(format.value as ExportFormat)}
                      >
                        <format.icon className="h-5 w-5 text-gray-600" />
                        <div className="flex-1">
                          <p className="font-medium">{format.label}</p>
                          <p className="text-sm text-gray-600">{format.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Export Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="filename">Filename</Label>
                    <Input
                      id="filename"
                      placeholder="Enter filename (optional)"
                      value={exportConfig.filename}
                      onChange={(e) => setExportConfig(prev => ({ ...prev, filename: e.target.value }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="title">Report Title</Label>
                    <Input
                      id="title"
                      placeholder="Enter report title (optional)"
                      value={exportConfig.title}
                      onChange={(e) => setExportConfig(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="subtitle">Subtitle</Label>
                  <Input
                    id="subtitle"
                    placeholder="Enter subtitle (optional)"
                    value={exportConfig.subtitle}
                    onChange={(e) => setExportConfig(prev => ({ ...prev, subtitle: e.target.value }))}
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeHeaders"
                      checked={exportConfig.includeHeaders}
                      onCheckedChange={(checked) => 
                        setExportConfig(prev => ({ ...prev, includeHeaders: checked as boolean }))
                      }
                    />
                    <Label htmlFor="includeHeaders">Include headers</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeMetadata"
                      checked={exportConfig.includeMetadata}
                      onCheckedChange={(checked) => 
                        setExportConfig(prev => ({ ...prev, includeMetadata: checked as boolean }))
                      }
                    />
                    <Label htmlFor="includeMetadata">Include metadata</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="multiple" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Select Datasets</CardTitle>
                <CardDescription>Choose multiple datasets to export together</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {dataTypes.map((type) => (
                    <div
                      key={type.value}
                      className="flex items-center space-x-3 p-3 rounded-lg border"
                    >
                      <Checkbox
                        id={`dataset-${type.value}`}
                        checked={selectedDatasets.includes(type.value)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedDatasets(prev => [...prev, type.value])
                          } else {
                            setSelectedDatasets(prev => prev.filter(d => d !== type.value))
                          }
                        }}
                      />
                      <type.icon className="h-5 w-5 text-gray-600" />
                      <div className="flex-1">
                        <Label htmlFor={`dataset-${type.value}`} className="font-medium">
                          {type.label}
                        </Label>
                        <p className="text-sm text-gray-600">{type.count} records</p>
                      </div>
                      <Badge variant="outline">{type.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Export Format</CardTitle>
              </CardHeader>
              <CardContent>
                <Select value={exportFormat} onValueChange={(value) => setExportFormat(value as ExportFormat)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {exportFormats.map((format) => (
                      <SelectItem key={format.value} value={format.value}>
                        <div className="flex items-center gap-2">
                          <format.icon className="h-4 w-4" />
                          {format.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="custom" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Custom Export</CardTitle>
                <CardDescription>Create custom reports with specific filters and formatting</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg bg-blue-50">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-5 w-5 text-blue-600" />
                      <p className="font-medium text-blue-800">Custom Export Coming Soon</p>
                    </div>
                    <p className="text-sm text-blue-700">
                      Advanced filtering, custom field selection, and report templates will be available in the next update.
                    </p>
                  </div>
                  
                  <div className="space-y-3">
                    <Label htmlFor="customFilters">Custom Filters (JSON)</Label>
                    <Textarea
                      id="customFilters"
                      placeholder='{"status": "Active", "type": "Car"}'
                      className="font-mono"
                      disabled
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Preview Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Preview</CardTitle>
            <CardDescription>
              Sample of data that will be exported
            </CardDescription>
          </CardHeader>
          <CardContent>
            {exportType === 'single' ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium">
                    {getDataForExport(selectedData).length} records ready for export
                  </span>
                </div>
                <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                  {JSON.stringify(getPreviewData(), null, 2).slice(0, 200)}...
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {selectedDatasets.map(type => (
                  <div key={type} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="font-medium">{type.charAt(0).toUpperCase() + type.slice(1)}</span>
                    <Badge variant="outline">{getDataForExport(type).length} records</Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        <Separator />

        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {exportType === 'single' 
              ? `${getDataForExport(selectedData).length} records will be exported`
              : `${selectedDatasets.reduce((sum, type) => sum + getDataForExport(type).length, 0)} total records across ${selectedDatasets.length} datasets`
            }
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? 'Exporting...' : 'Export Data'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}