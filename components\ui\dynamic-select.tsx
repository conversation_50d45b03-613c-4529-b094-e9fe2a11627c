"use client"

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useDropdownValues } from "@/hooks/use-dropdown-values"

interface DynamicSelectProps {
  categoryId: string
  placeholder?: string
  value?: string
  onValueChange?: (value: string) => void
  disabled?: boolean
  className?: string
}

export function DynamicSelect({
  categoryId,
  placeholder = "Select an option",
  value,
  onValueChange,
  disabled = false,
  className
}: DynamicSelectProps) {
  const { getActiveValues, isLoading } = useDropdownValues()
  
  const options = getActiveValues(categoryId)

  if (isLoading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Loading..." />
        </SelectTrigger>
      </Select>
    )
  }

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options
          .filter(option => option && option.trim() !== '')
          .map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
      </SelectContent>
    </Select>
  )
}