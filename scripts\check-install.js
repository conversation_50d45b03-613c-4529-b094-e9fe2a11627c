#!/usr/bin/env node

/**
 * Fleet Management System - Installation Checker
 * Checks if all required dependencies are installed
 */

const fs = require('fs')
const path = require('path')

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Load environment variables manually
function loadEnvVars() {
  const envPath = path.join(process.cwd(), '.env.local')
  const envVars = {}

  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')

    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=')
        const value = valueParts.join('=').trim()
        envVars[key.trim()] = value
      }
    }
  }

  return envVars
}

function checkInstallation() {
  log('🔍 Fleet Management System - Installation Check', 'bold')
  log('=' .repeat(50), 'blue')

  let allGood = true
  const envVars = loadEnvVars()

  // Check Node.js version
  log('\n📦 Checking Node.js...', 'blue')
  const nodeVersion = process.version
  log(`   Node.js version: ${nodeVersion}`, 'green')
  
  if (parseInt(nodeVersion.slice(1)) < 18) {
    log('   ⚠️  Node.js 18+ recommended', 'yellow')
  }

  // Check package.json
  log('\n📄 Checking package.json...', 'blue')
  const packageJsonPath = path.join(process.cwd(), 'package.json')
  
  if (fs.existsSync(packageJsonPath)) {
    log('   ✅ package.json found', 'green')
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const depCount = Object.keys(packageJson.dependencies || {}).length
      const devDepCount = Object.keys(packageJson.devDependencies || {}).length
      log(`   📊 ${depCount} dependencies, ${devDepCount} dev dependencies`, 'blue')
    } catch (error) {
      log('   ❌ Error reading package.json', 'red')
      allGood = false
    }
  } else {
    log('   ❌ package.json not found', 'red')
    allGood = false
  }

  // Check node_modules
  log('\n📁 Checking node_modules...', 'blue')
  const nodeModulesPath = path.join(process.cwd(), 'node_modules')
  
  if (fs.existsSync(nodeModulesPath)) {
    log('   ✅ node_modules directory exists', 'green')
    
    // Check specific important packages
    const importantPackages = [
      '@supabase/supabase-js',
      'next',
      'react',
      'typescript'
    ]
    
    for (const pkg of importantPackages) {
      const pkgPath = path.join(nodeModulesPath, pkg)
      if (fs.existsSync(pkgPath)) {
        log(`   ✅ ${pkg} installed`, 'green')
      } else {
        log(`   ❌ ${pkg} missing`, 'red')
        allGood = false
      }
    }
  } else {
    log('   ❌ node_modules directory not found', 'red')
    log('   💡 Run: npm install', 'yellow')
    allGood = false
  }

  // Check environment files
  log('\n🔧 Checking environment files...', 'blue')
  const envFiles = ['.env.local', '.env.example']
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile)
    if (fs.existsSync(envPath)) {
      log(`   ✅ ${envFile} found`, 'green')
    } else {
      log(`   ❌ ${envFile} missing`, 'red')
      if (envFile === '.env.local') {
        allGood = false
      }
    }
  }

  // Check Supabase configuration
  log('\n🔗 Checking Supabase configuration...', 'blue')
  const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY
  const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY

  if (supabaseUrl) {
    log(`   ✅ SUPABASE_URL: ${supabaseUrl.substring(0, 30)}...`, 'green')
  } else {
    log('   ❌ NEXT_PUBLIC_SUPABASE_URL missing', 'red')
    allGood = false
  }

  if (supabaseKey) {
    log(`   ✅ SUPABASE_ANON_KEY: ${supabaseKey.substring(0, 20)}...`, 'green')
  } else {
    log('   ❌ NEXT_PUBLIC_SUPABASE_ANON_KEY missing', 'red')
    allGood = false
  }

  if (supabaseServiceKey) {
    log(`   ✅ SUPABASE_SERVICE_KEY: ${supabaseServiceKey.substring(0, 20)}...`, 'green')
  } else {
    log('   ❌ SUPABASE_SERVICE_ROLE_KEY missing', 'red')
    allGood = false
  }

  // Check project structure
  log('\n📂 Checking project structure...', 'blue')
  const importantDirs = ['app', 'components', 'lib', 'scripts', 'supabase']
  
  for (const dir of importantDirs) {
    const dirPath = path.join(process.cwd(), dir)
    if (fs.existsSync(dirPath)) {
      log(`   ✅ ${dir}/ directory exists`, 'green')
    } else {
      log(`   ⚠️  ${dir}/ directory missing`, 'yellow')
    }
  }

  // Summary and recommendations
  log('\n' + '=' .repeat(50), 'blue')
  log('📋 Installation Summary', 'bold')
  log('=' .repeat(50), 'blue')

  if (allGood) {
    log('🎉 All checks passed! Your installation looks good.', 'green')
    log('\n🚀 Next steps:', 'blue')
    log('1. Apply RLS policies: npm run apply-rls', 'blue')
    log('2. Add sample data: npm run seed', 'blue')
    log('3. Start development: npm run dev', 'blue')
  } else {
    log('⚠️  Some issues found. Please follow the recommendations below.', 'yellow')
    log('\n🔧 Recommended actions:', 'blue')
    
    if (!fs.existsSync(path.join(process.cwd(), 'node_modules'))) {
      log('1. Install dependencies:', 'yellow')
      log('   npm install', 'green')
    }
    
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      log('2. Setup environment variables:', 'yellow')
      log('   Copy .env.example to .env.local', 'green')
      log('   Fill in your Supabase credentials', 'green')
    }
    
    log('3. Then run setup:', 'yellow')
    log('   node scripts/apply-seed-direct.js apply', 'green')
  }

  return allGood
}

// Quick fix suggestions
function showQuickFix() {
  log('\n🚀 Quick Fix Guide', 'bold')
  log('=' .repeat(30), 'blue')
  log('')
  log('If you\'re seeing "Cannot find module" errors:', 'yellow')
  log('')
  log('1. Install dependencies:', 'blue')
  log('   npm install', 'green')
  log('')
  log('2. If that fails, try:', 'blue')
  log('   npm cache clean --force', 'green')
  log('   rm -rf node_modules package-lock.json', 'green')
  log('   npm install', 'green')
  log('')
  log('3. For immediate seed data application:', 'blue')
  log('   node scripts/apply-seed-direct.js apply', 'green')
  log('')
}

// Main execution
function main() {
  const command = process.argv[2]

  switch (command) {
    case 'check':
      checkInstallation()
      break
    
    case 'fix':
      showQuickFix()
      break
    
    default:
      checkInstallation()
      if (!fs.existsSync(path.join(process.cwd(), 'node_modules'))) {
        showQuickFix()
      }
  }
}

if (require.main === module) {
  main()
}

module.exports = { checkInstallation }
