# 🛠️ أوامر المطور - نظام إدارة الأسطول

## 📋 الأوامر الأساسية

### تثبيت وإعداد المشروع
```bash
# تثبيت dependencies
npm install

# إعداد سريع للنظام (قاعدة البيانات + مستخدمين)
npm run setup

# فحص صحة النظام
npm run check

# تشغيل التطبيق في وضع التطوير
npm run dev
```

### بناء ونشر المشروع
```bash
# فحص الأنواع (TypeScript)
npm run type-check

# فحص جودة الكود
npm run lint

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع في وضع الإنتاج
npm run start
```

## 🗄️ إدارة قاعدة البيانات

### إعداد قاعدة البيانات
```bash
# تطبيق RLS policies
npm run apply-rls

# أو باستخدام script مباشر
node scripts/apply-rls-policies.js apply

# التحقق من حالة RLS
node scripts/apply-rls-policies.js verify
```

### إدارة البيانات التجريبية
```bash
# تطبيق بيانات تجريبية
npm run seed

# فحص حالة التثبيت
npm run install-check

# إصلاح مشاكل التثبيت
npm run fix-install

# تثبيت المكتبات المفقودة
npm run install-missing
```

## 🔧 Scripts متقدمة

### إدارة المستخدمين
```bash
# إنشاء مستخدمين تجريبيين
node scripts/apply-rls-policies.js create-users

# إعداد سريع شامل
node scripts/quick-setup.js
```

### مراقبة الأداء
```bash
# فحص شامل للنظام
node scripts/system-check.js

# تحليل حجم Bundle
npm run build:analyze

# مراقبة الأداء
npm run dev -- --turbo
```

## 📊 Supabase Commands

### الاتصال بـ Supabase
```bash
# تسجيل الدخول لـ Supabase CLI
npx supabase login

# ربط المشروع المحلي
npx supabase link --project-ref vjozjofhwlpskbgbgzve

# تحديث أنواع TypeScript
npx supabase gen types typescript --project-id vjozjofhwlpskbgbgzve > lib/database.types.ts
```

### إدارة Migrations
```bash
# إنشاء migration جديد
npx supabase migration new migration_name

# تطبيق migrations
npx supabase db push

# إعادة تعيين قاعدة البيانات المحلية
npx supabase db reset
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
npm run test

# اختبارات مع مراقبة التغييرات
npm run test:watch

# تقرير التغطية
npm run test:coverage

# اختبارات E2E
npm run test:e2e
```

### اختبار الأداء
```bash
# اختبار الأداء
npm run test:performance

# تحليل Bundle
npm run analyze

# فحص الذاكرة
node --inspect scripts/memory-check.js
```

## 🔍 التشخيص والإصلاح

### مشاكل شائعة
```bash
# مسح cache وإعادة البناء
rm -rf .next node_modules package-lock.json
npm install
npm run build

# إصلاح مشاكل TypeScript
npm run type-check

# إصلاح مشاكل ESLint
npm run lint -- --fix

# إعادة تطبيق RLS policies
npm run apply-rls
```

### فحص الاتصالات
```bash
# فحص اتصال Supabase
node -e "
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
supabase.from('profiles').select('count').then(console.log);
"

# فحص متغيرات البيئة
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"
```

## 📁 هيكل المشروع

### الملفات المهمة
```
fleet-management-system/
├── app/                    # Next.js App Router
├── components/             # React components
├── lib/                    # Utilities & Supabase client
├── hooks/                  # Custom React hooks
├── context/                # React contexts
├── scripts/                # Setup & utility scripts
├── supabase/migrations/    # Database migrations
├── docs/                   # Documentation
├── .env.local             # Environment variables
└── package.json           # Dependencies & scripts
```

### ملفات التكوين
```
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── .eslintrc.json         # ESLint configuration
└── .gitignore             # Git ignore rules
```

## 🔐 متغيرات البيئة

### المطلوبة للتطوير
```bash
NEXT_PUBLIC_SUPABASE_URL=https://vjozjofhwlpskbgbgzve.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_JWT_SECRET=NRWC5/yeFpryiZvL5y7FbZqRJ1jvWUtBKxVzygRr1Q4eVXu8ETIXkYF0IxkHxSS+EsFYKGIPvw2rjcqTQwVpEg==
```

### اختيارية للميزات الإضافية
```bash
DEBUG=true
ENABLE_REALTIME=true
FEATURE_ADVANCED_ANALYTICS=true
```

## 🚀 سير العمل للتطوير

### 1. إعداد جديد
```bash
git clone <repository>
cd fleet-management-system
npm install
cp .env.example .env.local
# تحديث .env.local بالقيم الصحيحة
npm run setup
npm run check
npm run dev
```

### 2. تطوير ميزة جديدة
```bash
git checkout -b feature/new-feature
npm run dev
# تطوير الميزة
npm run type-check
npm run lint
npm run test
git commit -m "Add new feature"
git push origin feature/new-feature
```

### 3. نشر في الإنتاج
```bash
npm run build
npm run start
# أو نشر على Vercel
vercel --prod
```

## 📚 موارد مفيدة

### الوثائق
- [Next.js Docs](https://nextjs.org/docs)
- [Supabase Docs](https://supabase.com/docs)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)
- [TypeScript Docs](https://www.typescriptlang.org/docs)

### أدوات التطوير
- [Supabase Dashboard](https://supabase.com/dashboard/project/vjozjofhwlpskbgbgzve)
- [Vercel Dashboard](https://vercel.com/dashboard)
- [React DevTools](https://react.dev/learn/react-developer-tools)

### مجتمع ودعم
- [Next.js GitHub](https://github.com/vercel/next.js)
- [Supabase GitHub](https://github.com/supabase/supabase)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/next.js)

---

**نصائح للمطورين:**
- استخدم `npm run check` بانتظام للتأكد من صحة النظام
- اتبع TypeScript types للحصول على أفضل تجربة تطوير
- استخدم React DevTools لتشخيص مشاكل الأداء
- راجع الوثائق في مجلد `docs/` للتفاصيل المتقدمة
